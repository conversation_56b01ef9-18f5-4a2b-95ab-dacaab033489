# Schémas JSON Task Master

Ce répertoire contient les schémas JSON pour valider les modèles de prompts Task Master. Ces schémas offrent un support IDE, une validation et une meilleure expérience développeur lors de la création de prompts.

## Vue d’ensemble

Le système de schéma fournit :
- **Validation structurelle** : garantit la présence de tous les champs requis et une structure JSON correcte
- **Sécurité de type** : valide les types de paramètres et les contraintes de valeurs
- **Intégration IDE** : IntelliSense et auto-complétion dans VS Code
- **Sécurité de développement** : détecte les erreurs avant l’exécution
- **Documentation** : modèles auto-documentés via les définitions de schéma

## Fichiers de schéma

### `prompt-template.schema.json` (Schéma principal)
**Version** : 1.0.0  
**But** : Schéma principal pour les fichiers de modèles de prompts Task Master

**Valide** :
- Métadonnées du modèle (id, version, description)
- Définitions des paramètres avec validation complète des types
- Variantes de prompts avec logique conditionnelle
- Références croisées entre paramètres et variables de modèle
- Conformité au versionnage sémantique
- Syntaxe de template Handlebars

**Champs obligatoires** :
- `id` : identifiant unique du modèle (kebab-case)
- `version` : version sémantique (ex : "1.0.0")
- `description` : description lisible
- `prompts.default` : variante de prompt par défaut

**Champs optionnels** :
- `metadata` : informations supplémentaires sur le modèle
- `parameters` : définitions des paramètres pour les variables du modèle
- `prompts.*` : variantes de prompt supplémentaires

### `parameter.schema.json` (Schéma de paramètre)
**Version** : 1.0.0  
**But** : Schéma réutilisable pour chaque paramètre de prompt

**Prend en charge** :
- **Validation de type** : `string`, `number`, `boolean`, `array`, `object`
- **Contraintes** : paramètres requis/facultatifs, valeurs par défaut
- **Validation de chaîne** : regex, énumérations
- **Validation numérique** : valeurs min/max, entiers
- **Validation de tableau** : type des éléments, longueur min/max
- **Validation d’objet** : définitions de propriétés et champs requis

**Propriétés de paramètre** :
```json
{
  "type": "string|number|boolean|array|object",
  "required": true|false,
  "default": "valeur correspondant au type",
  "description": "Documentation du paramètre",
  "enum": ["option1", "option2"],
  "pattern": "^regex$",
  "minimum": 0,
  "maximum": 100,
  "minLength": 1,
  "maxLength": 255,
  "items": { "type": "string" },
  "properties": { "key": { "type": "string" } }
}
```

### `variant.schema.json` (Schéma de variante)
**Version** : 1.0.0  
**But** : Schéma pour les variantes de modèles de prompt

**Valide** :
- Templates de prompt système et utilisateur
- Expressions conditionnelles pour la sélection de variante
- Variables avec la syntaxe Handlebars
- Métadonnées et descriptions de variante

**Structure de variante** :
```json
{
  "condition": "Expression JavaScript",
  "system": "Template de prompt système",
  "user": "Template de prompt utilisateur",
  "metadata": {
    "description": "Quand utiliser cette variante"
  }
}
```

## Règles de validation du schéma

### Validation de l’ID du modèle
- **Pattern** : `^[a-z][a-z0-9-]*[a-z0-9]$`
- **Format** : kebab-case, alphanumérique avec tirets
- **Exemples** : 
  - ✅ `add-task`, `parse-prd`, `analyze-complexity`
  - ❌ `AddTask`, `add_task`, `-invalid-`, `task-`

### Validation de la version
- **Pattern** : versionnage sémantique (semver)
- **Format** : `MAJOR.MINOR.PATCH`
- **Exemples** :
  - ✅ `1.0.0`, `2.1.3`, `10.0.0`
  - ❌ `1.0`, `v1.0.0`, `1.0.0-beta`

### Validation du type de paramètre
- **String** : valeurs texte avec contraintes optionnelles (pattern/enum)
- **Number** : valeurs numériques avec min/max optionnels
- **Boolean** : valeurs vrai/faux
- **Array** : listes avec validation du type d’élément
- **Object** : structures complexes avec définitions de propriétés

### Validation des variables de modèle
- **Syntaxe Handlebars** : `{{variable}}`, `{{#if condition}}`, `{{#each array}}`
- **Références de paramètres** : toutes les variables doivent avoir un paramètre correspondant
- **Accès imbriqué** : support de `{{object.property}}`
- **Variables spéciales** : `{{@index}}`, `{{@first}}`, `{{@last}}` dans les boucles

## Intégration IDE

### Configuration VS Code
Le profil VS Code configure automatiquement la validation des schémas :

```json
{
  "json.schemas": [
    {
      "fileMatch": [
        "src/prompts/**/*.json",
        ".taskmaster/prompts/**/*.json",
        "prompts/**/*.json"
      ],
      "url": "./src/prompts/schemas/prompt-template.schema.json"
    }
  ]
}
```

**Fonctionnalités** :
- **Auto-complétion** : IntelliSense pour toutes les propriétés du schéma
- **Validation en temps réel** : surlignage immédiat des erreurs
- **Documentation au survol** : descriptions des paramètres au survol
- **Messages d’erreur** : explications détaillées des erreurs

### Autres IDE
Pour d’autres environnements :

**URLs de schéma** :
- **Développement local** : `./src/prompts/schemas/prompt-template.schema.json`
- **Référence GitHub** : `https://github.com/eyaltoledano/claude-task-master/blob/main/src/prompts/schemas/prompt-template.schema.json`

**Patterns de fichiers** :
- `src/prompts/**/*.json`
- `.taskmaster/prompts/**/*.json`
- `prompts/**/*.json`

## Exemples de validation

### Exemple de modèle valide
```json
{
  "id": "example-prompt",
  "version": "1.0.0",
  "description": "Exemple de modèle de prompt avec validation complète",
  "metadata": {
    "author": "Task Master Team",
    "category": "task",
    "tags": ["example", "validation"]
  },
  "parameters": {
    "taskDescription": {
      "type": "string",
      "description": "Description de la tâche à effectuer",
      "required": true,
      "minLength": 5,
      "maxLength": 500
    },
    "priority": {
      "type": "string",
      "description": "Niveau de priorité de la tâche",
      "required": false,
      "enum": ["high", "medium", "low"],
      "default": "medium"
    },
    "maxTokens": {
      "type": "number",
      "description": "Nombre maximal de tokens pour la réponse",
      "required": false,
      "minimum": 100,
      "maximum": 4000,
      "default": 1000
    },
    "useResearch": {
      "type": "boolean",
      "description": "Inclure le contexte de recherche",
      "required": false,
      "default": false
    },
    "tags": {
      "type": "array",
      "description": "Tags pour la catégorisation",
      "required": false,
      "items": {
        "type": "string",
        "pattern": "^[a-z][a-z0-9-]*$"
      }
    }
  },
  "prompts": {
    "default": {
      "system": "Vous êtes un assistant IA utile qui crée des tâches avec une priorité {{priority}}.",
      "user": "Créer une tâche : {{taskDescription}}{{#if tags}}\nTags : {{#each tags}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}{{/if}}"
    },
    "research": {
      "condition": "useResearch === true",
      "system": "Vous êtes un assistant IA axé sur la recherche avec accès à l’information actuelle.",
      "user": "Recherchez et créez une tâche : {{taskDescription}}"
    }
  }
}
```

### Erreurs de validation courantes

**Champs obligatoires manquants** :
```json
// ❌ Erreur : champ 'id' manquant
{
  "version": "1.0.0",
  "description": "ID manquant"
}
```

**Format d’ID invalide** :
```json
// ❌ Erreur : l’ID doit être en kebab-case
{
  "id": "InvalidID_Format",
  "version": "1.0.0"
}
```

**Type de paramètre incorrect** :
```json
// ❌ Erreur : le type du paramètre ne correspond pas à l’utilisation
{
  "parameters": {
    "count": { "type": "string" }
  },
  "prompts": {
    "default": {
      "user": "Traiter {{count}} éléments" // Devrait être un nombre
    }
  }
}
```

**Syntaxe de condition invalide** :
```json
// ❌ Erreur : JavaScript invalide dans la condition
{
  "prompts": {
    "variant": {
      "condition": "useResearch = true", // Devrait être ===
      "user": "Prompt de recherche"
    }
  }
}
```

## Workflow de développement

### Création de nouveaux modèles
1. **Commencer avec le schéma** : utiliser VS Code avec la validation activée
2. **Définir la structure** : commencer par les champs requis (id, version, description)
3. **Ajouter les paramètres** : définir toutes les variables avec les bons types
4. **Créer les prompts** : écrire les prompts système et utilisateur avec les variables
5. **Tester la validation** : s’assurer que le modèle est valide
6. **Ajouter des variantes** : créer des variantes si besoin
7. **Documenter l’utilisation** : mettre à jour le README principal

### Modification de modèles existants
1. **Vérifier la version actuelle**
2. **Évaluer les changements** : cassants ou non
3. **Mettre à jour la version** : suivre le versionnage sémantique
4. **Maintenir la compatibilité** : éviter de casser les contrats de paramètres
5. **Tester à fond**
6. **Mettre à jour la documentation**

### Évolution des schémas
Lors de la mise à jour des schémas :

1. **Compatibilité ascendante** : les anciens modèles doivent rester valides
2. **Incrémenter la version** : mettre à jour `$id` et `version`
3. **Tester la migration** : valider tous les modèles existants
4. **Documenter les changements**
5. **Coordonner la publication** : synchroniser schéma et modèles

## Fonctionnalités avancées de validation

### Validation croisée
Le schéma vérifie que :
- Toutes les variables de template ont un paramètre correspondant
- Les types de paramètres correspondent à leur usage
- Les conditions de variante référencent des paramètres valides
- L’accès aux propriétés imbriquées est bien défini

### Validation conditionnelle
- **Schémas dynamiques** : règles différentes selon les valeurs de paramètres
- **Conditions de variante** : validation d’expressions JavaScript
- **Syntaxe de template** : validation Handlebars
- **Dépendances de paramètres** : paramètres requis selon d’autres paramètres

### Règles de validation personnalisées
Le schéma inclut des règles pour :
- **Versionnage sémantique**
- **Variables de template** : syntaxe Handlebars et références de paramètres
- **Expressions de condition** : validation de la syntaxe JavaScript
- **Patterns de fichiers** : conventions de nommage

## Considérations de performance

### Chargement des schémas
- **Mise en cache** : chargement unique et cache
- **Chargement paresseux** : validation à l’accès
- **Efficacité mémoire** : instances partagées

### Performance de validation
- **Validation rapide** : AJV optimisé
- **Regroupement des erreurs** : plusieurs erreurs en un passage
- **Surcharge minimale** : coût négligeable à l’exécution

### Impact en développement
- **Réactivité IDE** : validation en temps réel sans ralentissement
- **Temps de build** : validation pendant le dev, pas en prod
- **Vitesse de test** : validation rapide lors des tests

## Dépannage

### Problèmes courants

**Schéma non chargé** :
- Vérifier les chemins dans VS Code
- Vérifier l’existence et la validité des fichiers schéma
- Redémarrer VS Code si besoin

**Validation inactive** :
- Installer `ajv` et `ajv-formats`
- Vérifier la syntaxe JSON des modèles
- Vérifier les chemins des schémas

**Problèmes de performance** :
- Vérifier les références circulaires
- Vérifier la mise en cache
- Surveiller la fréquence de validation

### Débogage des erreurs

**Comprendre les messages d’erreur** :
```javascript
// Exemple de sortie d’erreur
{
  "instancePath": "/parameters/priority/type",
  "schemaPath": "#/properties/parameters/additionalProperties/properties/type/enum",
  "keyword": "enum",
  "params": { "allowedValues": ["string", "number", "boolean", "array", "object"] },
  "message": "doit être égal à l’une des valeurs autorisées"
}
```

**Erreurs fréquentes** :
- `instancePath` : où l’erreur s’est produite dans le modèle
- `schemaPath` : règle du schéma violée
- `keyword` : type de validation échouée
- `params` : contexte supplémentaire
- `message` : description lisible de l’erreur

### Obtenir de l’aide

**Ressources internes** :
- README principal : `src/prompts/README.md`
- Schémas : `src/prompts/schemas/*.json`
- Code PromptManager : `scripts/modules/prompt-manager.js`

**Ressources externes** :
- Documentation JSON Schema : https://json-schema.org/
- Librairie AJV : https://ajv.js.org/
- Syntaxe Handlebars : https://handlebarsjs.com/

## URLs et références des schémas

### Emplacements actuels
- **Développement local** : `./src/prompts/schemas/prompt-template.schema.json`
- **GitHub Blob** : `https://github.com/eyaltoledano/claude-task-master/blob/main/src/prompts/schemas/prompt-template.schema.json`
- **Schema ID** : utilisé pour les références internes et la validation

### Bonnes pratiques d’URL
- **Champ `$id`** : utiliser l’URL GitHub blob pour l’identification stable
- **Références locales** : chemins relatifs pour le dev/test
- **Outils externes** : URLs GitHub blob pour un accès stable et versionné
- **Documentation** : lier GitHub pour l’accès public au schéma 