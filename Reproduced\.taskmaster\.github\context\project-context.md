# Project Context Management for GitHub Copilot

## Overview
This document defines how to inject and manage project context for GitHub Copilot instructions and chatmodes to ensure Task-Master adaptations work effectively with project-specific information.

## Context Categories

### 1. Project Structure Context
Information about the project's organization and architecture:

```markdown
<!-- PROJECT_STRUCTURE_CONTEXT -->
**Project Type**: {PROJECT_TYPE} (e.g., web-app, mobile-app, api, library)
**Architecture**: {ARCHITECTURE} (e.g., monolith, microservices, serverless)
**Main Technologies**: {TECH_STACK}
**Project Structure**:
```
{PROJECT_TREE}
```
<!-- /PROJECT_STRUCTURE_CONTEXT -->
```

### 2. Technology Stack Context
Current technologies and frameworks in use:

```markdown
<!-- TECH_STACK_CONTEXT -->
**Frontend**: {FRONTEND_TECH} (e.g., <PERSON><PERSON>, Vue, Angular)
**Backend**: {BACKEND_TECH} (e.g., Node.js, Python, Java)
**Database**: {DATABASE_TECH} (e.g., PostgreSQL, MongoDB, Redis)
**Infrastructure**: {INFRA_TECH} (e.g., AWS, Docker, Kubernetes)
**Testing**: {TESTING_TECH} (e.g., Jest, Cypress, Playwright)
**Build Tools**: {BUILD_TECH} (e.g., Webpack, Vite, Rollup)
<!-- /TECH_STACK_CONTEXT -->
```

### 3. Existing Tasks Context
Information about current project tasks and their status:

```markdown
<!-- EXISTING_TASKS_CONTEXT -->
**Total Tasks**: {TASK_COUNT}
**Completed**: {COMPLETED_COUNT}
**In Progress**: {IN_PROGRESS_COUNT}
**Pending**: {PENDING_COUNT}

**Recent Tasks**:
{RECENT_TASKS_LIST}

**Current Dependencies**:
{DEPENDENCY_GRAPH}
<!-- /EXISTING_TASKS_CONTEXT -->
```

### 4. Code Patterns Context
Established patterns and conventions in the codebase:

```markdown
<!-- CODE_PATTERNS_CONTEXT -->
**Naming Conventions**: {NAMING_PATTERNS}
**File Organization**: {FILE_PATTERNS}
**Component Patterns**: {COMPONENT_PATTERNS}
**API Patterns**: {API_PATTERNS}
**Testing Patterns**: {TEST_PATTERNS}
**Error Handling**: {ERROR_PATTERNS}
<!-- /CODE_PATTERNS_CONTEXT -->
```

## Context Injection Methods

### Method 1: Comment-Based Injection
Inject context through code comments that GitHub Copilot can read:

```javascript
/**
 * PROJECT CONTEXT FOR GITHUB COPILOT
 * 
 * Project: Task-Master Reproduction
 * Type: Web Application
 * Stack: React + Node.js + PostgreSQL
 * Architecture: Monolithic with modular structure
 * 
 * Current Task Context:
 * - Working on user authentication system
 * - Dependencies: Database setup (completed), API structure (in progress)
 * - Priority: High
 * 
 * Code Patterns:
 * - Use functional components with hooks
 * - API calls through custom hooks
 * - Error handling with try-catch and error boundaries
 * - Testing with Jest and React Testing Library
 */
```

### Method 2: Workspace Configuration
Create `.vscode/settings.json` with context for Copilot:

```json
{
  "github.copilot.advanced": {
    "projectContext": {
      "type": "web-application",
      "technologies": ["react", "nodejs", "postgresql"],
      "patterns": {
        "components": "functional-hooks",
        "api": "rest-with-express",
        "testing": "jest-rtl"
      },
      "currentFocus": "authentication-system"
    }
  }
}
```

### Method 3: README-Based Context
Maintain project context in README files that Copilot can reference:

```markdown
# Project Context for AI Assistants

## Current Development Focus
- **Active Feature**: User Authentication System
- **Current Sprint**: Security and User Management
- **Priority Tasks**: Login, Registration, Password Reset

## Architecture Decisions
- **State Management**: Redux Toolkit for global state
- **Authentication**: JWT with refresh tokens
- **Database**: PostgreSQL with Prisma ORM
- **API Design**: RESTful with OpenAPI documentation

## Development Standards
- **Code Style**: ESLint + Prettier configuration
- **Testing**: Minimum 80% coverage, TDD approach
- **Git Workflow**: Feature branches with PR reviews
- **Documentation**: JSDoc for functions, README for modules
```

## Context Templates

### Task Creation Context Template
```markdown
<!-- TASK_CREATION_CONTEXT -->
**Project**: {PROJECT_NAME}
**Current Sprint**: {SPRINT_NAME}
**Active Features**: {ACTIVE_FEATURES}
**Technology Stack**: {TECH_STACK}
**Team Size**: {TEAM_SIZE}
**Timeline**: {PROJECT_TIMELINE}

**Existing Task Structure**:
- Foundation tasks (1-5): {FOUNDATION_STATUS}
- Feature tasks (6-20): {FEATURE_STATUS}
- Integration tasks (21+): {INTEGRATION_STATUS}

**Current Dependencies**:
{DEPENDENCY_MAP}

**Code Patterns to Follow**:
{CODE_PATTERNS}
<!-- /TASK_CREATION_CONTEXT -->
```

### Research Context Template
```markdown
<!-- RESEARCH_CONTEXT -->
**Project Type**: {PROJECT_TYPE}
**Scale**: {PROJECT_SCALE} (small/medium/large/enterprise)
**Performance Requirements**: {PERFORMANCE_REQS}
**Security Requirements**: {SECURITY_REQS}
**Compliance**: {COMPLIANCE_REQS}
**Budget Constraints**: {BUDGET_CONSTRAINTS}
**Timeline**: {TIMELINE_CONSTRAINTS}
**Team Expertise**: {TEAM_SKILLS}

**Current Technology Decisions**:
{TECH_DECISIONS}

**Research Focus Areas**:
{RESEARCH_PRIORITIES}
<!-- /RESEARCH_CONTEXT -->
```

## Context Automation Scripts

### Context Extraction Script
```javascript
// scripts/extract-context.js
const fs = require('fs');
const path = require('path');

class ProjectContextExtractor {
    extractTechStack() {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        return {
            dependencies: Object.keys(packageJson.dependencies || {}),
            devDependencies: Object.keys(packageJson.devDependencies || {}),
            scripts: packageJson.scripts
        };
    }

    extractProjectStructure() {
        // Analyze directory structure
        // Return organized project tree
    }

    extractCodePatterns() {
        // Analyze existing code for patterns
        // Return pattern documentation
    }

    generateContextFile() {
        const context = {
            techStack: this.extractTechStack(),
            structure: this.extractProjectStructure(),
            patterns: this.extractCodePatterns()
        };

        const contextMd = this.formatAsMarkdown(context);
        fs.writeFileSync('.github/context/current-context.md', contextMd);
    }
}
```

### Context Injection Helper
```javascript
// scripts/inject-context.js
class ContextInjector {
    injectIntoInstructions(instructionFile, contextData) {
        let content = fs.readFileSync(instructionFile, 'utf8');
        
        // Replace context placeholders
        Object.keys(contextData).forEach(key => {
            const placeholder = `{${key.toUpperCase()}}`;
            content = content.replace(new RegExp(placeholder, 'g'), contextData[key]);
        });

        return content;
    }

    updateAllInstructions() {
        const context = this.loadCurrentContext();
        const instructionFiles = fs.readdirSync('.github/instructions');
        
        instructionFiles.forEach(file => {
            if (file.endsWith('.instructions.md')) {
                const updated = this.injectIntoInstructions(
                    path.join('.github/instructions', file),
                    context
                );
                fs.writeFileSync(
                    path.join('.github/instructions', file),
                    updated
                );
            }
        });
    }
}
```

## Usage Guidelines

### For Task Creation
1. **Load Current Context**: Read project structure and existing tasks
2. **Inject into Instructions**: Replace placeholders with actual values
3. **Provide to Copilot**: Use context-aware instructions
4. **Update Context**: Refresh context as project evolves

### For Research Mode
1. **Gather Project Requirements**: Extract technical and business constraints
2. **Set Research Scope**: Focus on project-relevant technologies
3. **Apply Context Filters**: Filter recommendations by project needs
4. **Generate Contextual Results**: Provide project-specific guidance

### For Complexity Analysis
1. **Load Technical Context**: Current architecture and patterns
2. **Consider Team Context**: Available skills and resources
3. **Apply Project Constraints**: Timeline, budget, compliance
4. **Generate Contextual Analysis**: Complexity relative to project

## Best Practices

### Context Maintenance
- **Regular Updates**: Refresh context weekly or after major changes
- **Version Control**: Track context changes with git
- **Team Alignment**: Ensure all team members use same context
- **Documentation**: Keep context documentation current

### Context Quality
- **Accuracy**: Ensure context reflects current project state
- **Relevance**: Include only information that affects AI decisions
- **Completeness**: Cover all major project aspects
- **Clarity**: Use clear, unambiguous descriptions

### Performance Considerations
- **Context Size**: Keep context concise but comprehensive
- **Update Frequency**: Balance freshness with performance
- **Caching**: Cache context extraction results
- **Selective Loading**: Load only relevant context for each operation

## Integration with Copilot Workflow

### Development Workflow
1. **Project Setup**: Initialize context extraction
2. **Daily Development**: Use context-aware instructions
3. **Feature Development**: Update context for new features
4. **Code Review**: Validate context accuracy
5. **Sprint Planning**: Refresh context for new sprint

### Context-Aware Commands
- `@workspace /create-task-with-context <description>`
- `@workspace /research-for-project <topic>`
- `@workspace /analyze-complexity-in-context <task>`
- `@workspace /update-project-context`
