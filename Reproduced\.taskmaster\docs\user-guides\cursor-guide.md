# Cursor Task-Master Integration Guide

## Overview
This guide explains how to use the adapted Task-Master prompts with Cursor IDE, leveraging its native MCP support and advanced project context capabilities.

## Quick Start

### 1. Setup
1. Install Cursor IDE with latest version (MCP support required)
2. Copy the `.cursor` folder to your project root
3. Configure MCP Perplexity server for enhanced research
4. Restart Cursor to load the new configurations

### 2. Basic Usage
Cursor will automatically apply Task-Master capabilities through:
- Custom rules files (`.cursorrules`)
- JSON mode configurations
- Native MCP integration for real-time research
- Project context awareness

## Available Features

### Enhanced Cursor Rules
**File**: `.cursor/mcp-integration/perplexity-research.cursorrules`

**Automatic Activation**: Applied when working on research tasks
**Features**:
- Four research modes: Quick, Comprehensive, Deep Analysis, Reasoning
- Automatic MCP tool selection based on query complexity
- Project context integration with technology stack detection
- Real-time web search with current information

**Usage Examples**:
```
@research-quick TypeScript 5.3 new features
@research state management options for React e-commerce app
@research-deep microservices vs monolith for our platform
@research-reason database choice for high-traffic application
```

### Task Master JSON Mode
**File**: `.cursor/modes/task-master-mode.json`

**Activation**: Select "Task Master Mode" from Cursor's mode selector
**Sub-modes Available**:
1. **Task Creation**: Structured task creation with templates
2. **Task Expansion**: Break down complex features into subtasks
3. **Complexity Analysis**: Multi-dimensional complexity assessment
4. **Research**: Enhanced research with MCP integration
5. **Task Update**: Modify and refine existing tasks

#### Task Creation Mode
```json
{
  "mode": "task-creation",
  "input": {
    "title": "Implement user authentication",
    "description": "OAuth 2.0 with Google and GitHub providers",
    "constraints": ["React frontend", "Node.js backend", "PostgreSQL database"]
  }
}
```

**Output**: Structured task with acceptance criteria, implementation steps, and testing strategy

#### Task Expansion Mode
```json
{
  "mode": "task-expansion",
  "input": {
    "task": "Real-time chat feature",
    "complexity": "high",
    "timeline": "3 months"
  }
}
```

**Output**: Detailed subtasks with dependencies, implementation guidance, and testing approaches

#### Research Mode with MCP
```json
{
  "mode": "research",
  "input": {
    "query": "React state management solutions",
    "detailLevel": "deep",
    "researchType": "comparison",
    "focusAreas": ["performance", "scalability", "developer-experience"]
  }
}
```

**Output**: Comprehensive research with real-time data, citations, and project-specific recommendations

## MCP Perplexity Integration

### Native MCP Support
Cursor's native MCP integration provides seamless access to Perplexity tools:
- **perplexity_ask**: Quick queries and immediate answers
- **perplexity_research**: Comprehensive analysis with multiple sources
- **perplexity_reason**: Advanced reasoning and problem-solving

### Research Workflows

#### Quick Research Workflow
1. Use `@research-quick` trigger
2. Cursor automatically selects `perplexity_ask`
3. Provides fast, current information with key points
4. Includes relevant links and sources

**Example**:
```
@research-quick Next.js 14 app router changes
```

#### Comprehensive Research Workflow
1. Use `@research` trigger (default)
2. Cursor selects `perplexity_research`
3. Provides detailed analysis with multiple sources
4. Includes implementation guidance and best practices

**Example**:
```
@research API security best practices for Node.js microservices
```

#### Deep Analysis Workflow
1. Use `@research-deep` trigger
2. Extended `perplexity_research` with comprehensive scope
3. Provides exhaustive analysis with expert opinions
4. Includes case studies and future considerations

**Example**:
```
@research-deep cloud migration strategy for legacy monolith
```

#### Reasoning Workflow
1. Use `@research-reason` trigger
2. Cursor selects `perplexity_reason`
3. Provides systematic analysis with logical reasoning
4. Includes justified recommendations and decision frameworks

**Example**:
```
@research-reason choose between PostgreSQL and MongoDB for e-commerce platform
```

### Context-Aware Research
Cursor automatically enhances MCP queries with:
- Current project structure and file context
- Technology stack from package.json and dependencies
- README.md project description and goals
- Recent git commits and development patterns
- Current file and directory context

## Advanced Features

### Project Context Integration
Cursor's advanced project understanding enhances Task-Master capabilities:

#### Automatic Technology Stack Detection
```javascript
// Cursor automatically detects:
{
  "framework": "React",
  "language": "TypeScript", 
  "backend": "Node.js",
  "database": "PostgreSQL",
  "deployment": "Docker + AWS"
}
```

#### Intelligent Context Injection
Research queries are automatically enhanced:
```
Original: "Research authentication solutions"
Enhanced: "Research authentication solutions for React TypeScript application with Node.js backend, considering OAuth 2.0, JWT tokens, and PostgreSQL user storage"
```

### Custom Rules Configuration

#### Creating Project-Specific Rules
1. Create `.cursorrules` file in project root
2. Define project-specific research patterns
3. Include team preferences and constraints
4. Specify technology stack and architecture patterns

**Example Custom Rules**:
```yaml
# Project-specific research rules
project:
  type: "e-commerce-platform"
  stack: ["React", "TypeScript", "Node.js", "PostgreSQL"]
  constraints: ["PCI compliance", "high availability", "mobile-first"]

research:
  defaultMode: "comprehensive"
  focusAreas: ["security", "performance", "scalability"]
  excludePatterns: ["deprecated technologies", "experimental features"]

mcp:
  preferredTools: ["perplexity_research", "perplexity_reason"]
  contextEnhancement: true
  citationRequired: true
```

### Workflow Integration

#### Task Planning Workflow
1. **Research Phase**: Use MCP-enhanced research to gather current information
2. **Analysis Phase**: Apply complexity analysis with current best practices
3. **Planning Phase**: Create structured tasks with implementation guidance
4. **Execution Phase**: Break down tasks with current technology recommendations

#### Decision Support Workflow
1. **Problem Definition**: Clearly define the decision context
2. **Option Research**: Use MCP to research current alternatives
3. **Systematic Analysis**: Apply reasoning mode for logical evaluation
4. **Recommendation**: Get justified recommendations with current data

## Best Practices

### 1. Effective Research Queries
- Be specific about your project context and constraints
- Include technology stack and team expertise level
- Specify the type of information needed (overview, comparison, implementation)
- Mention timeline and resource constraints

**Good Example**:
```
@research-reason
Choose between Redux, Zustand, and Context API for state management in our React e-commerce application. 
Team has 4 developers with intermediate React experience. 
Need to support complex shopping cart, user preferences, and real-time inventory updates.
Timeline: 2 months for implementation.
```

### 2. Leveraging Project Context
- Maintain clear project documentation in README.md
- Keep package.json dependencies up to date
- Use descriptive commit messages for better context
- Organize code with clear architecture patterns

### 3. Optimizing MCP Usage
- Use appropriate research mode for your needs (quick vs deep)
- Cache frequently researched topics in project documentation
- Combine multiple research queries for comprehensive analysis
- Validate MCP recommendations against project constraints

### 4. Task Management
- Start with high-level feature descriptions
- Use task expansion for complex features
- Apply complexity analysis for effort estimation
- Include testing and deployment considerations

## Troubleshooting

### Common Issues

#### 1. MCP Integration Not Working
**Symptoms**: Research falls back to standard responses, no real-time data
**Solutions**:
- Verify Cursor version supports MCP (latest recommended)
- Check MCP server configuration: `npx @perplexity/mcp-server --port 3000`
- Validate API key: `echo $PERPLEXITY_API_KEY`
- Review Cursor MCP settings in preferences

#### 2. Task Master Mode Not Available
**Symptoms**: JSON mode not appearing in mode selector
**Solutions**:
- Ensure `.cursor/modes/task-master-mode.json` is in project root
- Validate JSON syntax with online validator
- Restart Cursor to reload configurations
- Check Cursor logs for parsing errors

#### 3. Context Not Recognized
**Symptoms**: Responses don't consider project specifics
**Solutions**:
- Verify project has clear README.md and package.json
- Use explicit context in queries when needed
- Check file and directory structure is logical
- Ensure current file context is relevant to query

#### 4. Performance Issues
**Symptoms**: Slow responses, timeouts
**Solutions**:
- Use quick research mode for immediate needs
- Break complex queries into smaller parts
- Check network connectivity to MCP server
- Monitor API rate limits and usage

### Performance Optimization

#### 1. Faster Research
- Use `@research-quick` for immediate answers
- Cache common research topics in project docs
- Use specific, focused queries
- Leverage Cursor's intelligent caching

#### 2. Better Context
- Maintain comprehensive project documentation
- Use clear naming conventions for files and functions
- Keep dependencies and documentation current
- Structure code with clear patterns

#### 3. Efficient Workflows
- Combine related research queries
- Use task expansion for systematic planning
- Apply complexity analysis early in planning
- Document decisions for future reference

## Examples and Templates

### Research Query Templates

#### Technology Comparison
```
@research-deep
Compare {TECHNOLOGY_A} vs {TECHNOLOGY_B} vs {TECHNOLOGY_C} for {PROJECT_TYPE}.

Project context:
- Technology stack: {CURRENT_STACK}
- Team size: {TEAM_SIZE}
- Timeline: {TIMELINE}
- Constraints: {CONSTRAINTS}

Focus on: performance, developer experience, community support, learning curve
```

#### Implementation Strategy
```
@research
Research implementation strategies for {FEATURE_NAME} in {TECHNOLOGY_STACK}.

Requirements:
- {REQUIREMENT_1}
- {REQUIREMENT_2}
- {REQUIREMENT_3}

Please include current best practices, common pitfalls, and testing approaches.
```

#### Problem Solving
```
@research-reason
Analyze the best approach to solve {PROBLEM_DESCRIPTION}.

Context:
- Current implementation: {CURRENT_STATE}
- Constraints: {CONSTRAINTS}
- Success criteria: {SUCCESS_CRITERIA}

Consider trade-offs, implementation complexity, and long-term maintainability.
```

### Task Creation Templates

#### Feature Task
```json
{
  "mode": "task-creation",
  "input": {
    "title": "{FEATURE_NAME}",
    "description": "{DETAILED_DESCRIPTION}",
    "requirements": [
      "{REQUIREMENT_1}",
      "{REQUIREMENT_2}",
      "{REQUIREMENT_3}"
    ],
    "constraints": [
      "{CONSTRAINT_1}",
      "{CONSTRAINT_2}"
    ],
    "acceptanceCriteria": [
      "{CRITERIA_1}",
      "{CRITERIA_2}"
    ]
  }
}
```

#### Bug Fix Task
```json
{
  "mode": "task-creation",
  "input": {
    "title": "Fix: {BUG_DESCRIPTION}",
    "type": "bug-fix",
    "severity": "{HIGH|MEDIUM|LOW}",
    "reproduction": "{STEPS_TO_REPRODUCE}",
    "expectedBehavior": "{EXPECTED_BEHAVIOR}",
    "actualBehavior": "{ACTUAL_BEHAVIOR}"
  }
}
```

This guide provides comprehensive coverage of using Task-Master prompts with Cursor IDE, from basic setup to advanced MCP integration and workflow optimization.
