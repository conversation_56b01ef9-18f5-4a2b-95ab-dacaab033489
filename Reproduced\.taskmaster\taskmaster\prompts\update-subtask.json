{"id": "update-subtask", "version": "1.0.0", "description": "Ajoutez des informations à une sous-tâche en générant uniquement du contenu nouveau", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["mise à jour", "sous-tâche", "ajouter", "journalisation"]}, "parameters": {"parentTask": {"type": "object", "required": true, "description": "Le contexte de la tâche parente"}, "prevSubtask": {"type": "object", "required": false, "description": "La sous-tâche précédente si elle existe"}, "nextSubtask": {"type": "object", "required": false, "description": "La sous-tâche suivante si elle existe"}, "currentDetails": {"type": "string", "required": true, "default": "(Aucun détail existant)", "description": "Détails actuels de la sous-tâche"}, "updatePrompt": {"type": "string", "required": true, "description": "<PERSON><PERSON><PERSON> de l'utilisateur concernant l'ajout"}, "useResearch": {"type": "boolean", "default": false, "description": "Utiliser le mode recherche"}, "gatheredContext": {"type": "string", "default": "", "description": "Contexte supplémentaire du projet"}}, "prompts": {"default": {"system": "Vous êtes un assistant IA aidant à mettre à jour une sous-tâche. Vous recevrez les détails existants de la sous-tâche, le contexte de la tâche parente et des tâches sœurs, ainsi qu'une demande utilisateur.{{#if useResearch}} Vous avez accès aux meilleures pratiques actuelles et aux dernières informations techniques pour fournir des mises à jour fondées sur la recherche.{{/if}}\n\nVotre objectif : Sur la base *uniquement* de la demande de l'utilisateur et de tout le contexte fourni (y compris les détails existants si pertinents), GÉNÉREZ le nouveau texte qui doit être ajouté aux détails de la sous-tâche.\nConcentrez-vous *uniquement* sur la génération du contenu de la mise à jour.\n\nExigences de sortie :\n1. Retournez *uniquement* le texte nouvellement généré sous forme de chaîne de caractères simple. Ne retournez PAS d'objet JSON ni aucune autre donnée structurée.\n2. Votre réponse ne doit PAS inclure les détails originaux de la sous-tâche, sauf si la demande de l'utilisateur demande explicitement de reformuler, résumer ou modifier directement le texte existant.\n3. N'incluez PAS d'horodatages, de balises XML, de markdown ou tout autre formatage spécial dans votre réponse.\n4. Assurez-vous que le texte généré est concis mais complet pour la mise à jour demandée. Évitez les phrases introductives ou les explications sur ce que vous faites (par exemple, ne commencez pas par « Voici la mise à jour... »).{{#if useResearch}}\n5. Incluez des bibliothèques spécifiques, des versions et les meilleures pratiques actuelles pertinentes pour la mise en œuvre de la sous-tâche.\n6. Fournissez des recommandations techniques fondées sur la recherche et des approches éprouvées.{{/if}}", "user": "Contexte de la tâche :\n\nTâche parente : {{{json parentTask}}}\n{{#if prevSubtask}}Sous-tâche précédente : {{{json prevSubtask}}}\n{{/if}}{{#if nextSubtask}}Sous-tâche suivante : {{{json nextSubtask}}}\n{{/if}}Détails actuels de la sous-tâche (pour information uniquement) :\n{{currentDetails}}\n\nDemande utilisateur : « {{updatePrompt}} »\n\n{{#if useResearch}}Recherchez et intégrez les meilleures pratiques actuelles, les dernières versions stables et les approches éprouvées dans votre mise à jour. {{/if}}Sur la base de la demande utilisateur et de tout le contexte de la tâche (y compris les détails actuels de la sous-tâche ci-dessus), quelle est la nouvelle information ou le texte à ajouter aux détails de cette sous-tâche ? Retournez UNIQUEMENT ce nouveau texte sous forme de chaîne de caractères simple.{{#if useResearch}} Incluez des recommandations techniques spécifiques basées sur les standards actuels de l'industrie.{{/if}}\n{{#if gatheredContext}}\n\n# Contexte supplémentaire du projet\n\n{{gatheredContext}}\n{{/if}}"}}}