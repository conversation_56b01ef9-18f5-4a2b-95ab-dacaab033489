{"id": "expand-task", "version": "1.0.0", "description": "<PERSON>é<PERSON>poser une tâche en sous-tâches détaillées", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["expansion", "sous-tâches", "décomposition"]}, "parameters": {"subtaskCount": {"type": "number", "required": true, "description": "Nombre de sous-tâches à générer"}, "task": {"type": "object", "required": true, "description": "La tâche à décomposer"}, "nextSubtaskId": {"type": "number", "required": true, "description": "ID de départ pour les nouvelles sous-tâches"}, "useResearch": {"type": "boolean", "default": false, "description": "Utiliser le mode recherche"}, "expansionPrompt": {"type": "string", "required": false, "description": "Invite d'expansion issue du rapport de complexité"}, "additionalContext": {"type": "string", "required": false, "default": "", "description": "Contexte supplémentaire pour la décomposition"}, "complexityReasoningContext": {"type": "string", "required": false, "default": "", "description": "Contexte de raisonnement d'analyse de complexité"}, "gatheredContext": {"type": "string", "required": false, "default": "", "description": "Contexte de projet collecté"}}, "prompts": {"complexity-report": {"condition": "expansionPrompt", "system": "Vous êtes un assistant IA aidant à la décomposition de tâches. <PERSON><PERSON><PERSON><PERSON> {{#if (gt subtaskCount 0)}}exactement {{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches à partir de l'invite et du contexte fournis.\nRépondez UNIQUEMENT avec un objet JSON valide contenant une seule clé \"subtasks\" dont la valeur est un tableau des sous-tâches générées.\nChaque objet sous-tâche du tableau doit avoir les clés : \"id\", \"title\", \"description\", \"dependencies\", \"details\", \"status\".\nAssurez-vous que 'id' commence à {{nextSubtaskId}} et soit séquentiel.\nPour 'dependencies', utilisez le format complet d'ID de sous-tâche : \"{{task.id}}.1\", \"{{task.id}}.2\", etc. Ne référencez que les sous-tâches de cette même tâche.\nAssurez-vous que 'status' soit 'pending'.\nN'incluez aucun autre texte ou explication.", "user": "{{expansionPrompt}}{{#if additionalContext}}\n\n{{additionalContext}}{{/if}}{{#if complexityReasoningContext}}\n\n{{complexityReasoningContext}}{{/if}}{{#if gatheredContext}}\n\n# Contexte du projet\n\n{{gatheredContext}}{{/if}}"}, "research": {"condition": "useResearch === true && !expansionPrompt", "system": "Vous êtes un assistant IA qui répond UNIQUEMENT avec des objets JSON valides comme demandé. L'objet doit contenir un tableau 'subtasks'.", "user": "Analysez la tâche suivante et décomposez-la en {{#if (gt subtaskCount 0)}}exactement {{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches spécifiques en utilisant vos capacités de recherche. Attribuez des IDs séquentiels à partir de {{nextSubtaskId}}.\n\nTâche parente :\nID : {{task.id}}\nTitre : {{task.title}}\nDescription : {{task.description}}\nDétails actuels : {{#if task.details}}{{task.details}}{{else}}Aucun{{/if}}{{#if additionalContext}}\nConsidérez ce contexte : {{additionalContext}}{{/if}}{{#if complexityReasoningContext}}\nRaisonnement d'analyse de complexité : {{complexityReasoningContext}}{{/if}}{{#if gatheredContext}}\n\n# Contexte du projet\n\n{{gatheredContext}}{{/if}}\n\nCRITIQUE : Répondez UNIQUEMENT avec un objet JSON valide contenant une seule clé \"subtasks\". La valeur doit être un tableau des sous-tâches générées, respectant strictement cette structure :\n\n{\n  \"subtasks\": [\n    {\n      \"id\": <number>, // ID séquentiel à partir de {{nextSubtaskId}}\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"dependencies\": [\"<string>\"], // Utilisez des IDs complets comme [\"{{task.id}}.1\", \"{{task.id}}.2\"]. Si aucune dépendance, utilisez un tableau vide [].\n      \"details\": \"<string>\",\n      \"testStrategy\": \"<string>\" // Optionnel\n    },\n    // ... (répétez pour {{#if (gt subtaskCount 0)}}{{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches)\n  ]\n}\n\nImportant : Pour le champ 'dependencies', si une sous-tâche n'a pas de dépendances, vous DEVEZ utiliser un tableau vide, par exemple : \"dependencies\": []. N'utilisez pas null et n'omettez pas le champ.\n\nN'incluez AUCUN texte explicatif, markdown ou balises de bloc de code. Juste l'objet JSON."}, "default": {"system": "Vous êtes un assistant IA aidant à la décomposition de tâches pour le développement logiciel.\nVous devez décomposer une tâche de haut niveau en {{#if (gt subtaskCount 0)}}{{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches spécifiques pouvant être réalisées une à une.\n\nLes sous-tâches doivent :\n1. Être spécifiques et actionnables\n2. Suivre une séquence logique\n3. Chacune traiter une partie distincte de la tâche parente\n4. Inclure des indications claires sur l'approche d'implémentation\n5. Avoir des chaînes de dépendances appropriées entre sous-tâches (en utilisant les IDs complets)\n6. Couvrir collectivement tous les aspects de la tâche parente\n\nPour chaque sous-tâche, fournissez :\n- id : Entier séquentiel à partir de nextSubtaskId\n- title : Titre clair et spécifique\n- description : Description détaillée\n- dependencies : Tableau des IDs de sous-tâches prérequis au format complet comme [\"{{task.id}}.1\", \"{{task.id}}.2\"]\n- details : Détails d'implémentation, la sortie doit être une chaîne\n- testStrategy : Approche de test optionnelle\n\nRépondez UNIQUEMENT avec un objet JSON valide contenant une seule clé \"subtasks\" dont la valeur est un tableau correspondant à la structure décrite. N'incluez aucun texte explicatif, formatage markdown ou balises de bloc de code.", "user": "Décomposez cette tâche en {{#if (gt subtaskCount 0)}}exactement {{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches spécifiques :\n\nID de la tâche : {{task.id}}\nTitre : {{task.title}}\nDescription : {{task.description}}\nDétails actuels : {{#if task.details}}{{task.details}}{{else}}Aucun{{/if}}{{#if additionalContext}}\nContexte supplémentaire : {{additionalContext}}{{/if}}{{#if complexityReasoningContext}}\nRaisonnement d'analyse de complexité : {{complexityReasoningContext}}{{/if}}{{#if gatheredContext}}\n\n# Contexte du projet\n\n{{gatheredContext}}{{/if}}\n\nRetournez UNIQUEMENT l'objet JSON contenant le tableau \"subtasks\", correspondant à cette structure :\n\n{\n  \"subtasks\": [\n    {\n      \"id\": {{nextSubtaskId}}, // Premier ID de sous-tâche\n      \"title\": \"Titre spécifique de la sous-tâche\",\n      \"description\": \"Description détaillée\",\n      \"dependencies\": [], // ex : [\"{{task.id}}.1\", \"{{task.id}}.2\"] pour les dépendances. Utilisez [] si aucune dépendance\n      \"details\": \"Indications d'implémentation\",\n      \"testStrategy\": \"Approche de test optionnelle\"\n    },\n    // ... (répétez pour {{#if (gt subtaskCount 0)}}un total de {{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches avec des IDs séquentiels)\n  ]\n}"}}}