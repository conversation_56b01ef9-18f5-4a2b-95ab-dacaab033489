/**
 * Script de conversion des templates Handlebars vers différents formats
 * Compatible avec les prompts Task-Master
 */

const fs = require('fs');
const path = require('path');

class HandlebarsConverter {
    constructor() {
        this.helpers = {
            'eq': (a, b) => a === b,
            'not': (a) => !a,
            'gt': (a, b) => a > b,
            'gte': (a, b) => a >= b,
            'json': (obj) => JSON.stringify(obj, null, 2)
        };
    }

    /**
     * Convertit un template Handlebars en format simple avec placeholders
     * @param {string} template - Template Handlebars
     * @param {Object} parameters - Définition des paramètres
     * @returns {string} Template converti
     */
    convertToSimplePlaceholders(template, parameters = {}) {
        let converted = template;

        // Remplacer les variables simples {{variable}} par {VARIABLE}
        Object.keys(parameters).forEach(param => {
            const regex = new RegExp(`{{${param}}}`, 'g');
            converted = converted.replace(regex, `{${param.toUpperCase()}}`);
        });

        // Convertir les conditionnels simples
        converted = converted.replace(
            /{{#if\s+(\w+)}}(.*?){{\/if}}/gs,
            (match, condition, content) => {
                return `[IF_${condition.toUpperCase()}]\n${content}\n[/IF_${condition.toUpperCase()}]`;
            }
        );

        // Convertir les conditionnels avec helpers
        converted = converted.replace(
            /{{#if\s+\(eq\s+(\w+)\s+"([^"]+)"\)}}(.*?){{\/if}}/gs,
            (match, variable, value, content) => {
                return `[IF_${variable.toUpperCase()}_EQUALS_${value.toUpperCase()}]\n${content}\n[/IF_${variable.toUpperCase()}_EQUALS_${value.toUpperCase()}]`;
            }
        );

        // Convertir les boucles
        converted = converted.replace(
            /{{#each\s+(\w+)}}(.*?){{\/each}}/gs,
            (match, array, content) => {
                return `[FOR_EACH_${array.toUpperCase()}]\n${content}\n[/FOR_EACH_${array.toUpperCase()}]`;
            }
        );

        // Convertir les helpers numériques
        converted = converted.replace(
            /{{#if\s+\(gt\s+(\w+)\s+(\d+)\)}}(.*?){{else}}(.*?){{\/if}}/gs,
            (match, variable, value, ifContent, elseContent) => {
                return `[IF_${variable.toUpperCase()}_GT_${value}]\n${ifContent}\n[ELSE]\n${elseContent}\n[/IF_${variable.toUpperCase()}_GT_${value}]`;
            }
        );

        return converted;
    }

    /**
     * Convertit pour GitHub Copilot (instructions simples)
     * @param {Object} promptData - Données du prompt
     * @returns {Object} Instructions converties
     */
    convertForGitHubCopilot(promptData) {
        const instructions = {
            id: promptData.id,
            description: promptData.description,
            parameters: this.extractParameterDocumentation(promptData.parameters || {}),
            systemPrompt: this.convertToSimplePlaceholders(
                promptData.prompts.default.system,
                promptData.parameters
            ),
            userPrompt: this.convertToSimplePlaceholders(
                promptData.prompts.default.user,
                promptData.parameters
            ),
            variants: this.extractVariants(promptData.prompts)
        };

        return instructions;
    }

    /**
     * Convertit pour Cursor (règles .mdc)
     * @param {Object} promptData - Données du prompt
     * @returns {Object} Règles Cursor
     */
    convertForCursor(promptData) {
        const rules = {
            name: promptData.id,
            description: promptData.description,
            context: this.extractContextRequirements(promptData.parameters || {}),
            behavior: {
                system: this.convertToSimplePlaceholders(
                    promptData.prompts.default.system,
                    promptData.parameters
                ),
                user: this.convertToSimplePlaceholders(
                    promptData.prompts.default.user,
                    promptData.parameters
                )
            },
            variants: this.extractVariants(promptData.prompts),
            parameters: this.extractParameterDocumentation(promptData.parameters || {})
        };

        return rules;
    }

    /**
     * Convertit pour Claude-Code (agents complets)
     * @param {Object} promptData - Données du prompt
     * @returns {Object} Agent Claude-Code
     */
    convertForClaudeCode(promptData) {
        const agent = {
            name: promptData.id.replace('-', '_') + '_agent',
            purpose: promptData.description,
            parameters: this.extractParameterDocumentation(promptData.parameters || {}),
            behavior: {
                default: {
                    system: promptData.prompts.default.system,
                    user: promptData.prompts.default.user
                }
            },
            variants: {},
            commands: this.generateSlashCommands(promptData)
        };

        // Ajouter les variantes
        Object.keys(promptData.prompts).forEach(key => {
            if (key !== 'default') {
                agent.variants[key] = {
                    condition: promptData.prompts[key].condition || '',
                    system: promptData.prompts[key].system,
                    user: promptData.prompts[key].user
                };
            }
        });

        return agent;
    }

    /**
     * Extrait la documentation des paramètres
     * @param {Object} parameters - Paramètres du prompt
     * @returns {Array} Documentation formatée
     */
    extractParameterDocumentation(parameters) {
        return Object.keys(parameters).map(key => {
            const param = parameters[key];
            return {
                name: key,
                type: param.type,
                required: param.required || false,
                default: param.default,
                description: param.description,
                enum: param.enum,
                pattern: param.pattern
            };
        });
    }

    /**
     * Extrait les variantes du prompt
     * @param {Object} prompts - Prompts avec variantes
     * @returns {Array} Variantes formatées
     */
    extractVariants(prompts) {
        const variants = [];
        Object.keys(prompts).forEach(key => {
            if (key !== 'default') {
                variants.push({
                    name: key,
                    condition: prompts[key].condition || '',
                    description: prompts[key].metadata?.description || `Variante ${key}`
                });
            }
        });
        return variants;
    }

    /**
     * Extrait les exigences de contexte
     * @param {Object} parameters - Paramètres du prompt
     * @returns {Array} Exigences de contexte
     */
    extractContextRequirements(parameters) {
        const contextParams = ['gatheredContext', 'projectContext', 'existingTasks', 'projectInfo'];
        return Object.keys(parameters)
            .filter(key => contextParams.includes(key))
            .map(key => ({
                name: key,
                description: parameters[key].description,
                required: parameters[key].required || false
            }));
    }

    /**
     * Génère les commandes slash pour Claude-Code
     * @param {Object} promptData - Données du prompt
     * @returns {Array} Commandes slash
     */
    generateSlashCommands(promptData) {
        const commands = [{
            name: `/${promptData.id.replace('-', '_')}`,
            description: promptData.description,
            parameters: Object.keys(promptData.parameters || {})
        }];

        // Ajouter des commandes pour les variantes
        Object.keys(promptData.prompts).forEach(key => {
            if (key !== 'default') {
                commands.push({
                    name: `/${promptData.id.replace('-', '_')}_${key}`,
                    description: `${promptData.description} (variante ${key})`,
                    parameters: Object.keys(promptData.parameters || {})
                });
            }
        });

        return commands;
    }

    /**
     * Convertit tous les prompts d'un répertoire
     * @param {string} inputDir - Répertoire source
     * @param {string} outputDir - Répertoire de sortie
     * @param {string} format - Format de sortie (copilot|cursor|claude)
     */
    convertAllPrompts(inputDir, outputDir, format) {
        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.json'));
        
        files.forEach(file => {
            const filePath = path.join(inputDir, file);
            const promptData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            
            let converted;
            let extension;
            
            switch (format) {
                case 'copilot':
                    converted = this.convertForGitHubCopilot(promptData);
                    extension = '.instructions.md';
                    break;
                case 'cursor':
                    converted = this.convertForCursor(promptData);
                    extension = '.mdc';
                    break;
                case 'claude':
                    converted = this.convertForClaudeCode(promptData);
                    extension = '.md';
                    break;
                default:
                    throw new Error(`Format non supporté: ${format}`);
            }
            
            const outputFile = path.join(outputDir, file.replace('.json', extension));
            fs.writeFileSync(outputFile, JSON.stringify(converted, null, 2));
            console.log(`Converti: ${file} -> ${path.basename(outputFile)}`);
        });
    }
}

module.exports = HandlebarsConverter;

// Utilisation en ligne de commande
if (require.main === module) {
    const converter = new HandlebarsConverter();
    const args = process.argv.slice(2);
    
    if (args.length < 3) {
        console.log('Usage: node convert-handlebars.js <inputDir> <outputDir> <format>');
        console.log('Formats: copilot, cursor, claude');
        process.exit(1);
    }
    
    const [inputDir, outputDir, format] = args;
    
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    converter.convertAllPrompts(inputDir, outputDir, format);
    console.log(`Conversion terminée pour le format ${format}`);
}
