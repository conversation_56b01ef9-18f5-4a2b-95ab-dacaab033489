{"master": {"tasks": [{"id": 1, "title": "Création de l'Interface de Stockage Abstraite et de la Fabrique", "description": "Définir une classe de base abstraite `ConversationStorage` pour dicter une interface commune (`setex`, `get`) et créer une fabrique `get_storage_backend()` qui sélectionne le backend de stockage (mémoire ou SQLite) en fonction des variables d'environnement. Cette étape est cruciale pour l'architecture modulaire.", "details": "Créer un nouveau fichier, par exemple `storage/base.py`. Définir une classe `ConversationStorage(ABC)` avec des méthodes abstraites `@abstractmethod` pour `get(key)` et `setex(key, ttl, value)`. Dans `utils/conversation_memory.py`, créer une fonction `get_storage_backend()` qui lira la variable d'environnement `MCP_STORAGE_BACKEND`. Si la valeur est 'sqlite', elle importera et instanciera `SQLiteStorage`. Sinon, elle instanciera `InMemoryStorage` par défaut.", "testStrategy": "Tests unitaires pour la fabrique `get_storage_backend()`: vérifier qu'elle retourne une instance de `SQLiteStorage` lorsque `MCP_STORAGE_BACKEND` est 'sqlite' et `InMemoryStorage` sinon. Vérifier que les deux classes lèvent un `TypeError` si instanciées sans implémenter les méthodes abstraites.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Implémentation du Backend de Stockage SQLite", "description": "Implémenter la classe `SQLiteStorage` qui hérite de `ConversationStorage`. Cette classe gérera la connexion à la base de données SQLite, la création du schéma de table et les opérations CRUD de base pour les conversations.", "details": "<PERSON><PERSON><PERSON> `storage/sqlite.py`. La classe `SQLiteStorage` initialisera une connexion à la base de données via `sqlite3.connect(database_path, check_same_thread=False)`. Au démarrage, elle exécutera le `CREATE TABLE IF NOT EXISTS` avec le schéma fourni dans le PRD. Implémenter `get(thread_id)` pour récupérer `context_json` et `setex` pour insérer ou mettre à jour une conversation. Utiliser des requêtes paramétrées pour éviter les injections SQL. Le chemin de la BDD proviendra de `MCP_SQLITE_PATH`.", "testStrategy": "Tests unitaires pour `SQLiteStorage`. Utiliser une base de données en mémoire (`:memory:`) pour les tests. Valider les opérations : 1. `setex` insère correctement une nouvelle conversation. 2. `get` récupère la conversation correspondante. 3. Une mise à jour via `setex` avec le même `thread_id` met à jour l'enregistrement existant. 4. La structure de la table est créée correctement au premier lancement.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Migration de InMemoryStorage vers la Nouvelle Interface", "description": "Adapter la classe `InMemoryStorage` existante pour qu'elle se conforme à la nouvelle interface `ConversationStorage`. Cela garantit que les deux systèmes de stockage sont interchangeables sans modifier le code qui les utilise.", "details": "Modifier la classe `InMemoryStorage` existante. La faire hériter de `ConversationStorage` (défini dans la tâche 1). S'assurer que ses méthodes `get` et `setex` correspondent exactement à la signature de l'interface abstraite. La logique interne de stockage en mémoire (dictionnaire Python) et la gestion du TTL peuvent rester les mêmes.", "testStrategy": "Exécuter la suite de tests existante pour `InMemoryStorage` pour s'assurer qu'aucune régression n'a été introduite. Ajouter un test pour vérifier que `isinstance(InMemoryStorage(), ConversationStorage)` est vrai.", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "Intégration de la Configuration Dynamique du Backend", "description": "Intégrer la fabrique de backend dans l'application pour que le choix du stockage soit effectif au démarrage du serveur, basé sur les variables d'environnement.", "details": "Dans `utils/conversation_memory.py` ou là où le stockage est actuellement initialisé, remplacer l'instanciation directe de `InMemoryStorage` par un appel à `get_storage_backend()`. S'assurer que les variables d'environnement `MCP_STORAGE_BACKEND` et `MCP_SQLITE_PATH` sont lues et utilisées correctement pour configurer et instancier le backend choisi.", "testStrategy": "Tests d'intégration au démarrage. Lancer le serveur avec `MCP_STORAGE_BACKEND=sqlite` et vérifier que le fichier de base de données est créé. Lancer sans la variable et vérifier que le comportement reste en mémoire. Valider manuellement qu'une conversation persiste après un redémarrage du serveur avec la configuration SQLite.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Développement de l'Extracteur de Métadonnées", "description": "Créer un module utilitaire pour extraire les métadonnées structurées (nom de l'outil, chemin du projet, prompt initial) à partir de l'objet de contexte de conversation (JSON blob).", "details": "<PERSON><PERSON><PERSON> `utils/metadata_extractor.py`. Développer une fonction `extract_metadata(context_json)` qui prend le blob JSON en entrée. Utiliser des heuristiques pour trouver le `tool_name` (souvent dans la première interaction), le `project_path` (peut être un argument de commande ou déduit du contexte CWD), et le `initial_prompt`. G<PERSON>rer les cas où les informations sont manquantes. Pour le résumé, une simple troncature du prompt initial peut suffire pour le MVP.", "testStrategy": "Tests unitaires pour `metadata_extractor`. Créer plusieurs exemples de `context_json` (complets, incomplets, avec différents outils) et vérifier que les métadonnées extraites sont correctes. Tester les cas limites où le contexte est vide ou malformé.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 6, "title": "Intégration de l'Extraction de Métadonnées dans SQLiteStorage", "description": "Modifier la méthode `setex` de `SQLiteStorage` pour utiliser l'extracteur de métadonnées avant d'écrire en base de données, afin de peupler les colonnes indexées.", "details": "Dans `storage/sqlite.py`, au sein de la méthode `setex` de `SQLiteStorage`, appeler `extract_metadata(context_json)` avant d'exécuter la requête `INSERT` ou `UPDATE`. Mapper les résultats de l'extracteur aux colonnes correspondantes de la table `conversations` (`tool_name`, `project_path`, `initial_prompt`, `summary`).", "testStrategy": "Tests d'intégration. <PERSON><PERSON><PERSON> `SQLiteStorage.setex` avec un contexte de conversation typique. Ensuite, exécuter une requête `SELECT` directe sur la base de données pour vérifier que les colonnes de métadonnées (`tool_name`, `project_path`, etc.) ont été correctement peuplées en plus de `context_json`.", "priority": "high", "dependencies": [2, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Création de l'Outil MCP @history pour la Recherche", "description": "<PERSON><PERSON><PERSON> un nouvel outil MCP `@history` qui permet aux utilisateurs de rechercher dans leur historique de conversations en utilisant divers filtres.", "details": "Créer un nouveau fichier dans le répertoire `tools/`, par exemple `history.py`. Définir une classe `HistoryTool` qui implémente la logique de l'outil. Utiliser `argparse` pour définir les arguments de ligne de commande : `--tool`, `--project`, `--date`, `--limit`, et un argument positionnel pour la recherche textuelle. La méthode `run` de l'outil construira une requête SQL `SELECT` dynamique et paramétrée basée sur les filtres fournis, interrogera `SQLiteStorage`, et formatera la sortie.", "testStrategy": "Tests d'intégration de l'outil. Pré-remplir une base de données de test avec plusieurs conversations. Exécuter l'outil `@history` avec différents filtres et vérifier que les résultats sont corrects et bien formatés. Tester les combinaisons de filtres et la recherche par contenu dans la colonne `summary` ou `initial_prompt`.", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Formatage de la Sortie de l'Outil @history", "description": "Mettre en forme la sortie de l'outil `@history` dans un tableau clair et lisible, incluant un `continuation_id` pour permettre aux utilisateurs de reprendre facilement une conversation passée.", "details": "Dans la classe `HistoryTool`, utiliser une bibliothèque comme `rich.table` ou une simple mise en forme de texte pour afficher les résultats. Les colonnes devraient inclure ID (un alias pour `thread_id`), Date, Outil, Projet, et Résumé. Chaque ligne doit inclure une instruction claire comme `Pour reprendre: @<tool_name> --continuation_id <thread_id>`.", "testStrategy": "Tests visuels. Exécuter l'outil `@history` et vérifier que la sortie est bien alignée, lisible et contient toutes les informations requises, y compris les instructions de reprise. Valider que le `continuation_id` affiché est le bon `thread_id`.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implémentation du Worker de Maintenance en Arrière-Plan", "description": "Mettre en place un thread ou un processus en arrière-plan pour effectuer des tâches de maintenance régulières sur la base de données SQLite, comme le nettoyage des conversations expirées et l'optimisation du fichier.", "details": "Créer un module `maintenance.py`. Définir une fonction `run_maintenance_worker` qui s'exécute dans un `threading.Thread` séparé. Le worker se réveillera périodiquement (ex: toutes les heures). Il exécutera une requête `DELETE FROM conversations WHERE (strftime('%s', 'now') - strftime('%s', last_updated_at)) > ttl_seconds`. Le TTL devra être stocké ou calculé. Ensuite, il exécutera la commande `PRAGMA VACUUM;` pour nettoyer la base de données. Le thread doit être démarré au lancement du serveur principal.", "testStrategy": "Tests unitaires pour le worker. Créer une BDD de test avec des enregistrements expirés et non expirés. Lancer la fonction de nettoyage et vérifier que seuls les enregistrements expirés sont supprimés. Pour le `VACUUM`, vérifier que la taille du fichier de la base de données diminue après suppression et exécution du `VACUUM`.", "priority": "medium", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 10, "title": "Intégration Docker pour la Persistance des Données", "description": "Mettre à jour la configuration Docker pour assurer la persistance de la base de données SQLite entre les redémarrages de conteneurs et documenter les nouvelles variables d'environnement.", "details": "Modifier le `docker-compose.yml` ou les commandes `docker run`. Ajouter un montage de volume pour persister le répertoire `./data/` où le fichier SQLite sera stocké. Exemple: `volumes: - ./data:/app/data`. Mettre à jour le `Dockerfile` pour s'assurer que le répertoire `./data` existe avec les bonnes permissions. Ajouter les variables d'environnement `MCP_STORAGE_BACKEND` et `MCP_SQLITE_PATH` à la section `environment` du service.", "testStrategy": "Test de déploiement. Construire et lancer le conteneur Docker. Créer plusieurs conversations. Arr<PERSON><PERSON> et redémarrer le conteneur (`docker-compose down && docker-compose up`). Utiliser l'outil `@history` pour vérifier que les conversations créées lors de la session précédente sont toujours présentes.", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-28T10:14:41.155Z", "updated": "2025-07-28T10:14:41.155Z", "description": "Tasks for master context"}}}