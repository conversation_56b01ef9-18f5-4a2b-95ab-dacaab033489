# Claude-Code Slash Commands

## Command Configuration
```yaml
command_system: "claude-code-slash"
version: "1.0.0"
description: "Comprehensive slash command system for Task-Master functionality"
validation: "enabled"
error_handling: "graceful"
```

## Core Task Management Commands

### /create-task
**Description**: Create a new structured development task
**Syntax**: `/create-task [description] [priority?] [research?]`
**Parameters**:
- `description` (required): Natural language task description
- `priority` (optional): high|medium|low (default: medium)
- `research` (optional): true|false (default: false)

**Examples**:
```
/create-task "Implement user authentication system" high
/create-task "Add payment processing" medium research
/create-task "Fix responsive design issues"
```

**Agent Routing**: → task-creator-agent
**Output**: Structured JSON task object
**Validation**: 
- Description must be non-empty and descriptive
- Priority must be valid enum value
- Research flag triggers research-agent coordination

### /expand-task
**Description**: Break down a complex task into manageable subtasks
**Syntax**: `/expand-task [task-id] [subtask-count] [strategy?]`
**Parameters**:
- `task-id` (required): ID of the task to expand
- `subtask-count` (required): Number of subtasks to create (2-8)
- `strategy` (optional): technical-layers|feature-components|risk-based|development-phases

**Examples**:
```
/expand-task 5 4 feature-components
/expand-task 12 6 technical-layers
/expand-task 8 3
```

**Agent Routing**: → task-expansion-agent
**Output**: Array of structured subtask objects
**Validation**:
- Task ID must exist and be valid
- Subtask count must be between 2 and 8
- Strategy must be valid enum value

### /analyze-complexity
**Description**: Analyze task complexity across multiple dimensions
**Syntax**: `/analyze-complexity [task-description] [focus-areas?]`
**Parameters**:
- `task-description` (required): Task to analyze
- `focus-areas` (optional): Comma-separated list of areas to emphasize

**Examples**:
```
/analyze-complexity "Migrate to microservices architecture"
/analyze-complexity "Implement real-time chat" security,performance
/analyze-complexity "Add machine learning recommendations" scalability
```

**Agent Routing**: → complexity-analyzer-agent
**Output**: Complexity analysis with scores and recommendations
**Validation**:
- Task description must be substantive
- Focus areas must be valid categories

### /update-task
**Description**: Update an existing task with new information
**Syntax**: `/update-task [task-id] [update-description] [mode?]`
**Parameters**:
- `task-id` (required): ID of task to update
- `update-description` (required): Description of changes to apply
- `mode` (optional): replace|append (default: replace)

**Examples**:
```
/update-task 7 "Add OAuth integration support" append
/update-task 3 "Change from REST to GraphQL API"
/update-task 15 "Include mobile responsiveness" append
```

**Agent Routing**: → task-creator-agent (update mode)
**Output**: Updated task object
**Validation**:
- Task ID must exist
- Update description must be meaningful
- Mode must be valid enum value

## Research Commands (MCP-Enhanced)

### /research
**Description**: Conduct comprehensive research with real-time web search via Perplexity MCP
**Syntax**: `/research [topic] [detail-level?] [research-type?] [focus-areas?]`
**MCP Tool**: perplexity_research
**Agent**: research-agent (MCP-enhanced)
**Parameters**:
- `topic` (required): Research topic or question
- `detail-level` (optional): quick|standard|deep|reasoning (default: standard)
- `research-type` (optional): web-search|analysis|comparison|best-practices|troubleshooting|reasoning (default: web-search)
- `focus-areas` (optional): Comma-separated focus areas

**Examples**:
```
/research "React state management solutions" deep comparison performance,scalability
/research "Node.js security best practices" standard best-practices security
/research "Database migration PostgreSQL to MongoDB" reasoning troubleshooting
```

### /research-quick
**Description**: Fast web search for immediate answers
**Syntax**: `/research-quick [query]`
**MCP Tool**: perplexity_ask
**Agent**: research-agent (MCP-enhanced)
**Parameters**:
- `query` (required): Quick research question

**Examples**:
```
/research-quick "TypeScript 5.3 new features"
/research-quick "Next.js 14 app router changes"
/research-quick "React 18 concurrent features"
```

### /research-reason
**Description**: Advanced reasoning and systematic problem-solving analysis
**Syntax**: `/research-reason [problem] [constraints?]`
**MCP Tool**: perplexity_reason
**Agent**: research-agent (MCP-enhanced)
**Parameters**:
- `problem` (required): Problem or decision to analyze
- `constraints` (optional): Project constraints and requirements

**Examples**:
```
/research-reason "database choice for high-traffic application" "budget-limited,team-expertise-sql"
/research-reason "microservices vs monolith architecture" "team-size-8,timeline-6months"
/research-reason "authentication strategy for multi-tenant SaaS" "security-critical,scalability-required"
```

**Agent Routing**: → research-agent
**Output**: Comprehensive research report with recommendations
**Validation**:
- Topic must be specific and actionable
- Detail level must be valid enum value
- Focus areas must be recognized categories

### /compare
**Description**: Compare multiple technologies or approaches
**Syntax**: `/compare [option1] [option2] [criteria?]`
**Parameters**:
- `option1` (required): First option to compare
- `option2` (required): Second option to compare
- `criteria` (optional): Specific comparison criteria

**Examples**:
```
/compare "PostgreSQL" "MongoDB" "e-commerce application"
/compare "React" "Vue.js" "team learning curve"
/compare "REST" "GraphQL" "API performance"
```

**Agent Routing**: → research-agent (comparison mode)
**Output**: Detailed comparison with recommendations
**Validation**:
- Both options must be specified
- Options should be comparable technologies/approaches

### /best-practices
**Description**: Research best practices for specific technologies
**Syntax**: `/best-practices [technology] [context?]`
**Parameters**:
- `technology` (required): Technology or domain to research
- `context` (optional): Specific context or use case

**Examples**:
```
/best-practices "Node.js API development"
/best-practices "React testing" "large applications"
/best-practices "Docker deployment" "production environment"
```

**Agent Routing**: → research-agent (best-practices mode)
**Output**: Curated best practices with implementation guidance
**Validation**:
- Technology must be specific
- Context should provide meaningful constraints

## Workflow Commands

### /plan-feature
**Description**: Complete feature planning workflow
**Syntax**: `/plan-feature [feature-description] [priority?]`
**Parameters**:
- `feature-description` (required): Detailed feature description
- `priority` (optional): high|medium|low (default: medium)

**Examples**:
```
/plan-feature "Real-time notification system" high
/plan-feature "User dashboard with analytics"
/plan-feature "Multi-language support" low
```

**Agent Routing**: → research-agent → task-creator-agent → complexity-analyzer-agent
**Output**: Complete feature plan with tasks and analysis
**Validation**:
- Feature description must be comprehensive
- Priority must be valid enum value

### /review-task
**Description**: Comprehensive task review and optimization
**Syntax**: `/review-task [task-id] [focus?]`
**Parameters**:
- `task-id` (required): ID of task to review
- `focus` (optional): complexity|dependencies|implementation|testing

**Examples**:
```
/review-task 12 complexity
/review-task 8 dependencies
/review-task 5
```

**Agent Routing**: → complexity-analyzer-agent → research-agent → task-creator-agent
**Output**: Task review with optimization recommendations
**Validation**:
- Task ID must exist and be valid
- Focus area must be valid category

### /project-analysis
**Description**: Analyze current project structure and provide recommendations
**Syntax**: `/project-analysis [scope?]`
**Parameters**:
- `scope` (optional): architecture|dependencies|patterns|performance

**Examples**:
```
/project-analysis architecture
/project-analysis dependencies
/project-analysis
```

**Agent Routing**: → research-agent (project analysis mode)
**Output**: Project analysis with improvement recommendations
**Validation**:
- Scope must be valid analysis category

## Utility Commands

### /estimate-effort
**Description**: Provide effort estimates for tasks
**Syntax**: `/estimate-effort [task-description] [team-size?]`
**Parameters**:
- `task-description` (required): Task to estimate
- `team-size` (optional): small|medium|large (default: medium)

**Examples**:
```
/estimate-effort "Implement user authentication" small
/estimate-effort "Build e-commerce checkout flow"
/estimate-effort "Add real-time chat features" large
```

**Agent Routing**: → complexity-analyzer-agent (estimation mode)
**Output**: Effort estimates with confidence levels
**Validation**:
- Task description must be specific
- Team size must be valid enum value

### /validate-dependencies
**Description**: Validate and optimize task dependencies
**Syntax**: `/validate-dependencies [task-ids]`
**Parameters**:
- `task-ids` (required): Comma-separated list of task IDs

**Examples**:
```
/validate-dependencies 5,7,12,15
/validate-dependencies 3,8,9
```

**Agent Routing**: → task-expansion-agent (dependency validation mode)
**Output**: Dependency analysis with optimization suggestions
**Validation**:
- All task IDs must exist
- Must have at least 2 task IDs

### /optimize-workflow
**Description**: Optimize task workflow and sequencing
**Syntax**: `/optimize-workflow [task-ids] [constraints?]`
**Parameters**:
- `task-ids` (required): Comma-separated list of task IDs
- `constraints` (optional): timeline|resources|risk

**Examples**:
```
/optimize-workflow 5,7,12,15 timeline
/optimize-workflow 3,8,9,14 resources
/optimize-workflow 1,2,6,10
```

**Agent Routing**: → task-expansion-agent (workflow optimization mode)
**Output**: Optimized workflow with parallel paths and critical path
**Validation**:
- All task IDs must exist
- Constraints must be valid categories

## Command Validation and Error Handling

### Parameter Validation
```yaml
validation_rules:
  required_parameters:
    action: "Return error with usage example"
    message: "Missing required parameter: {parameter_name}"
    
  invalid_enum_values:
    action: "Return error with valid options"
    message: "Invalid value '{value}' for {parameter}. Valid options: {options}"
    
  invalid_task_ids:
    action: "Return error with suggestion"
    message: "Task ID {id} not found. Use /list-tasks to see available tasks"
    
  malformed_input:
    action: "Return error with correct syntax"
    message: "Invalid syntax. Correct usage: {command_syntax}"
```

### Error Recovery
```yaml
error_recovery:
  partial_success:
    behavior: "Return partial results with error details"
    example: "Completed analysis for 3 of 5 tasks. Failed tasks: {failed_ids}"
    
  agent_unavailable:
    behavior: "Graceful degradation with alternative approach"
    example: "Primary agent unavailable. Using simplified analysis."
    
  timeout_handling:
    behavior: "Return progress update with continuation option"
    example: "Analysis in progress. Use /status {operation_id} to check progress."
```

### Help System
```yaml
help_commands:
  /help:
    description: "Show all available commands"
    output: "Categorized list of commands with brief descriptions"
    
  /help [command]:
    description: "Show detailed help for specific command"
    output: "Syntax, parameters, examples, and usage notes"
    
  /examples [category]:
    description: "Show example usage for command category"
    output: "Real-world examples with explanations"
```

## Command Shortcuts

### Quick Commands
```yaml
shortcuts:
  /qt [description]:
    expands_to: "/create-task [description] medium"
    description: "Quick task creation with medium priority"
    
  /qr [topic]:
    expands_to: "/research [topic] quick"
    description: "Quick research with minimal detail"
    
  /dr [topic]:
    expands_to: "/research [topic] deep"
    description: "Deep research with comprehensive analysis"
    
  /ac [description]:
    expands_to: "/analyze-complexity [description]"
    description: "Quick complexity analysis"
```

### Workflow Shortcuts
```yaml
workflow_shortcuts:
  /feature [description]:
    expands_to: "/plan-feature [description] medium"
    description: "Complete feature planning workflow"
    
  /fix [description]:
    expands_to: "/create-task [description] high"
    description: "Create high-priority fix task"
    
  /spike [description]:
    expands_to: "/research [description] deep"
    description: "Research spike for investigation"
```

## Integration Features

### Context Awareness
- Commands automatically access current project context
- Task IDs are validated against current project tasks
- Research is tailored to current technology stack
- Recommendations consider team capabilities and constraints

### Agent Coordination
- Commands route to appropriate specialized agents
- Agents share context and coordinate for complex workflows
- Results are aggregated and formatted consistently
- Error handling is coordinated across agent boundaries

### Output Formatting
- All outputs follow consistent JSON schema
- Results include metadata for tracking and debugging
- Error messages are user-friendly with actionable guidance
- Success responses include next step recommendations
