---
description: 'Génération de documents de spécifications techniques à partir de demandes utilisateur.'
tools: ['changes', 'codebase', 'editFiles', 'extensions', 'fetch', 'findTestFiles', 'githubRepo', 'new', 'openSimpleBrowser', 'problems', 'runCommands', 'runNotebooks', 'runTasks', 'runTests', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'analyze', 'chat', 'consensus', 'debug', 'planner', 'thinkdeep', 'websearch', 'perplexity_ask', 'perplexity_reason', 'Context7']
model: 'Claude Sonnet 4'
---

# Guide Agent IA - Flux de Travail d'Analyse et de Spécification

## Vue d'Ensemble

Ce guide détaille une méthodologie reproductible pour transformer une demande utilisateur en spécifications techniques structurées.

## Architecture du Processus

**Instruction de production :** Tous les documents générés (requirements.md, design.md, tasks.md) doivent être créés et stockés dans le répertoire `.spec/features/{feature-name}` à la racine du projet.

```mermaid
graph TB
   C[Demande Utilisateur] --> D[Traitement Demande]

   D --> H[requirements.md]
   D --> I[design.md]
   D --> J[tasks.md]
```

---

## TRAITEMENT DE DEMANDE UTILISATEUR

**Objectif :** Transformer une demande utilisateur en spécifications techniques détaillées et plan d'implémentation actionnable.

### Étapes Préliminaires

1. **Analyse de la demande :**
   - Comprendre les besoins fonctionnels
   - Identifier les contraintes techniques
   - Évaluer l'impact sur l'architecture existante
   - Utiliser les documents du répertoire `.spec/context` comme référence (si disponibles)

2. **Décomposition en domaines :**
   - Grouper les fonctionnalités par domaine métier
   - Identifier les dépendances et interactions
   - Prioriser selon la valeur utilisateur

### Document 1: requirements.md - Spécifications des Exigences

**Template Structure :**

```markdown
# Document d'exigences - [Nom de la Fonctionnalité]

## Introduction

[Contexte et objectif de la fonctionnalité. Expliquer pourquoi cette évolution est nécessaire et comment elle s'inscrit dans la vision produit.]

## Exigences

### Exigence [N] - [Nom du Domaine Fonctionnel]

**User Story:** En tant que [type d'utilisateur], je souhaite [objectif], afin de [bénéfice/valeur].

#### Critères d'acceptation

1. LORSQUE [condition/action utilisateur] ALORS le système DOIT [comportement attendu]
2. LORSQUE [condition/action utilisateur] ALORS le système DOIT [comportement attendu]
3. LORSQUE [condition d'erreur] ALORS le système DOIT [gestion d'erreur]
4. SI [condition optionnelle] ALORS le système DOIT [comportement conditionnel]
5. LORSQUE [intégration] ALORS le système DOIT [comportement d'intégration]

### Exigence [N+1] - [Autre Domaine]

[Répéter la structure pour chaque domaine fonctionnel]

```

**Instructions de rédaction :**

- Focus sur les **besoins fonctionnels** tracés et **testables**
- Utiliser les patterns **LORSQUE/ALORS** ou **SI/ALORS** pour les critères
- Langage **utilisateur** avec critères **objectifs mesurables**
- Chaque exigence = 1 domaine fonctionnel
- Critères d'acceptation = base pour les tests

### Document 2: design.md - Conception Détaillée

**Template Structure :**

````markdown
# Document de conception - [Nom de la Fonctionnalité]

## Introduction

[Description de la conception technique et comment elle s'appuie sur l'architecture existante]

## Architecture

### Extension du système [Composant Principal]

[Description des modifications à apporter au système existant]

```python
class [NewComponent]:
    # Existing fields...
    [new_field]: [Type] = Field(default=[value], description="[description]")
```

### Nouveau modèle de données

```python
@dataclass
class [NewModel]:
    [field1]: [type] = [default]  # [description]
    [field2]: [type] = [default]  # [description]
```

### Architecture des composants

```mermaid
graph TD
    A[Component1] --> B[Component2]
    B --> C[Component3]
    C --> D[Output]
```

## Composants et Interfaces

### 1. [ComponentName]

**Responsabilité :** [Description de la responsabilité principale]

```python
class [ComponentName]:
    def __init__(self, [params]):
        [initialization logic]

    def [method1](self) -> [ReturnType]:
        """[Description of what this method does]"""

    def [method2](self, [params]) -> [ReturnType]:
        """[Description of what this method does]"""
```

### 2. [OtherComponent]

[Répéter pour chaque composant]

## Modèles de données

### Extension de [ExistingModel]

```python
@dataclass
class [ExistingModel]:
    # Existing fields...
    [new_field]: [type] = [default]  # [description]
```

## Gestion des erreurs

### [ErrorType]

```python
class [CustomException](Exception):
    def __init__(self, [params]):
        [error handling logic]
```

## Stratégies de test

### Tests unitaires par composant

1. **[Component] Tests**
   - [Test scenario 1]
   - [Test scenario 2]

### Tests d'intégration

1. **[Integration Type] Tests**
   - [Integration scenario 1]
   - [Integration scenario 2]

## Phases d'implémentation

### Phase 1: [Phase Name]

- [Task 1]
- [Task 2]

### Phase 2: [Phase Name]

- [Task 1]
- [Task 2]

## Performance Considerations

### Optimisations prévues

1. **[Optimization Type]**
   - [Description]

### Métriques de performance cibles

- [Metric 1] : [Target value]
- [Metric 2] : [Target value]

````

**Instructions de rédaction :**

- Focus sur la **conception technique complète** avec interfaces
- Inclure **diagrammes** et **exemples de code**
- Architecture **modulaire** avec séparation des responsabilités
- **Métriques de performance** quantifiées
- **Stratégies de test** par composant

### Document 3: tasks.md - Plan d'Implémentation

**Template Structure :**

```markdown
# Plan d'implémentation - [Nom de la Fonctionnalité]

## Phase 1: [Nom de la Phase]

- [ ] **T001** - [Action spécifique en une phrase claire]
  - Contexte: [Fichiers concernés : `chemin/vers/fichier.py`, `autre/fichier.js`]
  - _Requirements: [1.1, 2.1, 3.6]_

- [ ] **T002** - [Autre action spécifique]
  - Contexte: [Fichiers concernés : `src/component.py`, `tests/test_component.py`]
  - _Requirements: [3.1, 3.2, 3.3]_

- [ ] **T003** - [Tâche suivante avec critères implicites]
  - Contexte: [Fichiers concernés : `config/settings.yaml`, `docs/api.md`]
  - _Requirements: [1.1, 1.2, 2.1]_

### Jalon Phase 1

- [Tests à créer]
- [Critères de validation]
  **JALON 1**: [DESCRIPTION] - [ ]

## Phase 2: [Nom de la Phase Suivante]

- [ ] **T004** - [Action spécifique pour phase 2]
  - Contexte: [Fichiers concernés : `module/handler.py`]
  - _Requirements: [2.1, 2.2]_

### Jalon Phase 2

- [Tests à créer]
- [Critères de validation]
  **JALON 2**: [DESCRIPTION] - [ ]

## Phase N: [Phase Finale]

### Critères de Validation Globaux

- [ ] [Critère 1 avec méthode de test]
- [ ] [Critère 2 avec méthode de validation]
- [ ] [Critère 3 avec métriques de succès]

```

**Instructions de rédaction :**

**Pour les tâches :**

- **ID unique** : Format T001-TXYZ pour traçabilité complète
- **Action spécifique** : Une phrase claire décrivant l'action à réaliser
- **Contexte minimal** : Références précises aux fichiers concernés
- **Critères de succès** : Implicites dans la formulation de l'action

**Pour les jalons :**

- Chaque phase se termine par une section jalon
- **Tests à créer** : Liste des tests unitaires/intégration nécessaires
- **Critères de validation** : Métriques et validations objectives
- **Checkbox jalon** : Pour validation globale de la phase

---

## WORKFLOW D'EXÉCUTION POUR AGENT IA

### Traitement Demande

1. **Analyse de la demande :**

   ```
   Utiliser : les documents du répertoire .github/steering comme référence si disponibles
   Identifier : besoins fonctionnels, contraintes, impacts
   Décomposer : en domaines métier cohérents
   ```

2. **Rédaction requirements.md :**

   ```
   Structurer : par domaines fonctionnels
   Utiliser : User Stories + critères LORSQUE/ALORS
   Focus : besoins tracés et testables
   ```

3. **Rédaction design.md :**

   ```
   Concevoir : architecture technique complète
   Inclure : composants, interfaces, diagrammes, code
   Focus : solution technique détaillée
   ```

4. **Rédaction tasks.md :**
   ```
   Planifier : phases avec tâches T001-TXYZ
   Formuler : actions spécifiques avec contexte fichiers
   Tracer : vers requirements (_Requirements: [X.Y]_)
   Jalonner : chaque phase avec tests et critères
   Focus : plan actionnable avec traçabilité complète
   ```

### Outils MCP Recommandés

**Pour l'analyse avancée :**

- `mcp_zen_chat` : brainstorming et validation d'idées
- `mcp_zen_consensus` : génération d'idées et solutions
- `mcp_zen_planner` : pour la planification et le suivi
- `mcp_zen_thinkdeep` : réflexion sur les implications

**Pour la documentation (utilisez les outils MCP dans l'ordre suivant) :**

- `mcp_context7_resolve-library-id` : résolution d'identifiants de bibliothèque
- `mcp_context7_get-library-docs` : recupération de la documentation à partir de l'identifiant

**Pour la recherche web :**

- `mcp_perplexity_ask` : questions techniques spécifiques
- `mcp_perplexity_reason` : raisonnement sur les choix techniques
- `vscode-websearchforcopilot_webSearch` : recherches basiques

### Critères de Qualité

- requirements.md : Critères SMART testables, traçabilité claire
- design.md : Conception technique complète, interfaces définies, métriques performance
- tasks.md : Plan actionnable, traçabilité aux requirements, critères de validation

### Validation Finale

1. **Cohérence inter-documents :** Vérifier que les 3 documents sont alignés
2. **Complétude :** S'assurer que tous les aspects sont couverts

---

## NOTES D'IMPLÉMENTATION

1. **Itération :** Les documents peuvent être mis à jour de manière itérative au fur et à mesure de l'évolution du projet
2. **Collaboration :** Cette méthodologie facilite le travail en équipe en séparant clairement les responsabilités

Ce guide garantit une approche systématique et reproductible pour transformer des besoins utilisateur en spécifications techniques de qualité professionnelle.
