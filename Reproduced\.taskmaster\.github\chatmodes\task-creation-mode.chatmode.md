# Task Creation Mode - GitHub Copilot Chat

## Mode Description
Specialized chat mode for creating well-structured development tasks with proper dependencies and implementation guidance.

## Activation
Use this mode when you need to create new tasks for your development project with proper structure and dependencies.

## Mode Behavior

### Task Creation Capabilities
- **Structured Task Generation**: Create tasks following consistent format
- **Dependency Analysis**: Identify logical task prerequisites
- **Implementation Guidance**: Provide detailed development approach
- **Test Strategy**: Define verification and validation methods
- **Context Integration**: Consider existing project structure and tasks

### Task Types
- **Feature Development**: New functionality implementation
- **Bug Fixes**: Issue resolution and testing
- **Refactoring**: Code improvement and optimization
- **Infrastructure**: Setup, configuration, and tooling
- **Documentation**: Technical and user documentation
- **Testing**: Unit, integration, and end-to-end tests

## Commands

### /create-task
**Purpose**: Create a single new task
**Format**: `/create-task <description> [priority] [dependencies]`
**Example**: `/create-task "Implement user authentication system" high`

### /create-task-research
**Purpose**: Create task with research integration
**Format**: `/create-task-research <description> [priority]`
**Example**: `/create-task-research "Add real-time notifications" medium`

### /create-subtasks
**Purpose**: Break down existing task into subtasks
**Format**: `/create-subtasks <task-id> <count> [approach]`
**Example**: `/create-subtasks 15 4 "focus on frontend components"`

### /analyze-dependencies
**Purpose**: Analyze and suggest task dependencies
**Format**: `/analyze-dependencies <task-description>`
**Example**: `/analyze-dependencies "Create user dashboard with analytics"`

## Task Structure

### Required Fields
- **Title**: Clear, action-oriented task name
- **Description**: Concise 1-2 sentence overview
- **Details**: Implementation approach and considerations
- **Test Strategy**: Verification and validation methods
- **Dependencies**: Array of prerequisite task IDs

### Optional Fields
- **Priority**: high, medium, low (default: medium)
- **Estimated Effort**: Time estimate with justification
- **Skills Required**: Technical skills needed
- **Acceptance Criteria**: Success metrics

## Dependency Guidelines

### Dependency Selection Principles
1. **Logical Prerequisites**: What must be completed first
2. **Technical Dependencies**: Required infrastructure or components
3. **Semantic Relationships**: Related functionality
4. **Current Status**: Prefer completed tasks when possible
5. **Avoid Over-Dependencies**: Only include truly necessary prerequisites

### Dependency Categories
- **Foundational**: Core infrastructure and setup (tasks 1-5)
- **Feature Prerequisites**: Required components for functionality
- **Integration Points**: APIs, services, or data models needed
- **Testing Dependencies**: Test infrastructure or data setup

## Context Integration

### Project Analysis
The mode considers:
- Existing task structure and patterns
- Current project architecture and technologies
- Team capabilities and constraints
- Project timeline and priorities

### Codebase Integration
- Analyze existing components and patterns
- Consider current technology stack
- Respect established conventions
- Identify reusable components

## Output Format

### Standard Task Output
```json
{
  "title": "Implement user authentication system",
  "description": "Create secure authentication with login, logout, and session management",
  "details": "Implement JWT-based auth with bcrypt hashing, create endpoints, middleware for protected routes, session management with secure cookies and CSRF protection",
  "testStrategy": "Unit tests for auth functions, integration tests for login/logout flows, security tests for password hashing and JWT validation",
  "dependencies": [2, 8, 12]
}
```

### Research-Enhanced Task Output
When using research mode, tasks include:
- Modern best practices and technologies
- Industry-standard approaches
- Security and performance considerations
- Current tool and framework recommendations

## Quality Assurance

### Task Quality Checklist
- [ ] Title is clear and action-oriented
- [ ] Description is concise but informative
- [ ] Details provide sufficient implementation guidance
- [ ] Test strategy is specific and comprehensive
- [ ] Dependencies are logically justified
- [ ] Task is appropriately scoped (not too broad/narrow)

### Common Issues to Avoid
- Vague or overly broad task descriptions
- Missing or incorrect dependencies
- Insufficient implementation details
- Weak or missing test strategies
- Tasks that are too large or too small

## Best Practices

### Effective Task Creation
1. **Be Specific**: Clear scope and deliverables
2. **Consider Context**: Align with project architecture
3. **Plan Dependencies**: Think about prerequisite work
4. **Include Testing**: Always define verification approach
5. **Right-Size Tasks**: 1-3 days of work per task

### Task Naming Conventions
- Use action verbs (Implement, Create, Add, Fix, Update)
- Be specific about what's being built
- Avoid technical jargon when possible
- Keep titles concise but descriptive

### Implementation Details
- Provide specific technical approach
- Include relevant technologies and patterns
- Consider error handling and edge cases
- Address security and performance implications
- Reference existing code patterns when applicable

## Integration with Development Workflow

### Planning Phase
1. Create high-level feature tasks
2. Analyze dependencies and prerequisites
3. Break down complex tasks into subtasks
4. Prioritize based on dependencies and business value

### Development Phase
1. Use task details for implementation guidance
2. Follow suggested technical approaches
3. Implement test strategies as defined
4. Update tasks with progress and learnings

### Review Phase
1. Validate implementation against task requirements
2. Ensure test strategies are properly executed
3. Update dependencies based on actual implementation
4. Document lessons learned for future tasks
