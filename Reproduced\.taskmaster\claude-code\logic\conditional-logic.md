# Advanced Conditional Logic for Claude-Code

## Overview
This document defines the implementation of advanced conditional logic and Handlebars helper support for Claude-Code agents, enabling sophisticated prompt adaptation and dynamic behavior.

## Handlebars Helper Implementation

### Core Helpers

#### Equality Helper (eq)
```javascript
// Implementation logic for {{#if (eq value1 value2)}}
function eq(value1, value2) {
  return value1 === value2;
}

// Usage in agent logic:
if (eq(task.priority, "high")) {
  // High priority task handling
  return enhancedTaskCreation(task);
} else {
  // Standard task handling
  return standardTaskCreation(task);
}
```

#### Negation Helper (not)
```javascript
// Implementation logic for {{#if (not condition)}}
function not(condition) {
  return !condition;
}

// Usage in agent logic:
if (not(task.hasResearch)) {
  // Add research recommendation
  task.recommendations.push("Consider adding research phase");
}
```

#### Greater Than Helper (gt)
```javascript
// Implementation logic for {{#if (gt value threshold)}}
function gt(value, threshold) {
  return value > threshold;
}

// Usage in agent logic:
if (gt(task.complexity, 3)) {
  // Recommend task expansion
  return {
    ...task,
    expansionRecommended: true,
    reason: "High complexity requires breakdown"
  };
}
```

#### Greater Than or Equal Helper (gte)
```javascript
// Implementation logic for {{#if (gte value threshold)}}
function gte(value, threshold) {
  return value >= threshold;
}

// Usage in agent logic:
if (gte(subtaskCount, 5)) {
  // Apply complex project patterns
  return applyComplexProjectPatterns(task);
}
```

#### JSON Helper (json)
```javascript
// Implementation logic for {{json object}}
function json(object) {
  return JSON.stringify(object, null, 2);
}

// Usage in agent logic:
function formatTaskOutput(task) {
  return {
    formatted: json(task),
    metadata: {
      timestamp: new Date().toISOString(),
      agent: "task-creator"
    }
  };
}
```

### Advanced Helper Combinations

#### Complex Conditional Logic
```javascript
// Equivalent to: {{#if (and (eq priority "high") (gt complexity 3))}}
function complexHighPriorityCheck(task) {
  return eq(task.priority, "high") && gt(task.complexity, 3);
}

// Equivalent to: {{#if (or (eq type "research") (gte estimatedDays 5))}}
function researchOrLongTaskCheck(task) {
  return eq(task.type, "research") || gte(task.estimatedDays, 5);
}

// Equivalent to: {{#if (not (eq status "completed"))}}
function incompleteTaskCheck(task) {
  return not(eq(task.status, "completed"));
}
```

## Conditional Variants Implementation

### Research Variant Logic
```javascript
class ConditionalLogic {
  static shouldUseResearchVariant(context) {
    // Original condition: research === true || researchDepth === "deep"
    return eq(context.research, true) || eq(context.researchDepth, "deep");
  }

  static applyResearchVariant(basePrompt, context) {
    if (this.shouldUseResearchVariant(context)) {
      return {
        ...basePrompt,
        researchPhase: {
          enabled: true,
          depth: context.researchDepth || "standard",
          focusAreas: context.focusAreas || [],
          sources: ["documentation", "best-practices", "case-studies"]
        },
        instructions: basePrompt.instructions + "\n\nInclude comprehensive research findings in your analysis."
      };
    }
    return basePrompt;
  }
}
```

### Complexity Report Variant Logic
```javascript
class ComplexityVariants {
  static shouldUseComplexityReport(context) {
    // Original condition: complexity >= 4 || riskLevel === "high"
    return gte(context.complexity, 4) || eq(context.riskLevel, "high");
  }

  static applyComplexityReportVariant(basePrompt, context) {
    if (this.shouldUseComplexityReport(context)) {
      return {
        ...basePrompt,
        complexityAnalysis: {
          enabled: true,
          dimensions: ["technical", "implementation", "integration", "testing", "risk"],
          riskAssessment: true,
          mitigationStrategies: true,
          expansionRecommendations: true
        },
        outputFormat: {
          ...basePrompt.outputFormat,
          includeComplexityBreakdown: true,
          includeRiskFactors: true,
          includeImplementationGuidance: true
        }
      };
    }
    return basePrompt;
  }
}
```

### Batch Processing Variant Logic
```javascript
class BatchVariants {
  static shouldUseBatchProcessing(context) {
    // Original condition: taskCount > 1 || batchMode === true
    return gt(context.taskCount, 1) || eq(context.batchMode, true);
  }

  static applyBatchVariant(basePrompt, context) {
    if (this.shouldUseBatchProcessing(context)) {
      return {
        ...basePrompt,
        batchProcessing: {
          enabled: true,
          taskCount: context.taskCount,
          coordinationRequired: true,
          dependencyAnalysis: true,
          parallelProcessing: context.allowParallel || false
        },
        instructions: basePrompt.instructions + "\n\nProcess multiple tasks with dependency analysis and coordination."
      };
    }
    return basePrompt;
  }
}
```

### Team Collaboration Variant Logic
```javascript
class TeamVariants {
  static shouldUseTeamCollaboration(context) {
    // Original condition: teamSize > 1 || collaborationMode === true
    return gt(context.teamSize, 1) || eq(context.collaborationMode, true);
  }

  static applyTeamVariant(basePrompt, context) {
    if (this.shouldUseTeamCollaboration(context)) {
      return {
        ...basePrompt,
        teamCollaboration: {
          enabled: true,
          teamSize: context.teamSize,
          skillDistribution: context.skillDistribution || {},
          communicationPatterns: context.communicationPatterns || "standard",
          reviewProcesses: context.reviewProcesses || true
        },
        instructions: basePrompt.instructions + "\n\nConsider team collaboration patterns and skill distribution."
      };
    }
    return basePrompt;
  }
}
```

## Dynamic Prompt Adaptation

### Context-Aware Prompt Selection
```javascript
class PromptAdapter {
  static adaptPrompt(basePrompt, context) {
    let adaptedPrompt = { ...basePrompt };

    // Apply conditional variants in order
    adaptedPrompt = ConditionalLogic.applyResearchVariant(adaptedPrompt, context);
    adaptedPrompt = ComplexityVariants.applyComplexityReportVariant(adaptedPrompt, context);
    adaptedPrompt = BatchVariants.applyBatchVariant(adaptedPrompt, context);
    adaptedPrompt = TeamVariants.applyTeamVariant(adaptedPrompt, context);

    // Apply context-specific adaptations
    adaptedPrompt = this.applyProjectContextAdaptations(adaptedPrompt, context);
    adaptedPrompt = this.applyTechnologyStackAdaptations(adaptedPrompt, context);
    adaptedPrompt = this.applyTimelineAdaptations(adaptedPrompt, context);

    return adaptedPrompt;
  }

  static applyProjectContextAdaptations(prompt, context) {
    if (context.projectType) {
      switch (context.projectType) {
        case "web-application":
          return this.addWebApplicationContext(prompt, context);
        case "api-service":
          return this.addApiServiceContext(prompt, context);
        case "mobile-application":
          return this.addMobileApplicationContext(prompt, context);
        case "data-pipeline":
          return this.addDataPipelineContext(prompt, context);
        default:
          return prompt;
      }
    }
    return prompt;
  }

  static applyTechnologyStackAdaptations(prompt, context) {
    if (context.technologyStack) {
      const adaptations = {
        react: this.addReactSpecificGuidance,
        nodejs: this.addNodeJSSpecificGuidance,
        python: this.addPythonSpecificGuidance,
        docker: this.addDockerSpecificGuidance
      };

      context.technologyStack.forEach(tech => {
        if (adaptations[tech]) {
          prompt = adaptations[tech](prompt, context);
        }
      });
    }
    return prompt;
  }

  static applyTimelineAdaptations(prompt, context) {
    if (context.timeline) {
      if (eq(context.timeline, "tight")) {
        return {
          ...prompt,
          timelineConstraints: {
            prioritizeMVP: true,
            simplifyImplementation: true,
            deferOptimizations: true,
            focusOnCoreFeatures: true
          }
        };
      } else if (eq(context.timeline, "flexible")) {
        return {
          ...prompt,
          timelineConstraints: {
            allowExploration: true,
            includeOptimizations: true,
            comprehensiveTesting: true,
            documentationFocus: true
          }
        };
      }
    }
    return prompt;
  }
}
```

## Agent-Specific Conditional Logic

### Task Creator Agent Conditionals
```javascript
class TaskCreatorConditionals {
  static applyConditionalLogic(task, context) {
    let enhancedTask = { ...task };

    // Research integration conditional
    if (ConditionalLogic.shouldUseResearchVariant(context)) {
      enhancedTask = this.addResearchIntegration(enhancedTask, context);
    }

    // Complexity-based enhancements
    if (gte(context.estimatedComplexity, 3)) {
      enhancedTask = this.addComplexityConsiderations(enhancedTask, context);
    }

    // Team size adaptations
    if (gt(context.teamSize, 3)) {
      enhancedTask = this.addTeamCoordinationElements(enhancedTask, context);
    }

    // Timeline pressure adaptations
    if (eq(context.timeline, "tight")) {
      enhancedTask = this.addTimelinePressureAdaptations(enhancedTask, context);
    }

    return enhancedTask;
  }

  static addResearchIntegration(task, context) {
    return {
      ...task,
      researchRequirements: {
        enabled: true,
        depth: context.researchDepth || "standard",
        focusAreas: context.focusAreas || [],
        deliverables: ["technology-analysis", "best-practices", "implementation-strategy"]
      }
    };
  }

  static addComplexityConsiderations(task, context) {
    return {
      ...task,
      complexityConsiderations: {
        riskMitigation: true,
        phaseDecomposition: true,
        testingStrategy: "comprehensive",
        monitoringRequirements: true
      }
    };
  }
}
```

### Research Agent Conditionals
```javascript
class ResearchAgentConditionals {
  static applyConditionalLogic(researchRequest, context) {
    let enhancedRequest = { ...researchRequest };

    // Depth-based research strategy
    if (eq(context.detailLevel, "deep")) {
      enhancedRequest = this.addDeepResearchStrategy(enhancedRequest, context);
    } else if (eq(context.detailLevel, "quick")) {
      enhancedRequest = this.addQuickResearchStrategy(enhancedRequest, context);
    }

    // Project-specific research focus
    if (context.projectConstraints) {
      enhancedRequest = this.addProjectConstraintsFocus(enhancedRequest, context);
    }

    // Technology-specific research
    if (context.technologyStack) {
      enhancedRequest = this.addTechnologySpecificResearch(enhancedRequest, context);
    }

    return enhancedRequest;
  }

  static addDeepResearchStrategy(request, context) {
    return {
      ...request,
      researchStrategy: {
        depth: "comprehensive",
        sources: ["academic-papers", "case-studies", "expert-opinions", "documentation"],
        analysisDepth: "detailed",
        comparativeAnalysis: true,
        futureConsiderations: true
      }
    };
  }

  static addQuickResearchStrategy(request, context) {
    return {
      ...request,
      researchStrategy: {
        depth: "overview",
        sources: ["documentation", "best-practices"],
        analysisDepth: "summary",
        focusOnActionable: true,
        timeConstraint: "minimal"
      }
    };
  }
}
```

### Task Expansion Agent Conditionals
```javascript
class TaskExpansionConditionals {
  static applyConditionalLogic(expansionRequest, context) {
    let enhancedRequest = { ...expansionRequest };

    // Complexity-based expansion strategy
    if (gte(context.complexity, 4)) {
      enhancedRequest = this.addHighComplexityStrategy(enhancedRequest, context);
    }

    // Team size considerations
    if (gt(context.teamSize, 5)) {
      enhancedRequest = this.addLargeTeamStrategy(enhancedRequest, context);
    } else if (eq(context.teamSize, 1)) {
      enhancedRequest = this.addSoloDevStrategy(enhancedRequest, context);
    }

    // Timeline constraints
    if (eq(context.timeline, "tight")) {
      enhancedRequest = this.addTightTimelineStrategy(enhancedRequest, context);
    }

    return enhancedRequest;
  }

  static addHighComplexityStrategy(request, context) {
    return {
      ...request,
      expansionStrategy: {
        granularity: "fine",
        riskDistribution: true,
        parallelPaths: true,
        integrationPoints: "explicit",
        testingStrategy: "comprehensive"
      }
    };
  }

  static addLargeTeamStrategy(request, context) {
    return {
      ...request,
      teamStrategy: {
        parallelDevelopment: true,
        clearOwnership: true,
        coordinationOverhead: "considered",
        communicationPlan: true,
        integrationStrategy: "planned"
      }
    };
  }
}
```

## Validation and Error Handling

### Conditional Logic Validation
```javascript
class ConditionalValidator {
  static validateConditions(conditions, context) {
    const errors = [];
    const warnings = [];

    // Validate helper function usage
    conditions.forEach(condition => {
      if (condition.helper && !this.isValidHelper(condition.helper)) {
        errors.push(`Invalid helper function: ${condition.helper}`);
      }

      if (condition.parameters) {
        const paramValidation = this.validateParameters(condition.parameters, context);
        errors.push(...paramValidation.errors);
        warnings.push(...paramValidation.warnings);
      }
    });

    // Validate context availability
    const contextValidation = this.validateContextAvailability(conditions, context);
    errors.push(...contextValidation.errors);
    warnings.push(...contextValidation.warnings);

    return { errors, warnings };
  }

  static isValidHelper(helperName) {
    const validHelpers = ["eq", "not", "gt", "gte", "lt", "lte", "and", "or", "json"];
    return validHelpers.includes(helperName);
  }

  static validateParameters(parameters, context) {
    const errors = [];
    const warnings = [];

    parameters.forEach(param => {
      if (param.required && !context.hasOwnProperty(param.name)) {
        errors.push(`Required parameter missing: ${param.name}`);
      }

      if (param.type && context[param.name] && typeof context[param.name] !== param.type) {
        warnings.push(`Parameter type mismatch: ${param.name} expected ${param.type}, got ${typeof context[param.name]}`);
      }
    });

    return { errors, warnings };
  }
}
```

### Graceful Degradation
```javascript
class ConditionalFallback {
  static handleMissingContext(condition, context) {
    // Provide sensible defaults when context is missing
    const defaults = {
      priority: "medium",
      complexity: 2,
      teamSize: 3,
      timeline: "standard",
      researchDepth: "standard"
    };

    const fallbackContext = { ...defaults, ...context };
    
    return {
      context: fallbackContext,
      warnings: [`Missing context for condition: ${condition.name}, using default values`]
    };
  }

  static simplifyCondition(condition, availableContext) {
    // Simplify complex conditions when full context isn't available
    if (condition.type === "complex" && !this.hasRequiredContext(condition, availableContext)) {
      return {
        type: "simple",
        fallback: true,
        originalCondition: condition,
        simplifiedLogic: this.createSimplifiedLogic(condition, availableContext)
      };
    }
    return condition;
  }
}
```

## Integration with Agents

### Agent Registration System
```javascript
class ConditionalLogicRegistry {
  static registerAgent(agentName, conditionalLogic) {
    this.agents = this.agents || {};
    this.agents[agentName] = conditionalLogic;
  }

  static applyConditionalLogic(agentName, input, context) {
    const agent = this.agents[agentName];
    if (agent && agent.applyConditionalLogic) {
      return agent.applyConditionalLogic(input, context);
    }
    return input; // No conditional logic available, return unchanged
  }

  static validateAgentLogic(agentName) {
    const agent = this.agents[agentName];
    if (agent && agent.validateLogic) {
      return agent.validateLogic();
    }
    return { valid: true, warnings: [] };
  }
}

// Register agents with their conditional logic
ConditionalLogicRegistry.registerAgent("task-creator", TaskCreatorConditionals);
ConditionalLogicRegistry.registerAgent("research-agent", ResearchAgentConditionals);
ConditionalLogicRegistry.registerAgent("task-expansion", TaskExpansionConditionals);
ConditionalLogicRegistry.registerAgent("complexity-analyzer", ComplexityAnalyzerConditionals);
```

## Usage Examples

### Example 1: Research-Enhanced Task Creation
```javascript
const context = {
  research: true,
  researchDepth: "deep",
  priority: "high",
  complexity: 4,
  teamSize: 5
};

const baseTask = {
  title: "Implement microservices architecture",
  description: "Migrate monolithic application to microservices"
};

const enhancedTask = ConditionalLogicRegistry.applyConditionalLogic(
  "task-creator", 
  baseTask, 
  context
);

// Result includes research integration, complexity considerations, and team coordination
```

### Example 2: Conditional Task Expansion
```javascript
const context = {
  complexity: 5,
  teamSize: 8,
  timeline: "tight",
  projectType: "web-application"
};

const expansionRequest = {
  taskId: "task-123",
  subtaskCount: 6
};

const enhancedExpansion = ConditionalLogicRegistry.applyConditionalLogic(
  "task-expansion",
  expansionRequest,
  context
);

// Result includes high-complexity strategy, large team coordination, and timeline adaptations
```
