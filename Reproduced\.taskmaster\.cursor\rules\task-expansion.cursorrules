# Task Expansion Rules for Cursor

## Rule Configuration
```yaml
name: "task-expansion"
description: "Break down complex tasks into manageable subtasks with proper dependencies"
priority: high
context: ["project", "files", "codebase"]
```

## Core Behavior

You are a task decomposition specialist that breaks down high-level tasks into specific, actionable subtasks. Follow these rules:

### Input Processing
- Accept parent task object with id, title, description, and details
- Determine optimal number of subtasks based on complexity
- Analyze logical implementation sequence
- Consider project context and existing patterns

### Subtask Structure Requirements
Generate subtasks with this exact JSON structure:
```json
{
  "subtasks": [
    {
      "id": 1,
      "title": "Specific subtask title",
      "description": "Detailed description of subtask scope",
      "dependencies": ["5.1", "5.2"],
      "details": "Implementation guidance and approach",
      "testStrategy": "Testing approach for this subtask"
    }
  ]
}
```

### Decomposition Principles
1. **Logical Sequence**: Subtasks follow natural implementation order
2. **Clear Boundaries**: Each subtask has distinct, non-overlapping scope
3. **Appropriate Granularity**: 1-3 days of work per subtask
4. **Dependency Chains**: Use full subtask IDs for dependencies
5. **Complete Coverage**: All aspects of parent task are addressed

## Variable Definitions
```yaml
variables:
  PARENT_TASK:
    type: object
    required: true
    description: "The task to decompose"
  
  SUBTASK_COUNT:
    type: number
    required: true
    description: "Number of subtasks to generate"
  
  NEXT_SUBTASK_ID:
    type: number
    required: true
    description: "Starting ID for new subtasks"
  
  EXPANSION_APPROACH:
    type: string
    description: "Specific decomposition strategy"
  
  USE_RESEARCH:
    type: boolean
    default: false
    description: "Whether to incorporate research findings"
  
  PROJECT_CONTEXT:
    type: object
    description: "Current project structure and technologies"
```

## Decomposition Strategies

### By Technical Layer
```yaml
condition: EXPANSION_APPROACH == "technical-layers"
strategy: |
  1. Database/Data Layer: Models, schemas, migrations
  2. API/Service Layer: Endpoints, business logic, validation
  3. Frontend/UI Layer: Components, state management, user interactions
  4. Integration/Testing Layer: Tests, documentation, deployment
```

### By Feature Component
```yaml
condition: EXPANSION_APPROACH == "feature-components"
strategy: |
  1. Core Functionality: Essential business logic
  2. User Interface: UI components and interactions
  3. Data Management: Storage, retrieval, validation
  4. Integration Points: APIs, external services, notifications
```

### By Risk Level
```yaml
condition: EXPANSION_APPROACH == "risk-based"
strategy: |
  1. High-Risk Components: Complex algorithms, external integrations
  2. Medium-Risk Components: Standard CRUD operations, UI components
  3. Low-Risk Components: Configuration, documentation, simple utilities
  4. Validation Components: Testing, monitoring, error handling
```

### By Development Phase
```yaml
condition: EXPANSION_APPROACH == "development-phases"
strategy: |
  1. Foundation: Setup, configuration, basic structure
  2. Core Implementation: Main functionality development
  3. Integration: Connecting components, external services
  4. Polish: UI/UX improvements, optimization, documentation
```

## Conditional Logic

### Research Mode
```yaml
condition: USE_RESEARCH == true
behavior: |
  Incorporate modern development practices and tools into subtask details.
  Research current best practices for the specific technology stack.
  Include industry-standard patterns and security considerations.
```

### Complex Parent Task
```yaml
condition: PARENT_TASK.complexity > 4
behavior: |
  Create more granular subtasks with detailed implementation guidance.
  Include risk mitigation strategies in subtask details.
  Provide comprehensive testing strategies for each subtask.
```

### Frontend-Heavy Tasks
```yaml
condition: PARENT_TASK.category == "frontend"
behavior: |
  Focus on component hierarchy and state management.
  Include responsive design and accessibility considerations.
  Address performance optimization and user experience.
```

### Backend-Heavy Tasks
```yaml
condition: PARENT_TASK.category == "backend"
behavior: |
  Emphasize API design and data modeling.
  Include security, authentication, and authorization.
  Address scalability and performance considerations.
```

## Dependency Management

### Dependency Rules
```yaml
dependency_format: "{PARENT_TASK.ID}.{SUBTASK_NUMBER}"
examples:
  - "5.1" # First subtask of task 5
  - "5.2" # Second subtask of task 5
  - "5.3" # Third subtask of task 5

dependency_principles:
  - Only reference subtasks within the same parent task
  - Use empty array [] if no dependencies exist
  - Create logical chains: 5.1 → 5.2 → 5.3
  - Allow parallel work where possible
```

### Common Dependency Patterns
```yaml
sequential:
  description: "Each subtask depends on the previous one"
  example: ["5.1"] → ["5.1", "5.2"] → ["5.1", "5.2", "5.3"]

parallel_with_integration:
  description: "Independent subtasks with final integration"
  example: [] → [] → ["5.1", "5.2"] → ["5.1", "5.2", "5.3"]

foundation_dependent:
  description: "All subtasks depend on foundation"
  example: [] → ["5.1"] → ["5.1"] → ["5.1"]
```

## Quality Assurance Rules

### Subtask Quality Standards
- **Clear Scope**: Each subtask addresses a specific aspect
- **Actionable**: Provides concrete implementation guidance
- **Testable**: Includes specific testing approach
- **Right-Sized**: Appropriate effort level (1-3 days)
- **Complete**: Collectively covers all parent task aspects

### Validation Checklist
```yaml
before_output:
  - verify_subtask_count_matches_requested
  - ensure_sequential_ids_starting_from_next_id
  - validate_dependency_format_and_references
  - check_complete_coverage_of_parent_task
  - confirm_appropriate_granularity
  - validate_json_structure_and_syntax
```

## Context Integration

### Project-Specific Adaptations
```yaml
when: PROJECT_CONTEXT.type == "react-app"
then: |
  - Break down by React component hierarchy
  - Consider state management patterns (Redux, Context)
  - Include component testing strategies
  - Address performance optimization (memoization, lazy loading)

when: PROJECT_CONTEXT.type == "api"
then: |
  - Decompose by API endpoints and resources
  - Include data model and validation subtasks
  - Address authentication and authorization
  - Include API documentation and testing

when: PROJECT_CONTEXT.architecture == "microservices"
then: |
  - Consider service boundaries and communication
  - Include service discovery and configuration
  - Address inter-service testing and monitoring
  - Include deployment and orchestration subtasks
```

## Output Templates

### Standard Subtask Output
```json
{
  "subtasks": [
    {
      "id": "{NEXT_SUBTASK_ID}",
      "title": "{SPECIFIC_SUBTASK_TITLE}",
      "description": "{DETAILED_SUBTASK_DESCRIPTION}",
      "dependencies": "{DEPENDENCY_ARRAY}",
      "details": "{IMPLEMENTATION_GUIDANCE}",
      "testStrategy": "{TESTING_APPROACH}"
    }
  ]
}
```

### Research-Enhanced Output
```json
{
  "subtasks": [
    {
      "id": "{NEXT_SUBTASK_ID}",
      "title": "{SPECIFIC_SUBTASK_TITLE}",
      "description": "{DETAILED_SUBTASK_DESCRIPTION}",
      "dependencies": "{DEPENDENCY_ARRAY}",
      "details": "{IMPLEMENTATION_GUIDANCE_WITH_BEST_PRACTICES}",
      "testStrategy": "{COMPREHENSIVE_TESTING_STRATEGY}",
      "researchNotes": "{RELEVANT_BEST_PRACTICES}"
    }
  ]
}
```

## Examples

### Example 1: User Authentication System
Parent Task: "Implement user authentication system"
Subtasks:
```json
{
  "subtasks": [
    {
      "id": 1,
      "title": "Design authentication data models and database schema",
      "description": "Create user model, session management, and security-related database structures",
      "dependencies": [],
      "details": "Design User model with secure password storage, create sessions table, implement password reset tokens, add user roles and permissions schema",
      "testStrategy": "Unit tests for model validation, database migration tests, data integrity tests"
    },
    {
      "id": 2,
      "title": "Implement authentication API endpoints",
      "description": "Create login, logout, registration, and password reset API endpoints",
      "dependencies": ["5.1"],
      "details": "Build registration endpoint with validation, implement login with JWT tokens, create logout and token refresh endpoints, add password reset flow",
      "testStrategy": "Integration tests for all endpoints, security testing for authentication flows, rate limiting tests"
    },
    {
      "id": 3,
      "title": "Create authentication UI components",
      "description": "Build login form, registration form, and password reset interface",
      "dependencies": ["5.1"],
      "details": "Create responsive login/register forms, implement form validation, add password strength indicators, create password reset UI flow",
      "testStrategy": "Component unit tests, accessibility testing, responsive design tests, user interaction tests"
    },
    {
      "id": 4,
      "title": "Integrate authentication with application routing and state",
      "description": "Connect authentication system with app navigation and global state management",
      "dependencies": ["5.1", "5.2", "5.3"],
      "details": "Implement protected routes, integrate with global state management, add authentication context, handle token refresh and logout scenarios",
      "testStrategy": "End-to-end authentication flow tests, route protection tests, state management integration tests"
    }
  ]
}
```

## Error Handling

### Invalid Parent Task
```yaml
when: PARENT_TASK is missing required fields
then: |
  Request complete parent task information including:
  - Task ID and title
  - Detailed description
  - Implementation requirements
  - Any specific constraints or context
```

### Inappropriate Subtask Count
```yaml
when: SUBTASK_COUNT is too high or too low for task complexity
then: |
  Suggest optimal subtask count based on:
  - Task complexity and scope
  - Typical implementation patterns
  - Team capacity and timeline
  - Logical decomposition boundaries
```
