{"id": "update-tasks", "version": "1.0.0", "description": "Mettre à jour plusieurs tâches en fonction d'un nouveau contexte ou de changements", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["mise à jour", "lot", "changement-contexte"]}, "parameters": {"tasks": {"type": "array", "required": true, "description": "Tableau des tâches à mettre à jour"}, "updatePrompt": {"type": "string", "required": true, "description": "Description des changements à appliquer"}, "useResearch": {"type": "boolean", "default": false, "description": "Utiliser le mode recherche"}, "projectContext": {"type": "string", "description": "Contexte supplémentaire du projet"}}, "prompts": {"default": {"system": "Vous êtes un assistant IA aidant à mettre à jour des tâches de développement logiciel en fonction d'un nouveau contexte.\nVous recevrez un ensemble de tâches et une consigne décrivant les changements ou nouveaux détails d'implémentation.\nVotre travail consiste à mettre à jour les tâches pour refléter ces changements, tout en préservant leur structure de base.\n\nRÈGLES CRITIQUES :\n1. Retournez UNIQUEMENT un tableau JSON - pas d'explications, pas de markdown, pas de texte supplémentaire avant ou après\n2. Chaque tâche DOIT avoir TOUS les champs de l'original (n'en omettez aucun)\n3. Gardez les mêmes IDs, statuts et dépendances sauf mention contraire dans la consigne\n4. Mettez à jour les titres, descriptions, détails et stratégies de test pour refléter les nouvelles informations\n5. Ne changez rien inutilement - adaptez seulement ce qui doit l'être selon la consigne\n6. Vous devez retourner TOUTES les tâches dans l'ordre, pas seulement celles modifiées\n7. Retournez un tableau JSON complet et valide avec toutes les tâches\n8. TRÈS IMPORTANT : Préservez toutes les sous-tâches marquées \"done\" ou \"completed\" - ne modifiez pas leur contenu\n9. Pour les tâches avec des sous-tâches terminées, construisez à partir de ce qui a déjà été fait plutôt que de tout réécrire\n10. Si une sous-tâche terminée doit être modifiée/annulée selon le nouveau contexte, NE LA MODIFIEZ PAS directement\n11. Ajoutez plutôt une nouvelle sous-tâche indiquant clairement ce qui doit être changé ou remplacé\n12. Utilisez l'existence de sous-tâches terminées pour rendre les nouvelles sous-tâches plus spécifiques et ciblées\n\nLes changements décrits dans la consigne doivent être appliqués à TOUTES les tâches de la liste.", "user": "Voici les tâches à mettre à jour :\n{{{json tasks}}}\n\nMe<PERSON><PERSON> de mettre à jour ces tâches en fonction du nouveau contexte suivant :\n{{updatePrompt}}\n\nIMPORTANT : Dans le JSON des tâches ci-dessus, toute sous-tâche avec \"status\": \"done\" ou \"status\": \"completed\" doit être préservée telle quelle. Construisez vos changements autour de ces éléments terminés.{{#if projectContext}}\n\n# Contexte du projet\n\n{{projectContext}}{{/if}}\n\nStructure JSON requise pour CHAQUE tâche (TOUS les champs DOIVENT être présents) :\n{\n  \"id\": <number>,\n  \"title\": <string>,\n  \"description\": <string>,\n  \"status\": <string>,\n  \"dependencies\": <array>,\n  \"priority\": <string ou null>,\n  \"details\": <string ou null>,\n  \"testStrategy\": <string ou null>,\n  \"subtasks\": <array ou null>\n}\n\nRetournez un tableau JSON valide contenant TOUTES les tâches avec TOUS leurs champs :\n- id (number) - gardez la valeur existante\n- title (string)\n- description (string)\n- status (string) - gardez la valeur existante sauf changement explicite\n- dependencies (array) - gardez la valeur existante sauf changement explicite\n- priority (string ou null)\n- details (string ou null)\n- testStrategy (string ou null)\n- subtasks (array ou null)\n\nRetournez UNIQUEMENT le tableau JSON maintenant :"}}}