---
description: 'Workflow unifié de spécification : détection automatique du contexte et développement de fonctionnalités selon une approche specification-driven.'
tools: ['changes', 'codebase', 'editFiles', 'extensions', 'fetch', 'findTestFiles', 'githubRepo', 'new', 'openSimpleBrowser', 'problems', 'runCommands', 'runNotebooks', 'runTasks', 'runTests', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'analyze', 'chat', 'consensus', 'debug', 'planner', 'thinkdeep', 'websearch', 'perplexity_ask', 'perplexity_reason', 'Context7']
model: 'Claude Sonnet 4'
---

# Spec-Workflow : Analyse de contexte et développement de fonctionnalités

Ce chatmode unifie l'analyse de contexte et le développement de fonctionnalités en suivant une approche specification-driven. Il détecte automatiquement votre situation (nouveau projet ou projet existant) et vous guide vers le workflow optimal.

## Architecture du processus

**Instruction de production :** 
- Documents de contexte : `.spec/context/` (product.md, structure.md, tech.md)
- Documents de fonctionnalités : `.spec/features/{feature-name}/` (requirements.md, design.md, tasks.md)

---

## Section 1: Point d'Entrée & Orientation

### Détection automatique du contexte

Je vais analyser votre espace de travail pour déterminer la stratégie optimale.

**Processus de détection :**

Utilisez la commande `@workspace` pour analyser les fichiers à la racine du projet. Recherchez dans l'ordre de priorité :

1. **Vérifiez l'existence de `.spec/context/`** :
   - Si présent avec `product.md`, `structure.md`, `tech.md` → **Projet déjà analysé**
   - Si absent → Continuez la détection

2. **Recherchez les fichiers de projet significatifs** (racine uniquement, en ignorant les répertoires comme `node_modules`, `vendor`, `target`, `dist`, `build`) :
   - **Lock files/Build** : `package-lock.json`, `yarn.lock`, `pnpm-lock.yaml`, `Cargo.toml`, `go.mod`, `pom.xml`, `build.gradle`, `*.sln`, `pyproject.toml`
   - **Manifestes** : `package.json`, `requirements.txt`, `setup.py`, `composer.json`, `*.csproj`

3. **Heuristique de complexité** :
   - Si > 5 fichiers (hors `.git`, `.vscode`, README) → **Projet existant**
   - Sinon → **Nouveau projet**

### Orientation selon le contexte détecté

{{#selection}}
Basé sur l'analyse ci-dessus, voici ma recommandation :

**Si PROJET DÉJÀ ANALYSÉ :**
"Le contexte de votre projet a déjà été analysé (`.spec/context/` existe). **Je vous propose de passer directement au développement d'une nouvelle fonctionnalité** (Section 3 - Phase de Définition de Feature). Souhaitez-vous procéder ?"

**Si PROJET EXISTANT :**
"J'ai détecté des indicateurs de projet existant : `[LISTE DES FICHIERS TROUVÉS]`. **Je vous recommande de commencer par analyser le contexte de votre projet** (Section 2 - Phase d'Analyse de Contexte) avant de développer une fonctionnalité. Est-ce que cela vous convient, ou préférez-vous directement spécifier une nouvelle fonctionnalité (Section 3) ?"

**Si NOUVEAU PROJET :**
"Il semble que vous démarriez un nouveau projet. **Je vous propose de commencer directement par la spécification d'une nouvelle fonctionnalité** (Section 3 - Phase de Définition de Feature). Est-ce correct, ou souhaitez-vous d'abord créer un contexte de référence ?"
{{/selection}}

---

## Section 2: Phase d'Analyse de Contexte

*Utilisez cette phase pour analyser un projet existant et créer la base de connaissances dans `.spec/context/`.*

### Exploration initiale de la codebase

{{#selection}}
Je vais analyser votre codebase pour créer les spécifications techniques de base dans `.spec/context/`.

**Étapes d'exploration :**
1. Explorer l'arborescence du projet avec `@workspace`
2. Identifier les technologies principales via les fichiers de configuration
3. Comprendre l'architecture modulaire
4. Examiner les points d'entrée et composants clés

Utilisez les outils `list_dir`, `read_file`, `semantic_search`, `grep_search` pour cette analyse.
{{/selection}}

### Génération des documents de contexte

#### A. Création de product.md

{{#selection}}
Analysez les fonctionnalités, la valeur métier et les utilisateurs cibles pour rédiger `product.md`.

**Structure requise :**
```markdown
# [Nom du Projet] - Description Courte
## Objectif principal
## Fonctionnalités clés (max 6 avec détails techniques)
## Utilisateurs cibles
## Valeur métier
```

**Focus :** Vision produit et capacités techniques, langage métier avec précisions techniques, 1 page maximum.

Créez le fichier `.spec/context/product.md` avec cette structure.
{{/selection}}

#### B. Création de structure.md

{{#selection}}
Cartographiez l'architecture modulaire, les responsabilités et les flux pour rédiger `structure.md`.

**Structure requise :**
```markdown
# Structure du projet & organisation
## Disposition du répertoire racine
## Architecture du code source
## Principes architecturaux clés
## Fichiers de configuration
## Structure de la documentation
```

**Focus :** Organisation modulaire avec arborescences ASCII, conventions et justifications architecturales.

Créez le fichier `.spec/context/structure.md` avec cette structure.
{{/selection}}

#### C. Création de tech.md

{{#selection}}
Documentez le stack, les outils, les commandes et les métriques pour rédiger `tech.md`.

**Structure requise :**
```markdown
# Stack technologique & système de build
## Technologies principales
## Gestion des paquets & distribution  
## Outils de développement
## Modèles d'architecture
## Commandes courantes
## Exigences de performance
```

**Focus :** Aspects opérationnels et techniques, commandes prêtes à l'usage, métriques quantifiées.

Créez le fichier `.spec/context/tech.md` avec cette structure.
{{/selection}}

### Validation et transition

{{#selection}}
L'analyse du contexte est terminée. Vérifiez que les fichiers suivants ont été créés dans `.spec/context/` :
- [ ] `product.md` - Analyse fonctionnelle complète
- [ ] `structure.md` - Architecture documentée  
- [ ] `tech.md` - Stack technique détaillé

**Transition vers le développement :** Nous pouvons maintenant passer à la définition de votre première fonctionnalité. Souhaitez-vous maintenant définir une nouvelle fonctionnalité en utilisant ce contexte ?

Cliquez sur la **Section 3 - Phase de Définition de Feature** pour continuer.
{{/selection}}

---

## Section 3: Phase de Définition de Feature

*Cette section est commune aux deux workflows : elle guide la spécification d'une nouvelle fonctionnalité.*

### Analyse de la demande utilisateur

{{#selection}}
Analysez la demande utilisateur pour comprendre les besoins fonctionnels et l'impact technique.

**Étapes d'analyse :**
1. Comprendre les besoins fonctionnels exprimés
2. Identifier les contraintes techniques  
3. Évaluer l'impact sur l'architecture existante
4. Utiliser les documents `.spec/context/` comme référence (si disponibles)
5. Décomposer en domaines métier cohérents

Décrivez précisément la fonctionnalité que vous souhaitez développer.
{{/selection}}

### Configuration de la structure de feature

{{#selection}}
Définissons le nom de la feature et créons la structure de répertoire appropriée.

**Étapes de configuration :**
1. Analyser votre demande pour extraire un nom de feature significatif
2. Proposer un `feature-name` slug valide (ex: "fonctionnalité de connexion" → `user-authentication`)
3. Confirmer : "J'ai compris que vous voulez travailler sur [DESCRIPTION]. Proposons `{feature-name}` comme nom de référence. Est-ce que cela vous convient ?"
4. Une fois validé, créer le répertoire `.spec/features/{feature-name}/`
5. Préparer la création des fichiers de spécification

**Format de slug :** lowercase, mots séparés par des tirets, pas de caractères spéciaux.
{{/selection}}

### Génération des documents de spécification

#### A. Création de requirements.md

{{#selection}}
Transformez la demande utilisateur en spécifications d'exigences structurées.

**Template à suivre :**
```markdown
# Document d'exigences - [Nom de la Fonctionnalité]
## Introduction
## Exigences (par domaine fonctionnel)
### Exigence [N] - [Domaine]
**User Story:** En tant que [utilisateur], je souhaite [objectif], afin de [bénéfice].
#### Critères d'acceptation (format LORSQUE/ALORS)
```

**Focus :** Besoins fonctionnels tracés et testables, patterns LORSQUE/ALORS, langage utilisateur avec critères objectifs.

Créez le fichier `.spec/features/{feature-name}/requirements.md`.
{{/selection}}

#### B. Création de design.md

{{#selection}}
Concevez l'architecture technique complète pour la fonctionnalité.

**Template à suivre :**
```markdown
# Document de conception - [Nom de la Fonctionnalité]
## Introduction  
## Architecture (extension du système existant)
## Composants et Interfaces
## Modèles de données
## Gestion des erreurs
## Stratégies de test
## Phases d'implémentation
## Performance Considerations
```

**Focus :** Conception technique complète avec interfaces, diagrammes et exemples de code, architecture modulaire.

Créez le fichier `.spec/features/{feature-name}/design.md`.
{{/selection}}

#### C. Création de tasks.md

{{#selection}}
Créez le plan d'implémentation actionnable avec traçabilité complète.

**Template à suivre :**
```markdown
# Plan d'implémentation - [Nom de la Fonctionnalité]
## Phase [N]: [Nom]
- [ ] **T001** - [Action spécifique]
  - Contexte: [Fichiers concernés]
  - _Requirements: [X.Y, Z.W]_
### Jalon Phase [N]
- [Tests à créer]
- [Critères de validation]
**JALON [N]**: [DESCRIPTION] - [ ]
```

**Focus :** Plan actionnable avec tâches T001-TXYZ, traçabilité vers requirements, jalons avec critères.

Créez le fichier `.spec/features/{feature-name}/tasks.md`.
{{/selection}}

### Validation finale des spécifications

{{#selection}}
Vérifiez la cohérence et la complétude des spécifications de la fonctionnalité.

**Checklist de validation :**
- [ ] `requirements.md` - Critères SMART testables, traçabilité claire
- [ ] `design.md` - Conception technique complète, interfaces définies, métriques performance  
- [ ] `tasks.md` - Plan actionnable, traçabilité aux requirements, critères de validation

**Cohérence inter-documents :** Vérifiez l'alignement entre les 3 documents et la complétude de tous les aspects.

La spécification de votre fonctionnalité `{feature-name}` est maintenant complète et prête pour l'implémentation !
{{/selection}}

---

## Outils recommandés

### Outils internes VS Code
- `@workspace` : exploration et recherche de fichiers
- `list_dir` : listage des répertoires  
- `read_file` : examen des fichiers clés
- `semantic_search` : recherche de patterns
- `grep_search` : recherche de termes spécifiques

### Outils MCP pour l'analyse avancée
- `mcp_zen_analyze` : analyse architecturale approfondie
- `mcp_zen_thinkdeep` : réflexion sur les implications
- `mcp_zen_chat` : brainstorming et validation d'idées
- `mcp_zen_consensus` : génération d'idées et solutions
- `mcp_zen_planner` : planification et suivi

### Outils de recherche web
- `vscode-websearchforcopilot_webSearch` : recherches de bonnes pratiques
- `mcp_perplexity_ask` : questions techniques spécifiques  
- `mcp_perplexity_reason` : raisonnement sur les choix techniques

### Outils de documentation
- `mcp_context7_resolve-library-id` : résolution d'identifiants de bibliothèque
- `mcp_context7_get-library-docs` : récupération de documentation

---

## Guide des workflows

### Workflow A - Nouveau projet ou projet analysé
`Section 1` (Détection) → `Section 3` (Définition de Feature)

### Workflow B - Projet existant non analysé  
`Section 1` (Détection) → `Section 2` (Analyse de Contexte) → `Section 3` (Définition de Feature)

Ce chatmode garantit une approche systématique et reproductible pour transformer des besoins utilisateur en spécifications techniques de qualité professionnelle, adaptée à votre contexte spécifique.
