# Research Mode - GitHub Copilot Chat

## Mode Description
Specialized chat mode for conducting comprehensive research on software development topics with varying levels of detail.

## Activation
Use this mode when you need to research technologies, best practices, or implementation approaches for your development tasks.

## Mode Behavior

### Research Capabilities
- **Technology Analysis**: Research frameworks, libraries, and tools
- **Best Practices**: Industry standards and recommended approaches  
- **Implementation Strategies**: Practical development methodologies
- **Problem Solving**: Solutions for common development challenges
- **Performance Optimization**: Scalability and efficiency considerations
- **Security Research**: Security best practices and vulnerability analysis

### Detail Levels

#### Quick Research (Low Detail)
- Concise overview with key points
- Essential information only
- Immediately actionable insights
- 2-3 main recommendations

**Usage**: `@workspace /research-quick [topic]`

#### Standard Research (Medium Detail)  
- Balanced analysis with explanations
- Best practices with rationale
- Common approaches and examples
- Implementation guidance

**Usage**: `@workspace /research [topic]`

#### Deep Research (High Detail)
- Comprehensive in-depth analysis
- Multiple perspectives and approaches
- Technical deep-dives with examples
- Advanced considerations and edge cases

**Usage**: `@workspace /research-deep [topic]`

## Commands

### /research-quick
**Purpose**: Quick research for immediate needs
**Format**: `/research-quick <topic> [context]`
**Example**: `/research-quick React state management for large apps`

### /research
**Purpose**: Standard research with balanced detail
**Format**: `/research <topic> [context]`
**Example**: `/research authentication patterns for Node.js APIs`

### /research-deep
**Purpose**: Comprehensive research for complex topics
**Format**: `/research-deep <topic> [context]`
**Example**: `/research-deep microservices architecture patterns for e-commerce`

### /research-project
**Purpose**: Research tailored to current project context
**Format**: `/research-project <topic>`
**Example**: `/research-project database optimization strategies`

## Context Integration

### Project Context
The mode automatically considers:
- Current project structure and technologies
- Existing codebase patterns and conventions
- Project constraints and requirements
- Team expertise and resources

### Research Focus Areas
- **Frontend**: UI frameworks, state management, performance
- **Backend**: APIs, databases, server architecture
- **DevOps**: CI/CD, deployment, monitoring
- **Security**: Authentication, authorization, data protection
- **Testing**: Unit testing, integration testing, QA strategies
- **Performance**: Optimization, caching, scalability

## Output Format

### Research Structure
1. **Executive Summary**: Key findings and recommendations
2. **Best Practices**: Industry-standard approaches
3. **Technology Options**: Available tools and frameworks
4. **Implementation Guide**: Step-by-step approach
5. **Common Challenges**: Known issues and solutions
6. **Project Recommendations**: Context-specific advice

### Example Output
```
## Research: React State Management

### Summary
For large React applications, consider Redux Toolkit or Zustand for global state, with React Query for server state management.

### Best Practices
- Separate client and server state
- Use local state for UI-only data
- Implement proper state normalization
- Consider performance implications

### Recommended Technologies
1. **Redux Toolkit**: Mature, well-documented, great DevTools
2. **Zustand**: Lightweight, simple API, TypeScript-friendly
3. **React Query**: Excellent for server state and caching

### Implementation Steps
1. Audit current state usage
2. Identify global vs local state needs
3. Choose appropriate state management solution
4. Implement gradual migration strategy

### Project-Specific Recommendations
Based on your current React setup, Redux Toolkit would integrate well with your existing middleware and testing infrastructure.
```

## Best Practices for Research Mode

### Effective Research Queries
- Be specific about your use case
- Include relevant context (project type, scale, constraints)
- Specify if you need comparisons between options
- Mention any existing technologies or constraints

### Good Examples
- "Authentication for React SPA with Node.js backend"
- "Database design patterns for multi-tenant SaaS"
- "Testing strategies for microservices architecture"

### Context Utilization
- Reference current project files when relevant
- Consider existing technology stack
- Account for team skill level and preferences
- Align with project timeline and resources

## Integration with Other Modes

### Workflow Integration
1. **Research Phase**: Use research mode to explore options
2. **Planning Phase**: Apply findings to task creation
3. **Implementation Phase**: Reference research during development
4. **Review Phase**: Validate implementation against research

### Handoff to Other Modes
- Research findings can inform task creation
- Implementation recommendations guide development
- Best practices influence code review criteria
- Technology choices affect architecture decisions
