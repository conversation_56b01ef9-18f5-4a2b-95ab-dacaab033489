{
  "name": "Task Master Mode",
  "description": "Comprehensive task management mode with creation, expansion, and analysis capabilities",
  "version": "1.0.0",
  "author": "Task-Master Adaptation",
  "
": {
    "task-creation": {
      "name": "Task Creation",
      "description": "Create well-structured development tasks",
      "trigger": "/create-task",
      "context": ["project", "files", "git"],
      "variables": {
        "taskDescription": {
          "type": "string",
          "required": true,
          "description": "Description of the task to create"
        },
        "priority": {
          "type": "enum",
          "values": ["high", "medium", "low"],
          "default": "medium",
          "description": "Task priority level"
        },
        "useResearch": {
          "type": "boolean",
          "default": false,
          "description": "Whether to incorporate research findings"
        }
      },
      "behavior": {
        "system": "You are a task creation assistant. Generate structured development tasks with proper dependencies and implementation guidance. Always output valid JSON with title, description, details, testStrategy, and dependencies fields.",
        "rules": [
          "Create actionable, specific tasks",
          "Include logical dependencies",
          "Provide detailed implementation guidance",
          "Include comprehensive test strategies",
          "Consider project context and existing architecture"
        ],
        "outputFormat": {
          "type": "json",
          "schema": {
            "title": "string",
            "description": "string",
            "details": "string",
            "testStrategy": "string",
            "dependencies": "array"
          }
        }
      }
    },
    "task-expansion": {
      "name": "Task Expansion",
      "description": "Break down complex tasks into manageable subtasks",
      "trigger": "/expand-task",
      "context": ["project", "files", "codebase"],
      "variables": {
        "parentTask": {
          "type": "object",
          "required": true,
          "description": "The task to decompose"
        },
        "subtaskCount": {
          "type": "number",
          "required": true,
          "description": "Number of subtasks to generate"
        },
        "expansionApproach": {
          "type": "enum",
          "values": ["technical-layers", "feature-components", "risk-based", "development-phases"],
          "default": "feature-components",
          "description": "Decomposition strategy"
        }
      },
      "behavior": {
        "system": "You are a task decomposition specialist. Break down complex tasks into specific, actionable subtasks with proper dependencies. Always output valid JSON with subtasks array.",
        "rules": [
          "Create subtasks with logical sequence",
          "Ensure complete coverage of parent task",
          "Use proper dependency format (parentId.subtaskNumber)",
          "Provide implementation guidance for each subtask",
          "Include testing strategies for subtasks"
        ],
        "outputFormat": {
          "type": "json",
          "schema": {
            "subtasks": {
              "type": "array",
              "items": {
                "id": "number",
                "title": "string",
                "description": "string",
                "dependencies": "array",
                "details": "string",
                "testStrategy": "string"
              }
            }
          }
        }
      }
    },
    "complexity-analysis": {
      "name": "Complexity Analysis",
      "description": "Analyze task complexity and provide expansion recommendations",
      "trigger": "/analyze-complexity",
      "context": ["project", "files", "codebase"],
      "variables": {
        "task": {
          "type": "object",
          "required": true,
          "description": "The task to analyze"
        },
        "focusAreas": {
          "type": "array",
          "description": "Specific complexity areas to emphasize"
        }
      },
      "behavior": {
        "system": "You are a complexity analysis expert. Analyze tasks across multiple dimensions and provide detailed expansion recommendations. Always output valid JSON with complexity scores and recommendations.",
        "rules": [
          "Score complexity on 1-5 scale across all dimensions",
          "Identify specific complexity factors and risks",
          "Provide justified expansion recommendations",
          "Include implementation guidance and considerations",
          "Consider project context and team capabilities"
        ],
        "outputFormat": {
          "type": "json",
          "schema": {
            "overallComplexity": "number",
            "complexityBreakdown": {
              "technical": "number",
              "implementation": "number",
              "integration": "number",
              "testing": "number",
              "risk": "number"
            },
            "complexityFactors": "array",
            "riskFactors": "array",
            "expansionRecommendation": {
              "shouldExpand": "boolean",
              "recommendedSubtasks": "number",
              "expansionReasoning": "string",
              "expansionPrompt": "string"
            },
            "implementationGuidance": "object"
          }
        }
      }
    },
    "research": {
      "name": "Research Mode with MCP Perplexity",
      "description": "Advanced research with real-time web search through Perplexity MCP integration",
      "trigger": "/research",
      "context": ["project", "web", "files", "mcp"],
      "mcpIntegration": {
        "enabled": true,
        "tools": ["perplexity_ask", "perplexity_research", "perplexity_reason"],
        "models": ["sonar-pro", "sonar-reasoning-pro"],
        "contextAware": true
      },
      "variables": {
        "query": {
          "type": "string",
          "required": true,
          "description": "Research topic or question"
        },
        "detailLevel": {
          "type": "enum",
          "values": ["quick", "standard", "deep", "reasoning"],
          "default": "standard",
          "description": "Depth and type of research required"
        },
        "researchType": {
          "type": "enum",
          "values": ["web-search", "analysis", "comparison", "best-practices", "troubleshooting", "reasoning"],
          "default": "web-search",
          "description": "Type of research to perform"
        },
        "focusAreas": {
          "type": "array",
          "description": "Specific areas to emphasize"
        }
      },
      "behavior": {
        "system": "You are a research specialist. Provide comprehensive, contextual research on software development topics. Adapt depth based on detail level and project context. Always output valid JSON with structured research results.",
        "rules": [
          "Provide accurate, current information",
          "Tailor research to project context and constraints",
          "Include practical implementation guidance",
          "Address common challenges and solutions",
          "Recommend specific technologies and approaches"
        ],
        "outputFormat": {
          "type": "json",
          "schema": {
            "summary": "string",
            "keyFindings": "array",
            "bestPractices": "array",
            "technologies": "array",
            "implementationGuidance": "object",
            "commonChallenges": "array",
            "resources": "array",
            "projectSpecificRecommendations": "array"
          }
        }
      }
    },
    "task-update": {
      "name": "Task Update",
      "description": "Update existing tasks with new information",
      "trigger": "/update-task",
      "context": ["project", "files"],
      "variables": {
        "task": {
          "type": "object",
          "required": true,
          "description": "The task to update"
        },
        "updatePrompt": {
          "type": "string",
          "required": true,
          "description": "Description of changes to apply"
        },
        "appendMode": {
          "type": "boolean",
          "default": false,
          "description": "Whether to append or replace content"
        }
      },
      "behavior": {
        "system": "You are a task update specialist. Update existing tasks with new information while preserving important details. Always output valid JSON with complete updated task.",
        "rules": [
          "Preserve task ID and core intent",
          "Update relevant fields based on update prompt",
          "Maintain logical dependencies",
          "Ensure coherence across all fields",
          "In append mode, preserve existing content"
        ],
        "outputFormat": {
          "type": "json",
          "schema": {
            "id": "number",
            "title": "string",
            "description": "string",
            "details": "string",
            "testStrategy": "string",
            "dependencies": "array",
            "status": "string"
          }
        }
      }
    }
  },
  "workflows": {
    "complete-task-planning": {
      "name": "Complete Task Planning Workflow",
      "description": "End-to-end task planning from research to implementation",
      "steps": [
        {
          "name": "Research",
          "mode": "research",
          "description": "Research best practices and approaches"
        },
        {
          "name": "Create Task",
          "mode": "task-creation",
          "description": "Create main task based on research"
        },
        {
          "name": "Analyze Complexity",
          "mode": "complexity-analysis",
          "description": "Analyze task complexity"
        },
        {
          "name": "Expand if Needed",
          "mode": "task-expansion",
          "description": "Break down complex tasks into subtasks",
          "condition": "complexity > 3"
        }
      ]
    },
    "task-refinement": {
      "name": "Task Refinement Workflow",
      "description": "Iterative task improvement and optimization",
      "steps": [
        {
          "name": "Analyze Current Task",
          "mode": "complexity-analysis",
          "description": "Assess current task complexity and issues"
        },
        {
          "name": "Research Improvements",
          "mode": "research",
          "description": "Research better approaches or solutions"
        },
        {
          "name": "Update Task",
          "mode": "task-update",
          "description": "Apply improvements to task"
        }
      ]
    }
  },
  "shortcuts": {
    "/quick-task": {
      "mode": "task-creation",
      "variables": {
        "priority": "medium",
        "useResearch": false
      }
    },
    "/research-task": {
      "mode": "task-creation",
      "variables": {
        "useResearch": true
      }
    },
    "/quick-research": {
      "mode": "research",
      "variables": {
        "detailLevel": "low"
      }
    },
    "/deep-research": {
      "mode": "research",
      "variables": {
        "detailLevel": "high"
      }
    },
    "/analyze": {
      "mode": "complexity-analysis"
    },
    "/expand": {
      "mode": "task-expansion",
      "variables": {
        "expansionApproach": "feature-components"
      }
    }
  },
  "contextIntegration": {
    "projectContext": {
      "enabled": true,
      "sources": ["package.json", "README.md", ".cursorrules", "src/"],
      "extraction": {
        "techStack": "auto-detect from dependencies",
        "architecture": "analyze directory structure",
        "patterns": "extract from existing code"
      }
    },
    "codebaseContext": {
      "enabled": true,
      "indexing": ["components", "services", "utils", "types"],
      "patterns": ["naming conventions", "file organization", "code style"]
    },
    "gitContext": {
      "enabled": true,
      "sources": ["recent commits", "branch structure", "PR history"],
      "insights": ["development patterns", "team practices", "change frequency"]
    }
  },
  "settings": {
    "defaultDetailLevel": "medium",
    "autoContextRefresh": true,
    "contextRefreshInterval": "1h",
    "outputValidation": true,
    "errorHandling": "graceful",
    "logging": {
      "enabled": true,
      "level": "info",
      "includeContext": true
    }
  }
}
