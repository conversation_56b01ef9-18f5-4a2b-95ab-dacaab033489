# Cursor Performance Optimization Rules

## Rule Configuration
```yaml
name: "cursor-optimizations"
description: "Performance-optimized rules leveraging Cursor's native capabilities"
priority: high
context: ["project", "files", "cache", "index"]
performance_mode: "optimized"
```

## Core Optimization Principles

### Leverage Cursor's Native Capabilities
- **Intelligent Caching**: Utilize Cursor's built-in caching for repeated operations
- **Index Optimization**: Leverage Cursor's codebase indexing for fast symbol lookup
- **Context Reuse**: Minimize context regeneration through smart context management
- **Incremental Processing**: Use Cursor's incremental analysis capabilities
- **Memory Efficiency**: Optimize memory usage through selective context loading

## Performance-Optimized Variables
```yaml
variables:
  CACHE_STRATEGY:
    type: enum
    values: ["aggressive", "balanced", "minimal"]
    default: "balanced"
    description: "Caching strategy for performance optimization"
  
  CONTEXT_SCOPE:
    type: enum
    values: ["file", "directory", "project", "workspace"]
    default: "directory"
    description: "Scope of context analysis for performance balance"
  
  INDEX_UTILIZATION:
    type: boolean
    default: true
    description: "Whether to leverage Cursor's symbol indexing"
  
  INCREMENTAL_MODE:
    type: boolean
    default: true
    description: "Enable incremental processing for large operations"
```

## Caching Strategies

### Aggressive Caching
```yaml
condition: CACHE_STRATEGY == "aggressive"
behavior: |
  - Cache all research results for 24 hours
  - Reuse project context across sessions
  - Cache dependency analysis results
  - Store code pattern analysis for reuse
  - Maintain persistent task dependency graphs
```

### Balanced Caching
```yaml
condition: CACHE_STRATEGY == "balanced"
behavior: |
  - Cache research results for 4 hours
  - Refresh project context every 2 hours
  - Cache frequently accessed code patterns
  - Invalidate cache on significant project changes
  - Balance memory usage with performance gains
```

### Minimal Caching
```yaml
condition: CACHE_STRATEGY == "minimal"
behavior: |
  - Cache only essential data for current session
  - Refresh context on each major operation
  - Prioritize accuracy over performance
  - Use minimal memory footprint
  - Suitable for resource-constrained environments
```

## Index Optimization

### Symbol Index Utilization
```yaml
when: INDEX_UTILIZATION == true
optimizations:
  - Use Cursor's symbol index for dependency analysis
  - Leverage indexed function/class relationships
  - Utilize cached type information for TypeScript projects
  - Access pre-computed import/export relationships
  - Reuse indexed code pattern analysis
```

### Smart Index Queries
```yaml
query_optimization:
  symbol_lookup:
    strategy: "Use indexed symbols before full-text search"
    fallback: "Full codebase scan if index incomplete"
    
  dependency_analysis:
    strategy: "Leverage pre-computed dependency graphs"
    refresh_trigger: "Package.json or import changes detected"
    
  pattern_detection:
    strategy: "Use cached pattern analysis from index"
    update_frequency: "On significant code structure changes"
```

## Context Optimization

### Selective Context Loading
```yaml
context_loading:
  file_level:
    load: ["current file", "direct imports", "direct exports"]
    skip: ["unrelated files", "test files", "build artifacts"]
    
  directory_level:
    load: ["current directory", "parent directory", "key subdirectories"]
    skip: ["node_modules", "build", "dist", ".git"]
    
  project_level:
    load: ["package.json", "tsconfig.json", "main entry points"]
    skip: ["large data files", "generated files", "temporary files"]
```

### Dynamic Context Adjustment
```yaml
context_adjustment:
  small_projects:
    strategy: "Load full project context"
    threshold: "< 100 files"
    
  medium_projects:
    strategy: "Load relevant directories and key files"
    threshold: "100-1000 files"
    
  large_projects:
    strategy: "Load current scope with intelligent expansion"
    threshold: "> 1000 files"
```

## Incremental Processing

### Task Creation Optimization
```yaml
when: operation == "task_creation"
optimizations:
  - Reuse existing project analysis
  - Cache dependency relationship mappings
  - Utilize pre-computed complexity metrics
  - Leverage cached research results for similar tasks
  - Incremental validation instead of full re-validation
```

### Research Optimization
```yaml
when: operation == "research"
optimizations:
  - Cache research results by topic and project context
  - Reuse technology stack analysis
  - Leverage cached best practices for known technologies
  - Incremental updates to research findings
  - Smart deduplication of research sources
```

### Complexity Analysis Optimization
```yaml
when: operation == "complexity_analysis"
optimizations:
  - Cache complexity scoring algorithms
  - Reuse architectural pattern analysis
  - Leverage pre-computed code metrics
  - Incremental complexity updates for task modifications
  - Cached risk assessment patterns
```

## Memory Management

### Efficient Data Structures
```yaml
memory_optimization:
  task_storage:
    structure: "Lightweight task objects with lazy loading"
    indexing: "Hash-based lookup for O(1) access"
    
  context_storage:
    structure: "Hierarchical context with selective loading"
    compression: "Compress inactive context data"
    
  cache_management:
    strategy: "LRU eviction with priority weighting"
    limits: "Configurable memory thresholds"
```

### Garbage Collection Optimization
```yaml
gc_optimization:
  automatic_cleanup:
    - Remove unused cached research after 24 hours
    - Clear temporary analysis data after operations
    - Compress long-term storage data
    - Periodic memory usage assessment
    
  manual_cleanup:
    - User-triggered cache clearing
    - Project context reset on major changes
    - Selective cache invalidation
    - Memory usage reporting and optimization suggestions
```

## Performance Monitoring

### Metrics Collection
```yaml
performance_metrics:
  operation_timing:
    - Task creation time
    - Research completion time
    - Context loading time
    - Cache hit/miss ratios
    
  resource_usage:
    - Memory consumption
    - Cache storage size
    - Index utilization rates
    - Context loading efficiency
    
  user_experience:
    - Response time percentiles
    - Operation success rates
    - Error frequency and types
    - User satisfaction indicators
```

### Performance Thresholds
```yaml
performance_thresholds:
  acceptable:
    task_creation: "< 3 seconds"
    research_standard: "< 10 seconds"
    research_deep: "< 30 seconds"
    context_loading: "< 1 second"
    
  warning:
    task_creation: "3-5 seconds"
    research_standard: "10-20 seconds"
    research_deep: "30-60 seconds"
    context_loading: "1-3 seconds"
    
  critical:
    task_creation: "> 5 seconds"
    research_standard: "> 20 seconds"
    research_deep: "> 60 seconds"
    context_loading: "> 3 seconds"
```

## Adaptive Optimization

### Dynamic Performance Adjustment
```yaml
adaptive_behavior:
  high_performance_mode:
    trigger: "User requests fast responses"
    adjustments:
      - Reduce research depth
      - Use cached results aggressively
      - Limit context scope
      - Prioritize speed over comprehensiveness
      
  balanced_mode:
    trigger: "Default operation mode"
    adjustments:
      - Standard research depth
      - Balanced caching strategy
      - Appropriate context scope
      - Balance speed and quality
      
  quality_mode:
    trigger: "User requests comprehensive analysis"
    adjustments:
      - Maximum research depth
      - Fresh data over cached results
      - Full context analysis
      - Prioritize quality over speed
```

### Resource-Aware Scaling
```yaml
resource_scaling:
  low_memory:
    threshold: "< 1GB available"
    adjustments:
      - Minimal caching
      - Reduced context scope
      - Aggressive garbage collection
      - Simplified analysis algorithms
      
  normal_memory:
    threshold: "1-4GB available"
    adjustments:
      - Balanced caching
      - Standard context scope
      - Regular garbage collection
      - Full feature set
      
  high_memory:
    threshold: "> 4GB available"
    adjustments:
      - Aggressive caching
      - Extended context scope
      - Lazy garbage collection
      - Enhanced analysis features
```

## Error Handling and Fallbacks

### Performance Degradation Handling
```yaml
degradation_handling:
  cache_miss:
    strategy: "Graceful fallback to fresh computation"
    optimization: "Background cache warming"
    
  index_unavailable:
    strategy: "Fall back to direct file analysis"
    optimization: "Request index rebuild"
    
  memory_pressure:
    strategy: "Reduce context scope and clear caches"
    optimization: "Suggest performance mode adjustment"
    
  timeout_handling:
    strategy: "Return partial results with continuation option"
    optimization: "Break operation into smaller chunks"
```

### Recovery Strategies
```yaml
recovery_strategies:
  cache_corruption:
    action: "Clear affected cache and rebuild"
    prevention: "Checksum validation for cache entries"
    
  index_corruption:
    action: "Request full index rebuild"
    prevention: "Incremental index validation"
    
  memory_leak:
    action: "Force garbage collection and cache clearing"
    prevention: "Regular memory usage monitoring"
    
  performance_regression:
    action: "Reset to default performance settings"
    prevention: "Performance baseline tracking"
```

## Configuration and Tuning

### User-Configurable Settings
```yaml
user_settings:
  performance_profile:
    options: ["speed", "balanced", "quality"]
    default: "balanced"
    
  cache_size_limit:
    options: ["small", "medium", "large", "unlimited"]
    default: "medium"
    
  context_scope_preference:
    options: ["minimal", "standard", "comprehensive"]
    default: "standard"
    
  auto_optimization:
    options: [true, false]
    default: true
```

### Advanced Tuning Parameters
```yaml
advanced_tuning:
  cache_ttl:
    research_results: "4h"
    project_context: "2h"
    code_analysis: "1h"
    
  batch_sizes:
    file_processing: 50
    symbol_analysis: 100
    dependency_resolution: 25
    
  concurrency_limits:
    parallel_research: 3
    concurrent_analysis: 2
    background_tasks: 5
```
