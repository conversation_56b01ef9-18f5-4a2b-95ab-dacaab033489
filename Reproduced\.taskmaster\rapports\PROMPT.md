OBJECTIF :
Reproduire le traitement effectué par Task-Master avec d'autres outils en adaptant les prompts du répertoire @c:\Users\<USER>\Documents\GitHub/Reproduced\.taskmaster\taskmaster\prompts/

---

Les outils avec lesquels reproduire le traitement de Task-Master sont les suivants, classés par catégories:

IDE IA :
- GitHub Copilot (Personnalisation par : Création de fichiers d'instructions, de prompts ou de Chatmo<PERSON>)
- Cursor (Personnalisation par : Création de fichiers d'instructions ou de Modes Personnalisés)
- Trae (Personnalisation par : Création de fichiers d'instructions ou de Modes Personnalisés)

Extensions VS Code IA :
- RooCode / Kilocode (Personnalisation par : Création de Modes Personnalisés)
- Cline (Personnalisation par : Création de fichiers d'instructions)

Outils IA en ligne de commande :
- Claude-Code (Personnalisation par : Création de fichiers d'instructions, de commandes slash ou de sous-agents )
- Gemini-CLI (Personnalisation par : Création de fichiers d'instructions)

Autres outils spécialisés :
- Traycer : Extension VS Code de génération de plan de développement complet ou par phase par IA
- Perplexity MCP : Serveur MCP exposant 3 outils de recherche via les modèles Sonar (recherche web, recherche avec réflexion, recherche approfondie)
- Zen-MCP-Server : Serveur MCP exposant de nombreux outils et modèles de LLM permettant de booster les capacités de l'agent IA les utilisant

---

Task-Master est un outil de gestion de tâches et de projets qui utilise des modèles de langage pour automatiser et optimiser les flux de travail. Il est conçu pour aider les utilisateurs à organiser, prioriser et exécuter leurs tâches de manière efficace.
La configuration se fait avec 3 modèles de LLM : un modèle principal, un modèle de recherche et un modèle secondaire de secours.

Ma configuration actuelle est la suivante :
- Modèle principal : gemini-2.5-pro-preview-05-06
- Modèle de recherche : sonar-pro
- Modèle secondaire de secours : gemini-2.5-fash

Ces modèles sont accessibles dans tous les outils mentionnés ci-dessus (sonar et ses variantes via le serveur MCP Perplexity) et il est possible de les remplacer par d'autres modèles selon les besoins.

---

Les prompts de Task-Master sont organisés en plusieurs catégories, chacune correspondant à une fonctionnalité ou un aspect spécifique du traitement des tâches.
Ils sont efficaces pour guider les modèles de langage dans la génération de réponses pertinentes et adaptées au contexte des tâches à accomplir, nous n'avons pas besoin de les modifier mais juste de les adapter pour chaque outil et de gérer l'injection de variables.

---

ANALYSEZ les prompts et créez un rapport détaillé dans AUGMENT.md sur les adaptations nécessaires pour chaque outil, en tenant compte des spécificités de chaque plateforme et de la manière dont elles gèrent les prompts et les instructions.