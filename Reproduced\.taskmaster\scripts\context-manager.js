/**
 * Context Manager for GitHub Copilot Integration
 * Extracts and manages project context for Task-Master adaptations
 */

const fs = require('fs');
const path = require('path');

class ProjectContextManager {
    constructor(projectRoot = process.cwd()) {
        this.projectRoot = projectRoot;
        this.contextDir = path.join(projectRoot, '.github', 'context');
        this.ensureContextDirectory();
    }

    ensureContextDirectory() {
        if (!fs.existsSync(this.contextDir)) {
            fs.mkdirSync(this.contextDir, { recursive: true });
        }
    }

    /**
     * Extract technology stack from package.json and other config files
     */
    extractTechStack() {
        const techStack = {
            frontend: [],
            backend: [],
            database: [],
            testing: [],
            build: [],
            deployment: []
        };

        // Analyze package.json
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const allDeps = {
                ...packageJson.dependencies,
                ...packageJson.devDependencies
            };

            // Categorize dependencies
            Object.keys(allDeps).forEach(dep => {
                if (['react', 'vue', 'angular', '@angular/core'].includes(dep)) {
                    techStack.frontend.push(dep);
                } else if (['express', 'fastify', 'koa', 'nestjs'].includes(dep)) {
                    techStack.backend.push(dep);
                } else if (['mongoose', 'prisma', 'typeorm', 'sequelize'].includes(dep)) {
                    techStack.database.push(dep);
                } else if (['jest', 'mocha', 'cypress', 'playwright'].includes(dep)) {
                    techStack.testing.push(dep);
                } else if (['webpack', 'vite', 'rollup', 'parcel'].includes(dep)) {
                    techStack.build.push(dep);
                }
            });
        }

        return techStack;
    }

    /**
     * Extract project structure and organization
     */
    extractProjectStructure() {
        const structure = {
            type: this.detectProjectType(),
            architecture: this.detectArchitecture(),
            directories: this.getDirectoryStructure(),
            entryPoints: this.findEntryPoints()
        };

        return structure;
    }

    detectProjectType() {
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            if (packageJson.dependencies?.react) return 'react-app';
            if (packageJson.dependencies?.vue) return 'vue-app';
            if (packageJson.dependencies?.angular) return 'angular-app';
            if (packageJson.dependencies?.express) return 'node-api';
            if (packageJson.dependencies?.next) return 'nextjs-app';
        }

        // Check for other indicators
        if (fs.existsSync(path.join(this.projectRoot, 'src', 'main.ts'))) return 'typescript-app';
        if (fs.existsSync(path.join(this.projectRoot, 'public', 'index.html'))) return 'web-app';
        
        return 'unknown';
    }

    detectArchitecture() {
        const dirs = fs.readdirSync(this.projectRoot, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        if (dirs.includes('services') && dirs.includes('packages')) return 'microservices';
        if (dirs.includes('apps') && dirs.includes('libs')) return 'monorepo';
        if (dirs.includes('src') && dirs.includes('public')) return 'spa';
        if (dirs.includes('pages') && dirs.includes('components')) return 'pages-based';
        
        return 'monolithic';
    }

    getDirectoryStructure(dir = this.projectRoot, depth = 0, maxDepth = 3) {
        if (depth > maxDepth) return {};

        const structure = {};
        const items = fs.readdirSync(dir, { withFileTypes: true });

        items.forEach(item => {
            if (item.name.startsWith('.') && item.name !== '.github') return;
            if (item.name === 'node_modules') return;

            if (item.isDirectory()) {
                structure[item.name] = this.getDirectoryStructure(
                    path.join(dir, item.name),
                    depth + 1,
                    maxDepth
                );
            } else {
                if (!structure._files) structure._files = [];
                structure._files.push(item.name);
            }
        });

        return structure;
    }

    findEntryPoints() {
        const entryPoints = [];
        const commonEntries = [
            'src/index.js', 'src/index.ts', 'src/main.js', 'src/main.ts',
            'index.js', 'index.ts', 'app.js', 'app.ts', 'server.js', 'server.ts'
        ];

        commonEntries.forEach(entry => {
            if (fs.existsSync(path.join(this.projectRoot, entry))) {
                entryPoints.push(entry);
            }
        });

        return entryPoints;
    }

    /**
     * Extract code patterns and conventions
     */
    extractCodePatterns() {
        const patterns = {
            naming: this.analyzeNamingConventions(),
            components: this.analyzeComponentPatterns(),
            api: this.analyzeApiPatterns(),
            testing: this.analyzeTestingPatterns()
        };

        return patterns;
    }

    analyzeNamingConventions() {
        // Analyze file names to detect patterns
        const files = this.getAllFiles('.js', '.ts', '.jsx', '.tsx');
        const conventions = {
            files: 'unknown',
            components: 'unknown',
            functions: 'unknown'
        };

        // Simple heuristics for naming conventions
        const camelCaseFiles = files.filter(f => /^[a-z][a-zA-Z0-9]*\.(js|ts|jsx|tsx)$/.test(path.basename(f)));
        const kebabCaseFiles = files.filter(f => /^[a-z][a-z0-9-]*\.(js|ts|jsx|tsx)$/.test(path.basename(f)));
        const pascalCaseFiles = files.filter(f => /^[A-Z][a-zA-Z0-9]*\.(js|ts|jsx|tsx)$/.test(path.basename(f)));

        if (pascalCaseFiles.length > camelCaseFiles.length && pascalCaseFiles.length > kebabCaseFiles.length) {
            conventions.files = 'PascalCase';
        } else if (kebabCaseFiles.length > camelCaseFiles.length) {
            conventions.files = 'kebab-case';
        } else {
            conventions.files = 'camelCase';
        }

        return conventions;
    }

    analyzeComponentPatterns() {
        // Look for React/Vue component patterns
        const componentFiles = this.getAllFiles('.jsx', '.tsx', '.vue');
        const patterns = {
            type: 'unknown',
            structure: 'unknown'
        };

        if (componentFiles.length > 0) {
            // Sample a few files to detect patterns
            const sampleFile = componentFiles[0];
            const content = fs.readFileSync(sampleFile, 'utf8');

            if (content.includes('function ') && content.includes('export default')) {
                patterns.type = 'functional-components';
            } else if (content.includes('class ') && content.includes('extends')) {
                patterns.type = 'class-components';
            }

            if (content.includes('useState') || content.includes('useEffect')) {
                patterns.structure = 'hooks-based';
            }
        }

        return patterns;
    }

    analyzeApiPatterns() {
        // Look for API patterns in the codebase
        const apiFiles = this.getAllFiles('.js', '.ts').filter(f => 
            f.includes('api') || f.includes('service') || f.includes('controller')
        );

        const patterns = {
            style: 'unknown',
            framework: 'unknown'
        };

        if (apiFiles.length > 0) {
            const content = fs.readFileSync(apiFiles[0], 'utf8');
            
            if (content.includes('app.get') || content.includes('app.post')) {
                patterns.framework = 'express';
                patterns.style = 'rest';
            } else if (content.includes('GraphQL') || content.includes('resolver')) {
                patterns.style = 'graphql';
            }
        }

        return patterns;
    }

    analyzeTestingPatterns() {
        const testFiles = this.getAllFiles('.test.js', '.test.ts', '.spec.js', '.spec.ts');
        const patterns = {
            framework: 'unknown',
            style: 'unknown'
        };

        if (testFiles.length > 0) {
            const content = fs.readFileSync(testFiles[0], 'utf8');
            
            if (content.includes('describe') && content.includes('it')) {
                patterns.framework = 'jest';
                patterns.style = 'bdd';
            } else if (content.includes('test(')) {
                patterns.framework = 'jest';
                patterns.style = 'simple';
            }
        }

        return patterns;
    }

    getAllFiles(...extensions) {
        const files = [];
        
        const walkDir = (dir) => {
            const items = fs.readdirSync(dir, { withFileTypes: true });
            
            items.forEach(item => {
                if (item.name.startsWith('.') || item.name === 'node_modules') return;
                
                const fullPath = path.join(dir, item.name);
                
                if (item.isDirectory()) {
                    walkDir(fullPath);
                } else if (extensions.some(ext => item.name.endsWith(ext))) {
                    files.push(fullPath);
                }
            });
        };

        walkDir(this.projectRoot);
        return files;
    }

    /**
     * Generate complete project context
     */
    generateContext() {
        const context = {
            timestamp: new Date().toISOString(),
            project: {
                name: this.getProjectName(),
                version: this.getProjectVersion(),
                description: this.getProjectDescription()
            },
            techStack: this.extractTechStack(),
            structure: this.extractProjectStructure(),
            patterns: this.extractCodePatterns(),
            stats: this.getProjectStats()
        };

        return context;
    }

    getProjectName() {
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            return packageJson.name || path.basename(this.projectRoot);
        }
        return path.basename(this.projectRoot);
    }

    getProjectVersion() {
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            return packageJson.version || '1.0.0';
        }
        return '1.0.0';
    }

    getProjectDescription() {
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            return packageJson.description || 'No description available';
        }
        return 'No description available';
    }

    getProjectStats() {
        const allFiles = this.getAllFiles('.js', '.ts', '.jsx', '.tsx', '.vue', '.css', '.scss', '.md');
        return {
            totalFiles: allFiles.length,
            codeFiles: allFiles.filter(f => !f.includes('test') && !f.includes('spec')).length,
            testFiles: allFiles.filter(f => f.includes('test') || f.includes('spec')).length,
            lastModified: new Date().toISOString()
        };
    }

    /**
     * Save context to file
     */
    saveContext() {
        const context = this.generateContext();
        const contextPath = path.join(this.contextDir, 'current-context.json');
        fs.writeFileSync(contextPath, JSON.stringify(context, null, 2));
        
        // Also create a markdown version for easy reading
        const markdownPath = path.join(this.contextDir, 'current-context.md');
        const markdown = this.formatContextAsMarkdown(context);
        fs.writeFileSync(markdownPath, markdown);
        
        console.log(`Context saved to ${contextPath} and ${markdownPath}`);
        return context;
    }

    formatContextAsMarkdown(context) {
        return `# Project Context

## Project Information
- **Name**: ${context.project.name}
- **Version**: ${context.project.version}
- **Description**: ${context.project.description}
- **Last Updated**: ${context.timestamp}

## Technology Stack
### Frontend
${context.techStack.frontend.map(tech => `- ${tech}`).join('\n')}

### Backend
${context.techStack.backend.map(tech => `- ${tech}`).join('\n')}

### Database
${context.techStack.database.map(tech => `- ${tech}`).join('\n')}

### Testing
${context.techStack.testing.map(tech => `- ${tech}`).join('\n')}

## Project Structure
- **Type**: ${context.structure.type}
- **Architecture**: ${context.structure.architecture}
- **Entry Points**: ${context.structure.entryPoints.join(', ')}

## Code Patterns
- **File Naming**: ${context.patterns.naming.files}
- **Component Type**: ${context.patterns.components.type}
- **Component Structure**: ${context.patterns.components.structure}
- **API Style**: ${context.patterns.api.style}
- **API Framework**: ${context.patterns.api.framework}
- **Testing Framework**: ${context.patterns.testing.framework}

## Project Statistics
- **Total Files**: ${context.stats.totalFiles}
- **Code Files**: ${context.stats.codeFiles}
- **Test Files**: ${context.stats.testFiles}
`;
    }

    /**
     * Load existing context
     */
    loadContext() {
        const contextPath = path.join(this.contextDir, 'current-context.json');
        if (fs.existsSync(contextPath)) {
            return JSON.parse(fs.readFileSync(contextPath, 'utf8'));
        }
        return null;
    }
}

module.exports = ProjectContextManager;

// CLI usage
if (require.main === module) {
    const manager = new ProjectContextManager();
    const command = process.argv[2];

    switch (command) {
        case 'generate':
            console.log('Generating project context...');
            manager.saveContext();
            break;
        case 'show':
            const context = manager.loadContext();
            if (context) {
                console.log(JSON.stringify(context, null, 2));
            } else {
                console.log('No context found. Run "generate" first.');
            }
            break;
        default:
            console.log('Usage: node context-manager.js [generate|show]');
    }
}
