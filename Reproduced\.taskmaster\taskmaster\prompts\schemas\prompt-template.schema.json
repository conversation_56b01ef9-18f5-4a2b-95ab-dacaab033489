{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://github.com/eyaltoledano/claude-task-master/blob/main/src/prompts/schemas/prompt-template.schema.json", "version": "1.0.0", "title": "<PERSON><PERSON><PERSON><PERSON> de prompt Task Master", "description": "Schéma pour les fichiers de modèles de prompt AI Task Master", "type": "object", "required": ["id", "version", "description", "prompts"], "properties": {"id": {"type": "string", "pattern": "^[a-z0-9-]+$", "description": "Identifiant unique pour le modèle de prompt"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Version sémantique du modèle de prompt"}, "description": {"type": "string", "minLength": 1, "description": "Brève description de ce que fait ce prompt"}, "metadata": {"$ref": "#/definitions/metadata"}, "parameters": {"type": "object", "additionalProperties": {"$ref": "#/definitions/parameter"}}, "prompts": {"type": "object", "properties": {"default": {"$ref": "#/definitions/promptVariant"}}, "additionalProperties": {"$ref": "#/definitions/conditionalPromptVariant"}}}, "definitions": {"parameter": {"type": "object", "required": ["type", "description"], "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"]}, "description": {"type": "string", "minLength": 1}, "required": {"type": "boolean", "default": false}, "default": {"description": "Valeur par défaut pour les paramètres optionnels"}, "enum": {"type": "array", "description": "Valeurs valides pour les paramètres de type chaîne"}, "pattern": {"type": "string", "description": "Expression régulière pour la validation des chaînes"}, "minimum": {"type": "number", "description": "Valeur minimale pour les paramètres numériques"}, "maximum": {"type": "number", "description": "Valeur maximale pour les paramètres numériques"}}}, "promptVariant": {"type": "object", "required": ["system", "user"], "properties": {"system": {"type": "string", "minLength": 1}, "user": {"type": "string", "minLength": 1}}}, "conditionalPromptVariant": {"allOf": [{"$ref": "#/definitions/promptVariant"}, {"type": "object", "properties": {"condition": {"type": "string", "description": "Expression JavaScript pour la sélection de variante"}}}]}, "metadata": {"type": "object", "properties": {"author": {"type": "string"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "category": {"type": "string", "enum": ["tâche", "analyse", "recherche", "parsing", "mise à jour", "expansion"]}}}}}