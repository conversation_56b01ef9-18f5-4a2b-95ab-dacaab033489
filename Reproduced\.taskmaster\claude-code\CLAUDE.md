# Claude-Code Task-Master Integration

## Overview
This document defines the Claude-Code integration for Task-Master capabilities, providing a comprehensive task management system through specialized agents and slash commands.

## Agent Architecture

### Core Agents
1. **Task Creator Agent** (`task-creator-agent.md`)
   - Primary responsibility: Create well-structured development tasks
   - Capabilities: Task creation, dependency analysis, research integration
   - Context awareness: Project structure, technology stack, team capabilities

2. **Research Agent** (`research-agent.md`)
   - Primary responsibility: Comprehensive software development research
   - Capabilities: Technology analysis, best practices, implementation strategies
   - Context awareness: Project constraints, industry trends, security considerations

3. **Task Expansion Agent** (`task-expansion-agent.md`)
   - Primary responsibility: Break down complex tasks into manageable subtasks
   - Capabilities: Task decomposition, dependency mapping, complexity analysis
   - Context awareness: Implementation patterns, team capacity, project timeline

4. **Complexity Analyzer Agent** (`complexity-analyzer-agent.md`)
   - Primary responsibility: Analyze task complexity and provide recommendations
   - Capabilities: Multi-dimensional complexity scoring, risk assessment, expansion guidance
   - Context awareness: Technical complexity, implementation challenges, team expertise

## Slash Commands

### Task Management Commands
```
/create-task [description] [priority?] [research?]
  - Creates a new structured task
  - Optional priority: high, medium, low
  - Optional research flag for enhanced task creation
  - Example: /create-task "Implement user authentication" high research

/expand-task [task-id] [subtask-count] [approach?]
  - Expands a complex task into subtasks
  - Approach options: technical-layers, feature-components, risk-based, development-phases
  - Example: /expand-task 5 4 feature-components

/analyze-complexity [task-description]
  - Analyzes task complexity across multiple dimensions
  - Provides expansion recommendations
  - Example: /analyze-complexity "Migrate to microservices architecture"

/update-task [task-id] [update-description] [append?]
  - Updates an existing task with new information
  - Optional append mode to preserve existing content
  - Example: /update-task 3 "Add OAuth integration" append
```

### Research Commands
```
/research [topic] [detail-level?] [focus-areas?]
  - Conducts comprehensive research on development topics
  - Detail levels: quick, standard, deep
  - Focus areas: security, performance, scalability, etc.
  - Example: /research "React state management" deep performance

/compare [option1] [option2] [criteria?]
  - Comparative analysis of technologies or approaches
  - Optional criteria for focused comparison
  - Example: /compare "PostgreSQL" "MongoDB" "e-commerce application"

/best-practices [technology] [context?]
  - Research best practices for specific technologies
  - Optional context for targeted recommendations
  - Example: /best-practices "Node.js API" "high-traffic application"
```

### Workflow Commands
```
/plan-feature [feature-description]
  - Complete feature planning workflow
  - Combines research, task creation, and complexity analysis
  - Example: /plan-feature "Real-time chat system"

/review-task [task-id]
  - Comprehensive task review and optimization
  - Analyzes complexity, dependencies, and implementation approach
  - Example: /review-task 7

/project-analysis
  - Analyzes current project structure and provides recommendations
  - Identifies technical debt and improvement opportunities
  - Example: /project-analysis
```

## Agent Coordination

### Inter-Agent Communication
```yaml
coordination_patterns:
  research_enhanced_creation:
    trigger: "research flag in task creation"
    flow: "task-creator → research-agent → task-creator"
    result: "Enhanced task with research insights"
  
  complexity_driven_expansion:
    trigger: "high complexity score"
    flow: "complexity-analyzer → task-expansion-agent"
    result: "Automatic task decomposition recommendation"
  
  iterative_refinement:
    trigger: "task update or review"
    flow: "complexity-analyzer → research-agent → task-creator"
    result: "Optimized task with current best practices"
```

### Context Sharing
- **Project Context**: Shared across all agents for consistent recommendations
- **Task Dependencies**: Maintained globally for accurate dependency analysis
- **Research Cache**: Shared research findings to avoid redundant research
- **User Preferences**: Consistent behavior based on user preferences and project patterns

## Configuration

### Agent Settings
```yaml
task_creator:
  default_priority: "medium"
  auto_research: false
  dependency_analysis: true
  context_integration: true

research_agent:
  default_detail_level: "standard"
  cache_duration: "24h"
  source_verification: true
  bias_detection: true

task_expansion:
  default_approach: "feature-components"
  max_subtasks: 8
  min_subtasks: 2
  auto_dependency_mapping: true

complexity_analyzer:
  expansion_threshold: 3
  risk_assessment: true
  team_capability_consideration: true
  timeline_impact_analysis: true
```

### Project Integration
```yaml
context_sources:
  - package.json
  - README.md
  - src/ directory structure
  - .env configuration
  - git history and branches
  - existing documentation

auto_detection:
  - technology stack
  - architecture patterns
  - coding conventions
  - testing strategies
  - deployment patterns
```

## Usage Workflows

### Feature Development Workflow
1. **Research Phase**: `/research "feature requirements" standard`
2. **Planning Phase**: `/create-task "feature description" medium research`
3. **Complexity Analysis**: `/analyze-complexity "task description"`
4. **Decomposition**: `/expand-task [id] [count]` (if complex)
5. **Implementation**: Execute tasks with agent guidance
6. **Review**: `/review-task [id]` for optimization

### Problem Solving Workflow
1. **Problem Analysis**: `/research "problem domain" deep`
2. **Solution Comparison**: `/compare "solution1" "solution2"`
3. **Implementation Planning**: `/create-task "solution implementation"`
4. **Risk Assessment**: `/analyze-complexity "implementation task"`
5. **Execution Planning**: `/expand-task [id] [count]` if needed

### Project Optimization Workflow
1. **Project Analysis**: `/project-analysis`
2. **Best Practices Research**: `/best-practices "current-tech-stack"`
3. **Improvement Planning**: `/create-task "optimization task"`
4. **Impact Analysis**: `/analyze-complexity "optimization task"`
5. **Implementation Strategy**: `/expand-task [id] [count]`

## Quality Assurance

### Output Validation
- **JSON Structure**: All outputs validated against defined schemas
- **Content Quality**: Automated checks for completeness and relevance
- **Context Consistency**: Ensures recommendations align with project context
- **Dependency Validation**: Verifies logical dependency chains

### Error Handling
- **Graceful Degradation**: Continues operation with reduced functionality if context unavailable
- **User Guidance**: Provides clear error messages and suggested corrections
- **Fallback Modes**: Alternative approaches when primary methods fail
- **Recovery Strategies**: Automatic retry with adjusted parameters

## Integration Benefits

### Enhanced Productivity
- **Structured Approach**: Consistent task creation and management
- **Research Integration**: Informed decisions based on current best practices
- **Complexity Management**: Appropriate task decomposition and risk assessment
- **Context Awareness**: Recommendations tailored to specific project needs

### Quality Improvement
- **Best Practices**: Automatic incorporation of industry standards
- **Risk Mitigation**: Early identification of potential challenges
- **Dependency Management**: Logical task sequencing and prerequisite identification
- **Continuous Learning**: Adaptation based on project patterns and outcomes

### Team Collaboration
- **Consistent Standards**: Uniform task structure and quality across team
- **Knowledge Sharing**: Research findings and best practices accessible to all
- **Clear Communication**: Structured task descriptions and implementation guidance
- **Progress Tracking**: Clear dependencies and completion criteria

## Customization

### Project-Specific Adaptations
- **Technology Stack**: Automatic adaptation to project technologies
- **Team Preferences**: Learning from user interactions and preferences
- **Complexity Thresholds**: Adjustable based on team capabilities
- **Research Depth**: Configurable based on project requirements

### Extension Points
- **Custom Agents**: Framework for adding specialized agents
- **Additional Commands**: Easy addition of project-specific slash commands
- **Integration Hooks**: Connection points for external tools and services
- **Workflow Customization**: Configurable agent coordination patterns

## Getting Started

### Initial Setup
1. Configure project context in Claude-Code settings
2. Review and adjust agent configurations
3. Test basic commands with sample tasks
4. Customize workflows based on team needs

### Best Practices
- Start with simple task creation to understand output format
- Use research commands for unfamiliar technologies
- Leverage complexity analysis for large features
- Establish team conventions for task structure and dependencies

### Troubleshooting
- Check project context configuration if recommendations seem off-target
- Verify agent settings if output format is unexpected
- Use `/project-analysis` to validate project understanding
- Consult agent documentation for detailed behavior explanations
