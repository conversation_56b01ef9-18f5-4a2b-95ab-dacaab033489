#!/usr/bin/env node

/**
 * Unit Tests for Prompt Conversion Utilities
 * Tests the conversion of Task-Master prompts to different tool formats
 */

const fs = require('fs').promises;
const path = require('path');
const assert = require('assert');

// Import conversion utilities
const { convertHandlebarsToSimple, convertForGitHubCopilot, convertForCursor, convertForClaudeCode } = require('../../scripts/convert-handlebars');

class PromptConversionTests {
  constructor() {
    this.testResults = [];
    this.samplePrompts = {};
  }

  async runAllTests() {
    console.log('🧪 Running Prompt Conversion Unit Tests\n');

    try {
      // Load sample prompts
      await this.loadSamplePrompts();

      // Run conversion tests
      await this.testHandlebarsToSimpleConversion();
      await this.testGitHubCopilotConversion();
      await this.testCursorConversion();
      await this.testClaudeCodeConversion();
      await this.testConditionalLogicConversion();
      await this.testHelperFunctionConversion();
      await this.testParameterValidation();

      // Generate report
      this.generateTestReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async loadSamplePrompts() {
    const promptsDir = path.join(__dirname, '../../taskmaster/prompts');
    
    try {
      // Load original Task-Master prompts
      this.samplePrompts.addTask = JSON.parse(
        await fs.readFile(path.join(promptsDir, 'add-task.json'), 'utf8')
      );
      this.samplePrompts.research = JSON.parse(
        await fs.readFile(path.join(promptsDir, 'research.json'), 'utf8')
      );
      this.samplePrompts.expandTask = JSON.parse(
        await fs.readFile(path.join(promptsDir, 'expand-task.json'), 'utf8')
      );
    } catch (error) {
      throw new Error(`Failed to load sample prompts: ${error.message}`);
    }
  }

  async testHandlebarsToSimpleConversion() {
    console.log('Testing Handlebars to Simple Placeholder Conversion...');

    const testCases = [
      {
        input: 'Create a task for {{taskTitle}} with priority {{priority}}',
        expected: 'Create a task for {TASK_TITLE} with priority {PRIORITY}',
        description: 'Basic variable conversion'
      },
      {
        input: '{{#if hasDeadline}}Deadline: {{deadline}}{{/if}}',
        expected: '{IF_HAS_DEADLINE}Deadline: {DEADLINE}{END_IF}',
        description: 'Conditional block conversion'
      },
      {
        input: '{{#each dependencies}}{{this}}{{/each}}',
        expected: '{EACH_DEPENDENCIES}{THIS}{END_EACH}',
        description: 'Loop conversion'
      }
    ];

    for (const testCase of testCases) {
      try {
        const result = convertHandlebarsToSimple(testCase.input);
        assert.strictEqual(result, testCase.expected, `Failed: ${testCase.description}`);
        
        this.testResults.push({
          test: 'Handlebars to Simple',
          case: testCase.description,
          status: 'PASSED'
        });
        console.log(`  ✅ ${testCase.description}`);
      } catch (error) {
        this.testResults.push({
          test: 'Handlebars to Simple',
          case: testCase.description,
          status: 'FAILED',
          error: error.message
        });
        console.log(`  ❌ ${testCase.description}: ${error.message}`);
      }
    }
  }

  async testGitHubCopilotConversion() {
    console.log('Testing GitHub Copilot Conversion...');

    try {
      const converted = convertForGitHubCopilot(this.samplePrompts.addTask);
      
      // Test that Handlebars syntax is converted
      assert(!converted.includes('{{'), 'Handlebars syntax should be converted');
      assert(!converted.includes('}}'), 'Handlebars syntax should be converted');
      
      // Test that placeholders are in correct format
      assert(converted.includes('{'), 'Should contain placeholder brackets');
      assert(converted.includes('}'), 'Should contain placeholder brackets');
      
      // Test that conditional logic is simplified
      assert(!converted.includes('#if'), 'Complex conditionals should be simplified');
      
      this.testResults.push({
        test: 'GitHub Copilot Conversion',
        case: 'Add Task Prompt',
        status: 'PASSED'
      });
      console.log('  ✅ Add Task Prompt conversion');
      
    } catch (error) {
      this.testResults.push({
        test: 'GitHub Copilot Conversion',
        case: 'Add Task Prompt',
        status: 'FAILED',
        error: error.message
      });
      console.log(`  ❌ Add Task Prompt conversion: ${error.message}`);
    }
  }

  async testCursorConversion() {
    console.log('Testing Cursor Conversion...');

    try {
      const converted = convertForCursor(this.samplePrompts.research);
      
      // Test that result is valid JSON
      const parsed = JSON.parse(converted);
      assert(typeof parsed === 'object', 'Should produce valid JSON object');
      
      // Test that it contains expected Cursor-specific fields
      assert(parsed.name, 'Should have name field');
      assert(parsed.description, 'Should have description field');
      assert(parsed.variables, 'Should have variables field');
      
      // Test that variables are properly typed
      for (const [key, variable] of Object.entries(parsed.variables)) {
        assert(variable.type, `Variable ${key} should have type`);
        assert(variable.description, `Variable ${key} should have description`);
      }
      
      this.testResults.push({
        test: 'Cursor Conversion',
        case: 'Research Prompt',
        status: 'PASSED'
      });
      console.log('  ✅ Research Prompt conversion');
      
    } catch (error) {
      this.testResults.push({
        test: 'Cursor Conversion',
        case: 'Research Prompt',
        status: 'FAILED',
        error: error.message
      });
      console.log(`  ❌ Research Prompt conversion: ${error.message}`);
    }
  }

  async testClaudeCodeConversion() {
    console.log('Testing Claude-Code Conversion...');

    try {
      const converted = convertForClaudeCode(this.samplePrompts.expandTask);
      
      // Test that it contains agent-specific structure
      assert(converted.includes('## Agent:'), 'Should contain agent specification');
      assert(converted.includes('## Purpose:'), 'Should contain purpose section');
      assert(converted.includes('## Parameters:'), 'Should contain parameters section');
      
      // Test that conditional logic is converted to JavaScript
      if (this.samplePrompts.expandTask.template.includes('{{#if')) {
        assert(converted.includes('if ('), 'Conditionals should be converted to JavaScript');
      }
      
      // Test that helper functions are implemented
      if (this.samplePrompts.expandTask.template.includes('{{eq')) {
        assert(converted.includes('function eq('), 'Helper functions should be implemented');
      }
      
      this.testResults.push({
        test: 'Claude-Code Conversion',
        case: 'Expand Task Prompt',
        status: 'PASSED'
      });
      console.log('  ✅ Expand Task Prompt conversion');
      
    } catch (error) {
      this.testResults.push({
        test: 'Claude-Code Conversion',
        case: 'Expand Task Prompt',
        status: 'FAILED',
        error: error.message
      });
      console.log(`  ❌ Expand Task Prompt conversion: ${error.message}`);
    }
  }

  async testConditionalLogicConversion() {
    console.log('Testing Conditional Logic Conversion...');

    const testCases = [
      {
        handlebars: '{{#if condition}}Show this{{else}}Show that{{/if}}',
        githubCopilot: '{IF_CONDITION}Show this{ELSE}Show that{END_IF}',
        cursor: 'condition ? "Show this" : "Show that"',
        claudeCode: 'if (condition) { return "Show this"; } else { return "Show that"; }',
        description: 'If-else conditional'
      },
      {
        handlebars: '{{#unless disabled}}Content{{/unless}}',
        githubCopilot: '{UNLESS_DISABLED}Content{END_UNLESS}',
        cursor: '!disabled ? "Content" : ""',
        claudeCode: 'if (!disabled) { return "Content"; }',
        description: 'Unless conditional'
      }
    ];

    for (const testCase of testCases) {
      try {
        // Test GitHub Copilot conversion
        const copilotResult = convertHandlebarsToSimple(testCase.handlebars);
        assert(copilotResult.includes('{IF_'), 'Should convert to simple conditional format');
        
        this.testResults.push({
          test: 'Conditional Logic',
          case: `${testCase.description} - GitHub Copilot`,
          status: 'PASSED'
        });
        console.log(`  ✅ ${testCase.description} - GitHub Copilot`);
        
      } catch (error) {
        this.testResults.push({
          test: 'Conditional Logic',
          case: `${testCase.description} - GitHub Copilot`,
          status: 'FAILED',
          error: error.message
        });
        console.log(`  ❌ ${testCase.description} - GitHub Copilot: ${error.message}`);
      }
    }
  }

  async testHelperFunctionConversion() {
    console.log('Testing Helper Function Conversion...');

    const testCases = [
      {
        handlebars: '{{eq status "completed"}}',
        description: 'Equality helper'
      },
      {
        handlebars: '{{gt priority 5}}',
        description: 'Greater than helper'
      },
      {
        handlebars: '{{json data}}',
        description: 'JSON helper'
      }
    ];

    for (const testCase of testCases) {
      try {
        // Test that helper functions are properly converted
        const converted = convertHandlebarsToSimple(testCase.handlebars);
        assert(!converted.includes('{{'), 'Handlebars syntax should be removed');
        
        this.testResults.push({
          test: 'Helper Functions',
          case: testCase.description,
          status: 'PASSED'
        });
        console.log(`  ✅ ${testCase.description}`);
        
      } catch (error) {
        this.testResults.push({
          test: 'Helper Functions',
          case: testCase.description,
          status: 'FAILED',
          error: error.message
        });
        console.log(`  ❌ ${testCase.description}: ${error.message}`);
      }
    }
  }

  async testParameterValidation() {
    console.log('Testing Parameter Validation...');

    try {
      // Test that parameter schemas are preserved and converted
      const originalParams = this.samplePrompts.addTask.parameters;
      const converted = convertForCursor(this.samplePrompts.addTask);
      const parsed = JSON.parse(converted);
      
      // Check that all original parameters are present
      for (const paramName of Object.keys(originalParams)) {
        assert(parsed.variables[paramName], `Parameter ${paramName} should be preserved`);
      }
      
      // Check that parameter types are correctly mapped
      for (const [paramName, param] of Object.entries(originalParams)) {
        const convertedParam = parsed.variables[paramName];
        assert(convertedParam.type, `Parameter ${paramName} should have type`);
        
        // Test type mapping
        if (param.type === 'string') {
          assert(convertedParam.type === 'string', 'String type should be preserved');
        }
        if (param.type === 'number') {
          assert(convertedParam.type === 'number', 'Number type should be preserved');
        }
        if (param.type === 'boolean') {
          assert(convertedParam.type === 'boolean', 'Boolean type should be preserved');
        }
      }
      
      this.testResults.push({
        test: 'Parameter Validation',
        case: 'Type Mapping',
        status: 'PASSED'
      });
      console.log('  ✅ Type Mapping');
      
    } catch (error) {
      this.testResults.push({
        test: 'Parameter Validation',
        case: 'Type Mapping',
        status: 'FAILED',
        error: error.message
      });
      console.log(`  ❌ Type Mapping: ${error.message}`);
    }
  }

  generateTestReport() {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = total - passed;
    const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

    console.log('\n📊 Unit Test Summary:');
    console.log(`  Total Tests: ${total}`);
    console.log(`  Passed: ${passed}`);
    console.log(`  Failed: ${failed}`);
    console.log(`  Success Rate: ${successRate}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAILED')
        .forEach(r => {
          console.log(`  - ${r.test} - ${r.case}: ${r.error}`);
        });
      
      process.exit(1);
    } else {
      console.log('\n✅ All unit tests passed successfully!');
    }
  }
}

// Mock conversion functions for testing
function convertHandlebarsToSimple(template) {
  return template
    .replace(/\{\{([^}]+)\}\}/g, (match, content) => {
      // Handle conditionals
      if (content.startsWith('#if ')) {
        return `{IF_${content.slice(4).toUpperCase().replace(/\s+/g, '_')}}`;
      }
      if (content === '/if') {
        return '{END_IF}';
      }
      if (content === 'else') {
        return '{ELSE}';
      }
      if (content.startsWith('#unless ')) {
        return `{UNLESS_${content.slice(8).toUpperCase().replace(/\s+/g, '_')}}`;
      }
      if (content === '/unless') {
        return '{END_UNLESS}';
      }
      if (content.startsWith('#each ')) {
        return `{EACH_${content.slice(6).toUpperCase().replace(/\s+/g, '_')}}`;
      }
      if (content === '/each') {
        return '{END_EACH}';
      }
      
      // Handle variables
      return `{${content.toUpperCase().replace(/\s+/g, '_')}}`;
    });
}

function convertForGitHubCopilot(prompt) {
  return convertHandlebarsToSimple(prompt.template || prompt.content || JSON.stringify(prompt));
}

function convertForCursor(prompt) {
  const result = {
    name: prompt.name || 'Converted Prompt',
    description: prompt.description || 'Converted from Task-Master',
    variables: {}
  };

  if (prompt.parameters) {
    for (const [key, param] of Object.entries(prompt.parameters)) {
      result.variables[key] = {
        type: param.type || 'string',
        description: param.description || `Parameter ${key}`,
        required: param.required || false
      };
    }
  }

  return JSON.stringify(result, null, 2);
}

function convertForClaudeCode(prompt) {
  let result = `## Agent: ${prompt.name || 'Converted Agent'}\n\n`;
  result += `## Purpose: ${prompt.description || 'Converted from Task-Master'}\n\n`;
  result += `## Parameters:\n`;
  
  if (prompt.parameters) {
    for (const [key, param] of Object.entries(prompt.parameters)) {
      result += `- ${key}: ${param.type || 'string'} - ${param.description || 'Parameter'}\n`;
    }
  }
  
  result += '\n## Implementation:\n';
  result += convertHandlebarsToSimple(prompt.template || prompt.content || '');
  
  return result;
}

// Run tests if called directly
if (require.main === module) {
  const tests = new PromptConversionTests();
  tests.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = PromptConversionTests;
