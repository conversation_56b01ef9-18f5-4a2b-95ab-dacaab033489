# Zen Planner - Guide Expert

## 🧠 Philosophie de l'Outil

**`mcp_zen_planner`** n'est pas un simple générateur de plans - c'est un **"Échafaudage Cognitif pour la Réflexion Stratégique"**.

### Vision Unique
Cet outil combat la tendance des agents IA à la convergence prématurée vers des solutions tactiques. Il privilégie la **rigueur et la délibération** sur la vitesse d'exécution, fonctionnant comme un partenaire de sparring stratégique qui structure le processus de réflexion plutôt que de simplement produire une liste de tâches.

### Différenciation Critique
- **Seul outil workflow auto-contenu** : Pas d'analyse experte externe nécessaire
- **Réflexion forcée** : Pauses obligatoires comme "sécurité cognitive" 
- **Graphe de décision** : Produit des structures de décision complexes via branching
- **Externalisation de la pensée** : Transforme le raisonnement interne en artefact traçable

---

## 🎯 Quand Utiliser cet Outil

### Cas d'Usage Optimaux ✅
- **Planification architecturale** : Concevoir des systèmes complexes avec incertitudes
- **Stratégies de migration** : Évaluer plusieurs approches avant implémentation
- **Prise de décision critique** : Quand le coût d'une mauvaise décision initiale est élevé
- **Décomposition de problèmes complexes** : Transformer des défis abstraits en étapes concrètes
- **Comparaison d'alternatives** : Explorer systématiquement différentes approches

### Éviter Pour ❌
- **Tâches simples linéaires** : Une todo list basique suffit
- **Planification tactique immédiate** : Quand les étapes sont évidentes
- **Contraintes de temps strict** : Les pauses réflexives prennent du temps
- **Plans déjà bien définis** : L'outil excelle dans l'ambiguïté, pas la formalisation

---

## 🚀 Framework de Prompt Expert

### Structure PROBLÈME-CONTEXTE-AMBITION-CONTRAINTES

Pour maximiser l'efficacité de l'outil, utilisez cette structure dans votre prompt initial :

```javascript
await mcp_zen_planner({
  step: `
  PROBLÈME À RÉSOUDRE : [Décrivez le défi de manière expressive]
  
  CONTEXTE COMPLET :
  - Situation actuelle : [État des lieux détaillé]
  - Contraintes techniques : [Limitations, dépendances]
  - Ressources disponibles : [Équipe, temps, budget]
  - Risques identifiés : [Ce qui pourrait mal tourner]
  
  AMBITION & CRITÈRES DE SUCCÈS :
  - Objectif principal : [Ce que vous voulez accomplir]
  - Métriques de réussite : [Comment mesurer le succès]
  - Vision long terme : [Impact désiré]
  
  CONTRAINTES & QUESTIONS :
  - Limitations : [Ce qui n'est pas négociable]
  - Incertitudes : [Ce que vous ne savez pas encore]
  - Alternatives pressenties : [Options déjà considérées]
  `,
  step_number: 1,
  total_steps: [Estimation initiale - sera ajustée],
  next_step_required: true
});
```

### Exemples Comparatifs

#### ❌ Prompt Faible
```javascript
{
  step: "Je veux créer une API pour mon app",
  step_number: 1,
  total_steps: 3,
  next_step_required: true
}
```

#### ✅ Prompt Expert
```javascript
{
  step: `
  PROBLÈME À RÉSOUDRE : Concevoir l'architecture API pour une plateforme e-commerce B2B qui doit servir 3 types d'clients différents
  
  CONTEXTE COMPLET :
  - Situation actuelle : Application monolithique PHP legacy avec 50k utilisateurs
  - Contraintes techniques : Migration progressive nécessaire, équipe mixte junior/senior
  - Ressources disponibles : 4 développeurs, 6 mois, budget cloud flexible
  - Risques identifiés : Interruption service existant, courbe apprentissage équipe
  
  AMBITION & CRITÈRES DE SUCCÈS :
  - Objectif principal : API moderne, évolutive, 99.9% uptime
  - Métriques de réussite : <200ms latence, support 100k req/min
  - Vision long terme : Plateforme multi-tenant, intégrations tierces
  
  CONTRAINTES & QUESTIONS :
  - Limitations : Budget infrastructure, compétences équipe actuelles
  - Incertitudes : REST vs GraphQL vs gRPC, stratégie authentification
  - Alternatives pressenties : Microservices vs API Gateway vs Modular Monolith
  `,
  step_number: 1,
  total_steps: 8,
  next_step_required: true
}
```

---

## 🎪 Stratégies d'Orchestration Avancées

### 1. Pattern "Entonnoir Stratégique → Tactique"

**Objectif** : Utiliser le planner pour définir le "quoi" et "pourquoi", puis alimenter d'autres outils spécialisés.

```javascript
// Phase 1 : Planification stratégique
const strategicPlan = await mcp_zen_planner({
  step: `[Analyse architecturale complète]`,
  step_number: 1,
  total_steps: 6,
  next_step_required: true
});

// ... Continuer jusqu'à next_step_required: false

// Phase 2 : Utiliser le plan comme cahier des charges
const implementation = await mcp_zen_codereview({
  continuation_id: strategicPlan.continuation_id,
  relevant_files: ["architecture-spec.md"], // Généré depuis le plan
  step: `Valider l'implémentation selon le plan stratégique établi`
});
```

### 2. Pattern "Comparaison A/B via Branching"

**Objectif** : Explorer systématiquement plusieurs approches avant décision.

```javascript
// Étape charnière
await mcp_zen_planner({
  step: `Nous devons choisir entre une architecture microservices et modular monolith`,
  step_number: 3,
  total_steps: 8,
  next_step_required: true
});

// Branche A : Exploration microservices
await mcp_zen_planner({
  step: `Analyser l'approche microservices : avantages, complexité, coûts`,
  step_number: 4,
  total_steps: 8,
  is_branch_point: true,
  branch_from_step: 3,
  branch_id: 'microservices-exploration',
  next_step_required: true
});

// Branche B : Exploration modular monolith
await mcp_zen_planner({
  step: `Analyser l'approche modular monolith : simplicité, évolutivité, contraintes`,
  step_number: 4,
  total_steps: 8,
  is_branch_point: true,
  branch_from_step: 3,
  branch_id: 'modular-monolith-exploration',
  next_step_required: true
});

// Les branches sont stockées et peuvent être comparées pour décision finale
```

### 3. Pattern "Correction de Trajectoire"

**Objectif** : Mettre à jour formellement le plan quand de nouvelles contraintes émergent.

```javascript
// Découverte d'une contrainte imprévue par un autre outil
const constraintDiscovery = await mcp_zen_codereview({
  // ... analyse qui révèle une limitation critique
});

// Correction formelle du plan
await mcp_zen_planner({
  step: `Révision suite à découverte contrainte : [nouvelle contrainte]. Réajuster l'approche de l'étape 2.`,
  step_number: 6,
  total_steps: 8,
  is_step_revision: true,
  revises_step_number: 2,
  next_step_required: true
});
```

---

## ⚙️ Paramètres et Capacités Techniques

### Paramètres Essentiels

| Paramètre | Type | Description | Usage Expert |
|-----------|------|-------------|--------------|
| `step` | string | **Contenu de l'étape de planification** | Doit être expressif pour step 1, concret ensuite |
| `step_number` | int | **Numéro d'étape** (débute à 1) | Incrémenté séquentiellement |
| `total_steps` | int | **Estimation totale** (ajustable) | Peut évoluer avec `more_steps_needed: true` |
| `next_step_required` | bool | **Continuation nécessaire** | `false` = plan terminé |

### Paramètres de Branching/Révision

| Paramètre | Type | Description | Usage Expert |
|-----------|------|-------------|--------------|
| `is_branch_point` | bool | **Point de branchement** | Créer alternative d'exploration |
| `branch_from_step` | int | **Étape source du branchement** | Référence l'étape de départ |
| `branch_id` | string | **Identifiant de branche** | Ex: 'rest-api-study', 'grpc-approach' |
| `is_step_revision` | bool | **Révision d'étape** | Corriger une étape antérieure |
| `revises_step_number` | int | **Numéro d'étape révisée** | Quelle étape est corrigée |
| `more_steps_needed` | bool | **Étapes supplémentaires** | Étendre au-delà de l'estimation initiale |

### Capacités Uniques

- **Réflexion Forcée** : Plans ≥5 étapes déclenchent pauses obligatoires sur steps 1-3
- **Persistance des branches** : Toutes les branches sont stockées dans `self.branches`
- **Auto-contenu** : Pas d'analyse experte externe, l'outil EST l'analyse
- **Mémoire contextuelle** : Support `continuation_id` pour orchestration multi-outils

---

## 🎭 Réflexion Forcée - Comprendre les Pauses

### Mécanisme de Sécurité Cognitive

Pour les plans complexes (≥5 étapes), l'outil impose des pauses réflexives sur les 3 premières étapes :

#### Step 1 - Analyse du Scope Complet
```
MANDATORY: DO NOT call the planner tool again immediately. 
REQUIRED DEEP THINKING before calling planner step 2:
1. Analyze the FULL SCOPE: What exactly needs to be accomplished?
2. Consider MULTIPLE APPROACHES: What are 2-3 different ways to tackle this?
3. Identify CONSTRAINTS & DEPENDENCIES: What limits our options?
4. Think about SUCCESS CRITERIA: How will we know we've succeeded?
5. Consider RISKS & MITIGATION: What could go wrong early vs late?
```

#### Step 2 - Validation de l'Approche
```
STOP! Complex planning requires reflection between steps.
MANDATORY REFLECTION before planner step 3:
1. EVALUATE YOUR APPROACH: Is the direction from step 1 still the best?
2. IDENTIFY MAJOR PHASES: What are the 3-5 main chunks of work?
3. SPOT DEPENDENCIES: What must happen before what?
4. CONSIDER RESOURCES: What skills, tools, or access do we need?
5. FIND CRITICAL PATHS: Where could delays hurt the most?
```

#### Step 3 - Transition Stratégique → Tactique
```
PAUSE for final strategic reflection.
FINAL DEEP THINKING before planner step 4:
1. VALIDATE COMPLETENESS: Does this plan address all original requirements?
2. CHECK FOR GAPS: What assumptions need validation? What's unclear?
3. PLAN FOR ADAPTATION: How will we know if we need to change course?
4. DEFINE FIRST STEPS: What are the first 2-3 concrete actions?
5. TRANSITION MINDSET: Ready to shift from strategic to tactical planning?
```

### Pourquoi Ces Pauses Sont Essentielles

- **Prévention de la convergence prématurée** : Évite de se précipiter vers une solution
- **Validation des hypothèses** : Force l'examen critique des suppositions
- **Exploration d'alternatives** : Garantit la considération de multiples approches
- **Gestion du risque** : Identifie les points de défaillance potentiels

---

## 🏆 Indicateurs de Succès

### Métriques de Qualité du Plan

#### ✅ Plan de Haute Qualité
- **Couverture complète** : Tous les aspects du problème initial sont adressés
- **Étapes actionnables** : Chaque step contient des actions concrètes et mesurables
- **Gestion des risques** : Identification proactive des points de défaillance
- **Flexibilité** : Plans adaptatifs avec points de révision intégrés
- **Branches explorées** : Alternatives évaluées quand approprié

#### ❌ Signaux d'Alarme
- **Convergence hâtive** : Passage direct aux détails sans exploration
- **Steps vagues** : Étapes trop générales ou non actionnables
- **Ignorance des contraintes** : Plan irréaliste face aux limitations
- **Pas d'alternatives** : Une seule approche considérée
- **Absence de métriques** : Aucun critère de succès défini

### ROI Attendu

- **Réduction des erreurs stratégiques** : 60-80% de réduction des pivots coûteux
- **Amélioration de la qualité décisionnelle** : Plans 3-5x plus robustes face à l'incertitude
- **Accélération de l'exécution** : Paradoxalement, investir du temps en planification accélère l'implémentation
- **Confiance d'équipe** : Plans structurés augmentent l'adhésion et réduisent l'anxiété

---

## 💡 Bonnes Pratiques et Pièges à Éviter

### ✅ Bonnes Pratiques

#### 1. Investissement Initial Stratégique
```javascript
// ✅ Bon : Prompt riche pour démarrage fort
{
  step: `[Analyse complète 200+ mots avec contexte, contraintes, ambitions]`,
  step_number: 1,
  total_steps: 7, // Estimation réaliste pour complexité
  next_step_required: true
}
```

#### 2. Utilisation des Pauses Réflexives
- **Respectez les pauses** : Ne contournez pas les mécanismes de réflexion forcée
- **Approfondissez pendant les pauses** : Utilisez le temps pour vraiment analyser
- **Documentez vos réflexions** : Capturez les insights pour steps suivants

#### 3. Orchestration Multi-Outils
```javascript
// ✅ Chaînage efficace plan → implémentation
const plan = await mcp_zen_planner({ /* plan complet */ });
const validation = await mcp_zen_codereview({
  continuation_id: plan.continuation_id,
  // Utiliser le plan comme spécification
});
```

### ❌ Pièges à Éviter

#### 1. Sous-estimation de la Complexité
```javascript
// ❌ Mauvais : Complexité sous-estimée
{
  step: "Migrer vers microservices",
  total_steps: 3, // Irréaliste pour cette complexité
}
```

#### 2. Contournement des Mécanismes de Sécurité
```javascript
// ❌ Éviter : Forcer la continuation sans réflexion
// Respecter les pauses imposées par l'outil
```

#### 3. Abandonment Prématuré
- **Ne pas abandonner** : Si le plan semble long, c'est normal pour la complexité
- **Ajuster avec `more_steps_needed`** : Préférer étendre que comprimer artificiellement
- **Utiliser le branching** : Explorer les alternatives plutôt que choisir arbitrairement

#### 4. Plans Purement Tactiques
```javascript
// ❌ Mauvais : Tactical d'emblée
{
  step: "Étape 1: Créer les endpoints, Étape 2: Tester, Étape 3: Déployer",
}

// ✅ Bon : Strategic d'abord
{
  step: "Analyser les besoins architecturaux, contraintes, et définir l'approche optimale",
}
```

---

## 🔄 Workflow d'Utilisation Recommandé

### Phase 1 : Préparation (Avant le Premier Appel)
1. **Clarifiez le problème** : Écrivez 2-3 paragraphes sur le défi
2. **Identifiez les contraintes** : Listez ce qui n'est pas négociable
3. **Définissez le succès** : Comment mesurerez-vous la réussite ?
4. **Estimez la complexité** : Simple (3-4 steps) vs Complexe (5+ steps)

### Phase 2 : Lancement Stratégique
```javascript
const planInitial = await mcp_zen_planner({
  step: `[Prompt structuré PROBLÈME-CONTEXTE-AMBITION-CONTRAINTES]`,
  step_number: 1,
  total_steps: [Estimation réaliste],
  next_step_required: true
});
```

### Phase 3 : Réflexion et Itération
- **Respectez les pauses** imposées par l'outil
- **Approfondissez l'analyse** pendant les pauses
- **Ajustez `total_steps`** si nécessaire avec `more_steps_needed: true`
- **Explorez les branches** pour les décisions critiques

### Phase 4 : Finalisation et Transition
- **Complétez avec `next_step_required: false`**
- **Capturez le `continuation_id`** pour orchestration
- **Utilisez le plan** comme spécification pour autres outils

### Phase 5 : Orchestration Multi-Outils (Optionnel)
```javascript
// Utiliser le plan comme cahier des charges
const implementation = await mcp_zen_codereview({
  continuation_id: plan.continuation_id,
  step: `Valider l'architecture selon le plan établi`
});
```

---

## 🎯 Conclusion

`mcp_zen_planner` est bien plus qu'un outil de planification - c'est un **partenaire de réflexion stratégique** qui structure votre processus décisionnel. Son investissement initial en temps de réflexion se traduit par des plans plus robustes, moins de pivots coûteux, et une exécution plus fluide.

### Méta-Apprentissage pour Agents IA

En tant qu'agent IA utilisant cet outil :
- **Embrassez la lenteur délibérée** : La vitesse de planification n'est pas une métrique de succès
- **Valorisez l'exploration** : Utilisez le branching pour éviter les biais de confirmation  
- **Structurez votre réflexion** : Laissez l'outil guider votre processus cognitif
- **Pensez orchestration** : Planifiez comment le plan alimentera d'autres outils

**Slogan** : *"Pour des stratégies robustes, pas des plans hâtifs."*

---

**Continuation ID pour cette session** : Utilisez le continuation_id fourni pour orchestrer avec d'autres outils MCP Zen ou continuer l'analyse.
