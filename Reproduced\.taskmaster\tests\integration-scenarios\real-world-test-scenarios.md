# Real-World Integration Test Scenarios

## Overview
Comprehensive test scenarios that simulate real-world usage of the adapted Task-Master prompts across GitHub Copilot, Cursor, and Claude-Code environments.

## Scenario Categories

### 1. E-commerce Platform Development

#### Scenario 1.1: Feature Planning and Research
**Context**: Building a new e-commerce platform with React, Node.js, and PostgreSQL
**Objective**: Research and plan implementation of user authentication system

**GitHub Copilot Test**:
1. Activate enhanced-research-mode chatmode
2. Query: "Research modern authentication patterns for e-commerce platforms"
3. Use MCP integration to get current best practices
4. Verify response includes OAuth 2.0, JWT, and security considerations
5. Check for project-specific recommendations

**Cursor Test**:
1. Apply perplexity-research.cursorrules
2. Use @research command with project context
3. Query: "Authentication architecture for React e-commerce app"
4. Verify MCP tools provide current security standards
5. Check integration with existing project structure

**Claude-Code Test**:
1. Use research-agent with MCP enhancement
2. Execute: `/research-reason "authentication strategy" "e-commerce,security-critical"`
3. Verify systematic analysis with current threat landscape
4. Check for implementation roadmap with current tools

**Success Criteria**:
- All tools provide current authentication best practices
- Recommendations consider e-commerce specific requirements
- Security considerations include recent vulnerabilities
- Implementation guidance is actionable and current

#### Scenario 1.2: Complex Task Breakdown
**Context**: Implementing shopping cart functionality
**Objective**: Break down complex feature into manageable tasks

**GitHub Copilot Test**:
1. Use expand-task.instructions.md
2. Input: "Implement shopping cart with persistence, real-time updates, and checkout"
3. Verify task breakdown includes all components
4. Check for testing strategies and dependencies

**Cursor Test**:
1. Use task-master-mode.json in task-expansion sub-mode
2. Input complex shopping cart requirements
3. Verify JSON output with structured subtasks
4. Check for implementation guidance and testing approaches

**Claude-Code Test**:
1. Use task-expansion-agent with MCP research
2. Execute: `/expand-task "shopping cart implementation"`
3. Verify agent researches current patterns and best practices
4. Check for detailed subtasks with current technology recommendations

**Success Criteria**:
- Task breakdown is comprehensive and logical
- Dependencies are correctly identified
- Testing strategies are included
- Current best practices are incorporated

### 2. SaaS Application Development

#### Scenario 2.1: Technology Stack Decision
**Context**: Building a multi-tenant SaaS application
**Objective**: Choose appropriate technology stack and architecture

**GitHub Copilot Test**:
1. Use mcp-research.instructions.md
2. Query: "Compare technology stacks for multi-tenant SaaS application"
3. Verify MCP provides current market analysis
4. Check for scalability and cost considerations

**Cursor Test**:
1. Use research mode with MCP integration
2. Query: "SaaS architecture patterns 2024"
3. Verify real-time research with current trends
4. Check for multi-tenancy specific recommendations

**Claude-Code Test**:
1. Use research-agent with perplexity_reason
2. Execute: `/research-reason "SaaS technology stack" "multi-tenant,scalable,cost-effective"`
3. Verify systematic evaluation of options
4. Check for justified recommendations with current data

**Success Criteria**:
- Technology recommendations are current and relevant
- Multi-tenancy considerations are addressed
- Scalability and cost factors are analyzed
- Implementation complexity is assessed

#### Scenario 2.2: Performance Optimization Planning
**Context**: Existing SaaS application experiencing performance issues
**Objective**: Analyze and plan performance optimization strategy

**GitHub Copilot Test**:
1. Use analyze-complexity.instructions.md
2. Input: Performance bottlenecks in Node.js API with PostgreSQL
3. Verify complexity analysis includes current optimization techniques
4. Check for monitoring and measurement strategies

**Cursor Test**:
1. Use complexity-analysis mode
2. Analyze performance optimization requirements
3. Verify structured analysis with current tools and techniques
4. Check for implementation priority and impact assessment

**Claude-Code Test**:
1. Use complexity-analyzer-agent with MCP reasoning
2. Execute: `/analyze-complexity "API performance optimization"`
3. Verify multi-dimensional analysis with current benchmarks
4. Check for optimization roadmap with current tools

**Success Criteria**:
- Performance analysis is comprehensive and current
- Optimization strategies include latest techniques
- Implementation complexity is accurately assessed
- Monitoring and measurement approaches are included

### 3. Mobile Application Development

#### Scenario 3.1: Cross-Platform Framework Selection
**Context**: Developing mobile app for iOS and Android
**Objective**: Choose between React Native, Flutter, and native development

**GitHub Copilot Test**:
1. Use enhanced research chatmode with comparison focus
2. Query: "React Native vs Flutter vs Native development 2024"
3. Verify MCP provides current performance comparisons
4. Check for development team considerations

**Cursor Test**:
1. Use research mode with comparison type
2. Query mobile framework comparison with project constraints
3. Verify real-time analysis of current framework states
4. Check for team expertise and timeline considerations

**Claude-Code Test**:
1. Use research-agent with comparison analysis
2. Execute: `/research "mobile development frameworks" deep comparison`
3. Verify comprehensive analysis with current market data
4. Check for decision framework with justified recommendations

**Success Criteria**:
- Framework comparison includes latest versions and features
- Performance benchmarks are current and relevant
- Development team factors are considered
- Long-term maintenance implications are addressed

#### Scenario 3.2: Feature Implementation Planning
**Context**: Adding offline functionality to mobile app
**Objective**: Plan offline data synchronization and conflict resolution

**GitHub Copilot Test**:
1. Use add-task.instructions.md for offline feature
2. Input: "Implement offline data sync with conflict resolution"
3. Verify task creation includes current sync patterns
4. Check for data consistency and user experience considerations

**Cursor Test**:
1. Use task-creation mode with offline sync requirements
2. Create structured task with implementation details
3. Verify inclusion of current offline-first patterns
4. Check for testing and edge case handling

**Claude-Code Test**:
1. Use task-creator-agent with MCP research
2. Execute: `/create-task "offline data synchronization"`
3. Verify agent researches current offline patterns
4. Check for comprehensive task with current best practices

**Success Criteria**:
- Offline sync strategies are current and proven
- Conflict resolution approaches are comprehensive
- User experience considerations are included
- Testing strategies cover edge cases

### 4. Enterprise Application Integration

#### Scenario 4.1: Legacy System Integration
**Context**: Integrating new microservices with legacy monolith
**Objective**: Plan integration strategy with minimal disruption

**GitHub Copilot Test**:
1. Use research instructions for integration patterns
2. Query: "Legacy system integration patterns for microservices"
3. Verify MCP provides current integration strategies
4. Check for risk mitigation and rollback plans

**Cursor Test**:
1. Use research mode for integration analysis
2. Query legacy integration with specific technology constraints
3. Verify real-time research on current integration tools
4. Check for phased migration strategies

**Claude-Code Test**:
1. Use research-agent with reasoning mode
2. Execute: `/research-reason "legacy integration strategy" "minimal-disruption,gradual-migration"`
3. Verify systematic analysis of integration options
4. Check for risk assessment and mitigation strategies

**Success Criteria**:
- Integration strategies minimize business disruption
- Current integration tools and patterns are recommended
- Risk assessment is comprehensive and realistic
- Migration timeline is practical and phased

#### Scenario 4.2: Security Compliance Implementation
**Context**: Implementing GDPR and SOC 2 compliance
**Objective**: Plan compliance implementation across systems

**GitHub Copilot Test**:
1. Use complexity analysis for compliance requirements
2. Input: "GDPR and SOC 2 compliance implementation"
3. Verify analysis includes current regulatory requirements
4. Check for implementation complexity and timeline

**Cursor Test**:
1. Use complexity-analysis mode for compliance
2. Analyze compliance requirements with current regulations
3. Verify structured analysis with current compliance tools
4. Check for audit preparation and documentation needs

**Claude-Code Test**:
1. Use complexity-analyzer-agent with compliance focus
2. Execute: `/analyze-complexity "regulatory compliance implementation"`
3. Verify multi-dimensional analysis with current requirements
4. Check for compliance roadmap with current standards

**Success Criteria**:
- Compliance requirements are current and accurate
- Implementation complexity is realistically assessed
- Current compliance tools and services are recommended
- Audit preparation strategies are included

### 5. DevOps and Infrastructure

#### Scenario 5.1: CI/CD Pipeline Modernization
**Context**: Modernizing legacy CI/CD pipeline
**Objective**: Implement modern DevOps practices and tools

**GitHub Copilot Test**:
1. Use research mode for CI/CD modernization
2. Query: "Modern CI/CD pipeline best practices 2024"
3. Verify MCP provides current DevOps tools and practices
4. Check for migration strategies from legacy systems

**Cursor Test**:
1. Use research mode with DevOps focus
2. Query CI/CD modernization with current tools
3. Verify real-time research on current pipeline technologies
4. Check for implementation and migration guidance

**Claude-Code Test**:
1. Use research-agent for DevOps analysis
2. Execute: `/research "CI/CD modernization" deep best-practices`
3. Verify comprehensive analysis with current tools
4. Check for implementation roadmap with current practices

**Success Criteria**:
- CI/CD recommendations include latest tools and practices
- Migration strategies are practical and low-risk
- Security and compliance considerations are included
- Performance and reliability improvements are quantified

#### Scenario 5.2: Cloud Migration Planning
**Context**: Migrating on-premises infrastructure to cloud
**Objective**: Plan comprehensive cloud migration strategy

**GitHub Copilot Test**:
1. Use task expansion for cloud migration
2. Input: "Migrate on-premises infrastructure to AWS"
3. Verify task breakdown includes current migration patterns
4. Check for cost optimization and security considerations

**Cursor Test**:
1. Use task-expansion mode for migration planning
2. Create detailed migration tasks with current cloud services
3. Verify inclusion of current migration tools and strategies
4. Check for risk mitigation and rollback plans

**Claude-Code Test**:
1. Use task-expansion-agent with cloud migration focus
2. Execute: `/expand-task "cloud migration project"`
3. Verify agent researches current migration patterns
4. Check for detailed subtasks with current cloud technologies

**Success Criteria**:
- Migration strategy includes current cloud services and tools
- Cost optimization strategies are comprehensive
- Security and compliance requirements are addressed
- Migration timeline is realistic and phased

## Test Execution Guidelines

### Pre-Test Setup
1. Ensure all tools have MCP Perplexity integration configured
2. Verify API keys and server connectivity
3. Load appropriate project contexts for each scenario
4. Prepare test data and expected outcomes

### During Testing
1. Document all queries and responses
2. Measure response times and quality
3. Verify MCP tool selection and usage
4. Check context integration and relevance
5. Assess user experience and workflow efficiency

### Post-Test Analysis
1. Compare results across tools for consistency
2. Evaluate information currency and accuracy
3. Assess practical applicability of recommendations
4. Identify areas for improvement and optimization
5. Document lessons learned and best practices

### Success Metrics
- **Information Currency**: >95% of recommendations include current data
- **Context Relevance**: >90% of responses are relevant to project context
- **Implementation Practicality**: >85% of guidance is actionable
- **Tool Consistency**: <10% variance in core recommendations across tools
- **User Experience**: Seamless workflow with minimal friction

These real-world scenarios ensure that the adapted Task-Master prompts provide genuine value in practical development situations while leveraging the enhanced capabilities of MCP Perplexity integration.
