# Performance Optimization Guide

## Overview
This guide provides strategies and best practices for optimizing the performance of Task-Master prompt adaptations across GitHub Copilot, Cursor, and Claude-Code environments, with special focus on MCP Perplexity integration.

## Performance Metrics and Targets

### Response Time Targets
- **Quick Research**: < 3 seconds
- **Standard Research**: < 8 seconds  
- **Deep Analysis**: < 15 seconds
- **Task Creation**: < 5 seconds
- **Task Expansion**: < 10 seconds
- **Complexity Analysis**: < 12 seconds

### Quality Metrics
- **Information Currency**: > 95% of responses include current data
- **Context Relevance**: > 90% of responses relevant to project context
- **Implementation Practicality**: > 85% of guidance is actionable
- **Source Quality**: > 90% of citations from authoritative sources

### Resource Usage Targets
- **Memory Usage**: < 100MB increase per session
- **API Rate Limits**: < 80% of allowed requests per minute
- **Cache Hit Rate**: > 70% for frequently requested information
- **Error Rate**: < 2% under normal conditions

## MCP Perplexity Optimization

### 1. Query Optimization

#### Smart Query Construction
```javascript
// Optimized query construction
function constructOptimizedQuery(userQuery, context) {
  return {
    query: `${userQuery} for ${context.projectType}`,
    context: {
      // Include only essential context
      techStack: context.techStack.slice(0, 3), // Limit to top 3
      constraints: context.constraints.filter(c => c.priority === 'high'),
      timeline: context.timeline
    },
    focusAreas: context.priorities.slice(0, 3) // Limit focus areas
  };
}
```

#### Query Batching
```javascript
// Batch related queries for efficiency
function batchQueries(queries) {
  const batches = [];
  const batchSize = 3;
  
  for (let i = 0; i < queries.length; i += batchSize) {
    batches.push(queries.slice(i, i + batchSize));
  }
  
  return batches.map(batch => ({
    query: batch.map(q => q.query).join('; '),
    combinedContext: mergeBatchContext(batch)
  }));
}
```

### 2. Intelligent Caching

#### Multi-Level Caching Strategy
```javascript
class IntelligentCache {
  constructor() {
    this.sessionCache = new Map(); // Current session
    this.projectCache = new Map(); // Project-specific
    this.globalCache = new Map();  // General knowledge
  }

  async get(query, context) {
    const cacheKey = this.generateCacheKey(query, context);
    
    // Check session cache first (fastest)
    if (this.sessionCache.has(cacheKey)) {
      return this.sessionCache.get(cacheKey);
    }
    
    // Check project cache
    const projectKey = this.generateProjectKey(query, context);
    if (this.projectCache.has(projectKey)) {
      const result = this.projectCache.get(projectKey);
      this.sessionCache.set(cacheKey, result); // Promote to session
      return result;
    }
    
    // Check global cache
    const globalKey = this.generateGlobalKey(query);
    if (this.globalCache.has(globalKey)) {
      return this.globalCache.get(globalKey);
    }
    
    return null; // Cache miss
  }

  set(query, context, result) {
    const cacheKey = this.generateCacheKey(query, context);
    const ttl = this.calculateTTL(query, result);
    
    this.sessionCache.set(cacheKey, { result, expires: Date.now() + ttl });
    
    if (this.isProjectSpecific(context)) {
      const projectKey = this.generateProjectKey(query, context);
      this.projectCache.set(projectKey, { result, expires: Date.now() + ttl * 2 });
    }
    
    if (this.isGeneralKnowledge(query)) {
      const globalKey = this.generateGlobalKey(query);
      this.globalCache.set(globalKey, { result, expires: Date.now() + ttl * 5 });
    }
  }
}
```

#### Cache Invalidation Strategy
```javascript
class CacheInvalidation {
  invalidateOnProjectChange(projectContext) {
    // Invalidate project-specific cache when dependencies change
    if (this.hasPackageJsonChanged(projectContext)) {
      this.projectCache.clear();
    }
  }

  invalidateOnTimeBasedRules() {
    const now = Date.now();
    
    // Technology information expires faster
    this.globalCache.forEach((value, key) => {
      if (this.isTechnologyQuery(key) && now > value.expires) {
        this.globalCache.delete(key);
      }
    });
  }

  invalidateOnContentChange(query, newResult, cachedResult) {
    // Invalidate if significant content change detected
    const similarity = this.calculateSimilarity(newResult, cachedResult.result);
    if (similarity < 0.8) {
      this.invalidateRelatedQueries(query);
    }
  }
}
```

### 3. Request Optimization

#### Parallel Processing
```javascript
async function optimizedResearch(queries, context) {
  // Group queries by type for optimal tool selection
  const quickQueries = queries.filter(q => q.type === 'quick');
  const researchQueries = queries.filter(q => q.type === 'research');
  const reasoningQueries = queries.filter(q => q.type === 'reasoning');

  // Process in parallel with appropriate tools
  const [quickResults, researchResults, reasoningResults] = await Promise.all([
    processQuickQueries(quickQueries, context),
    processResearchQueries(researchQueries, context),
    processReasoningQueries(reasoningQueries, context)
  ]);

  return mergeResults(quickResults, researchResults, reasoningResults);
}
```

#### Request Prioritization
```javascript
class RequestPrioritizer {
  prioritizeRequests(requests) {
    return requests.sort((a, b) => {
      // User-initiated requests have highest priority
      if (a.userInitiated && !b.userInitiated) return -1;
      if (!a.userInitiated && b.userInitiated) return 1;
      
      // Quick queries before complex ones
      if (a.complexity < b.complexity) return -1;
      if (a.complexity > b.complexity) return 1;
      
      // Cached queries before new ones
      if (a.cached && !b.cached) return -1;
      if (!a.cached && b.cached) return 1;
      
      return 0;
    });
  }
}
```

## Tool-Specific Optimizations

### GitHub Copilot Optimization

#### Instruction File Optimization
```markdown
<!-- Optimized instruction structure -->
# Task Creation Instructions

## Quick Reference
- Use {TASK_TITLE} for task name
- Include {ACCEPTANCE_CRITERIA} for validation
- Add {TESTING_STRATEGY} for quality assurance

## Conditional Logic (Optimized)
{IF_COMPLEX_TASK}
  Break down into subtasks:
  1. {SUBTASK_1}
  2. {SUBTASK_2}
  3. {SUBTASK_3}
{END_IF}

## Context Integration
Project: {PROJECT_TYPE}
Stack: {TECH_STACK}
Timeline: {TIMELINE}
```

#### Chatmode Performance
```markdown
<!-- Optimized chatmode configuration -->
# Enhanced Research Mode

## Performance Settings
- Max tokens: 2000 (optimized for speed)
- Temperature: 0.1 (consistent results)
- Cache duration: 300 seconds

## Quick Triggers
- "quick:" for perplexity_ask
- "deep:" for perplexity_research  
- "reason:" for perplexity_reason

## Context Limits
- Include only essential project context
- Limit technology stack to top 3 items
- Focus on current file context when relevant
```

### Cursor Optimization

#### Rules File Optimization
```yaml
# Optimized Cursor rules
performance:
  caching:
    enabled: true
    duration: 300 # 5 minutes
    levels: ["session", "project", "global"]
  
  mcp:
    timeout: 10000 # 10 seconds
    retries: 2
    batchSize: 3
  
  context:
    maxTokens: 2000
    priorityFields: ["techStack", "constraints", "timeline"]
    excludeFields: ["detailedHistory", "fullDependencyTree"]

research:
  quickMode:
    tool: "perplexity_ask"
    maxTokens: 1000
    timeout: 5000
  
  standardMode:
    tool: "perplexity_research"
    maxTokens: 2000
    timeout: 10000
  
  deepMode:
    tool: "perplexity_research"
    maxTokens: 3000
    timeout: 15000
```

#### JSON Mode Optimization
```json
{
  "modes": {
    "research": {
      "performance": {
        "cacheResults": true,
        "batchQueries": true,
        "parallelProcessing": true
      },
      "mcpIntegration": {
        "toolSelection": "automatic",
        "contextOptimization": true,
        "resultFiltering": true
      }
    }
  }
}
```

### Claude-Code Optimization

#### Agent Performance Tuning
```javascript
// Optimized agent configuration
class OptimizedResearchAgent {
  constructor() {
    this.cache = new IntelligentCache();
    this.requestQueue = new PriorityQueue();
    this.contextOptimizer = new ContextOptimizer();
  }

  async processRequest(query, context) {
    // Check cache first
    const cached = await this.cache.get(query, context);
    if (cached && !this.isStale(cached)) {
      return cached.result;
    }

    // Optimize context for MCP request
    const optimizedContext = this.contextOptimizer.optimize(context);
    
    // Select optimal MCP tool
    const tool = this.selectOptimalTool(query, optimizedContext);
    
    // Make request with timeout and retry logic
    const result = await this.makeOptimizedRequest(tool, query, optimizedContext);
    
    // Cache result
    this.cache.set(query, context, result);
    
    return result;
  }
}
```

#### Command Optimization
```javascript
// Optimized slash command processing
class OptimizedCommandProcessor {
  async processCommand(command, args, context) {
    // Parse and validate command quickly
    const parsedCommand = this.quickParse(command, args);
    
    // Check for cached results
    const cacheKey = this.generateCommandCacheKey(parsedCommand, context);
    const cached = this.commandCache.get(cacheKey);
    if (cached && !this.isExpired(cached)) {
      return cached.result;
    }
    
    // Route to appropriate agent with optimized context
    const agent = this.selectAgent(parsedCommand.type);
    const optimizedContext = this.optimizeContextForAgent(context, agent);
    
    // Process with performance monitoring
    const startTime = Date.now();
    const result = await agent.process(parsedCommand, optimizedContext);
    const duration = Date.now() - startTime;
    
    // Log performance metrics
    this.logPerformance(command, duration, result.quality);
    
    // Cache successful results
    if (result.success) {
      this.commandCache.set(cacheKey, { result, timestamp: Date.now() });
    }
    
    return result;
  }
}
```

## Context Optimization

### 1. Smart Context Selection
```javascript
class ContextOptimizer {
  optimize(fullContext) {
    return {
      // Essential context only
      projectType: fullContext.projectType,
      primaryTechStack: fullContext.techStack.slice(0, 3),
      criticalConstraints: fullContext.constraints.filter(c => c.priority === 'high'),
      currentPhase: fullContext.developmentPhase,
      
      // Exclude heavy context
      // fullDependencyTree: excluded for performance
      // detailedHistory: excluded for performance
      // allTeamMembers: excluded for performance
    };
  }

  adaptForTool(context, tool) {
    switch (tool) {
      case 'perplexity_ask':
        return this.minimizeForQuickQuery(context);
      case 'perplexity_research':
        return this.balanceForResearch(context);
      case 'perplexity_reason':
        return this.enrichForReasoning(context);
    }
  }
}
```

### 2. Dynamic Context Loading
```javascript
class DynamicContextLoader {
  async loadContext(query, tool) {
    const baseContext = await this.getBaseContext();
    
    // Load additional context based on query analysis
    const queryAnalysis = this.analyzeQuery(query);
    
    if (queryAnalysis.needsTechStack) {
      baseContext.techStack = await this.getTechStack();
    }
    
    if (queryAnalysis.needsConstraints) {
      baseContext.constraints = await this.getConstraints();
    }
    
    if (queryAnalysis.needsTimeline) {
      baseContext.timeline = await this.getTimeline();
    }
    
    return this.optimizeForTool(baseContext, tool);
  }
}
```

## Error Handling and Resilience

### 1. Graceful Degradation
```javascript
class ResilientMCPClient {
  async makeRequest(tool, query, context) {
    try {
      // Primary request with timeout
      return await this.requestWithTimeout(tool, query, context, 10000);
    } catch (error) {
      if (error.type === 'TIMEOUT') {
        // Retry with simplified context
        const simplifiedContext = this.simplifyContext(context);
        return await this.requestWithTimeout(tool, query, simplifiedContext, 15000);
      } else if (error.type === 'RATE_LIMIT') {
        // Queue request for later
        return await this.queueRequest(tool, query, context);
      } else {
        // Fallback to cached or offline mode
        return await this.fallbackResponse(query, context);
      }
    }
  }
}
```

### 2. Circuit Breaker Pattern
```javascript
class MCPCircuitBreaker {
  constructor() {
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.failureThreshold = 5;
    this.timeout = 60000; // 1 minute
  }

  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

## Monitoring and Analytics

### 1. Performance Monitoring
```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      responseTime: new Map(),
      cacheHitRate: new Map(),
      errorRate: new Map(),
      userSatisfaction: new Map()
    };
  }

  recordResponseTime(operation, duration) {
    const times = this.metrics.responseTime.get(operation) || [];
    times.push(duration);
    this.metrics.responseTime.set(operation, times.slice(-100)); // Keep last 100
  }

  calculateAverageResponseTime(operation) {
    const times = this.metrics.responseTime.get(operation) || [];
    return times.reduce((a, b) => a + b, 0) / times.length;
  }

  generatePerformanceReport() {
    return {
      averageResponseTimes: this.calculateAverageResponseTimes(),
      cacheHitRates: this.calculateCacheHitRates(),
      errorRates: this.calculateErrorRates(),
      recommendations: this.generateRecommendations()
    };
  }
}
```

### 2. User Experience Analytics
```javascript
class UXAnalytics {
  trackUserInteraction(interaction) {
    this.interactions.push({
      timestamp: Date.now(),
      type: interaction.type,
      duration: interaction.duration,
      success: interaction.success,
      userSatisfaction: interaction.rating
    });
  }

  identifyPerformanceBottlenecks() {
    const slowInteractions = this.interactions.filter(i => i.duration > 10000);
    const failedInteractions = this.interactions.filter(i => !i.success);
    const lowSatisfaction = this.interactions.filter(i => i.userSatisfaction < 3);
    
    return {
      slowOperations: this.groupByType(slowInteractions),
      failurePatterns: this.analyzeFailurePatterns(failedInteractions),
      satisfactionIssues: this.analyzeSatisfactionIssues(lowSatisfaction)
    };
  }
}
```

## Best Practices Summary

### 1. Query Optimization
- Use specific, focused queries rather than broad questions
- Include only essential context information
- Batch related queries when possible
- Select appropriate MCP tools based on query type

### 2. Caching Strategy
- Implement multi-level caching (session, project, global)
- Use intelligent cache invalidation based on content changes
- Cache frequently requested information with appropriate TTL
- Prioritize cache hits for better performance

### 3. Resource Management
- Monitor API rate limits and implement queuing
- Use circuit breaker pattern for resilience
- Implement graceful degradation for failures
- Optimize memory usage with smart context loading

### 4. User Experience
- Provide immediate feedback for long-running operations
- Implement progressive disclosure of information
- Use appropriate response times for different operation types
- Monitor user satisfaction and adjust accordingly

This optimization guide ensures that Task-Master prompt adaptations provide excellent performance while maintaining high quality and reliability across all supported tools.
