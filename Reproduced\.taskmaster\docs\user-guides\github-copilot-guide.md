# GitHub Copilot Task-Master Integration Guide

## Overview
This guide explains how to use the adapted Task-Master prompts with GitHub Copilot, including the enhanced research capabilities through Perplexity MCP integration.

## Quick Start

### 1. Setup
1. Ensure you have GitHub Copilot subscription and VS Code extension installed
2. Copy the `.github` folder to your project root
3. Configure MCP Perplexity server (optional but recommended for enhanced research)
4. Restart VS Code to load the new configurations

### 2. Basic Usage
GitHub Copilot will automatically use the Task-Master instructions when you:
- Create or modify tasks in your project
- Ask for help with task planning or expansion
- Request research on development topics
- Need complexity analysis for features

## Available Features

### Task Creation Instructions
**File**: `.github/instructions/add-task.instructions.md`

**Usage**: When creating new development tasks
```
Create a task for implementing user authentication with OAuth 2.0
```

**What it does**:
- Provides structured task creation guidance
- Includes acceptance criteria templates
- Suggests testing strategies
- Considers project context and constraints

### Task Expansion Instructions
**File**: `.github/instructions/expand-task.instructions.md`

**Usage**: When breaking down complex features
```
Expand the shopping cart feature into detailed subtasks
```

**What it does**:
- Breaks complex tasks into manageable subtasks
- Identifies dependencies between tasks
- Provides implementation guidance for each subtask
- Includes testing and validation strategies

### Complexity Analysis Instructions
**File**: `.github/instructions/analyze-complexity.instructions.md`

**Usage**: When assessing feature complexity
```
Analyze the complexity of implementing real-time notifications
```

**What it does**:
- Evaluates technical complexity across multiple dimensions
- Identifies potential risks and challenges
- Suggests mitigation strategies
- Provides effort estimation guidance

### Enhanced Research Capabilities

#### Standard Research Instructions
**File**: `.github/instructions/research.instructions.md`

**Usage**: For general development research
```
Research best practices for React state management in large applications
```

#### MCP-Enhanced Research Instructions
**File**: `.github/instructions/mcp-research.instructions.md`

**Usage**: For real-time web research (requires MCP setup)
```
Research the latest security vulnerabilities in Node.js dependencies
```

**Enhanced capabilities**:
- Real-time web search through Perplexity
- Current information beyond training data
- Proper citations and sources
- Context-aware recommendations

### Research Chatmodes

#### Enhanced Research Mode
**File**: `.github/chatmodes/enhanced-research-mode.chatmode.md`

**Activation**: Type `@enhanced-research-mode` in Copilot Chat

**Features**:
- Four research modes: Quick, Standard, Deep Analysis, Reasoning
- Automatic MCP tool selection based on query type
- Project context integration
- Current technology stack awareness

**Example Usage**:
```
@enhanced-research-mode
Quick research on React 18 concurrent features for our e-commerce platform
```

#### Project Context Mode
**File**: `.github/chatmodes/project-context-mode.chatmode.md`

**Activation**: Type `@project-context-mode` in Copilot Chat

**Features**:
- Automatic project analysis and context injection
- Technology stack detection
- Architecture pattern recognition
- Tailored recommendations based on project structure

#### Task Planning Mode
**File**: `.github/chatmodes/task-planning-mode.chatmode.md`

**Activation**: Type `@task-planning-mode` in Copilot Chat

**Features**:
- Structured task planning workflows
- Dependency analysis and sequencing
- Resource estimation and timeline planning
- Risk assessment and mitigation strategies

## MCP Perplexity Integration

### Setup Requirements
1. Install MCP Perplexity server: `npm install -g @perplexity/mcp-server`
2. Set environment variable: `PERPLEXITY_API_KEY=your_api_key`
3. Configure VS Code settings for MCP integration

### Enhanced Research Workflow

#### Quick Research
For immediate answers and current information:
```
@enhanced-research-mode
Quick: What are the new features in TypeScript 5.3?
```

**MCP Tool Used**: `perplexity_ask`
**Response Time**: ~2-3 seconds
**Output**: Concise, current information with key points

#### Standard Research
For comprehensive analysis with multiple sources:
```
@enhanced-research-mode
Research: Compare React state management solutions for enterprise applications
```

**MCP Tool Used**: `perplexity_research`
**Response Time**: ~5-8 seconds
**Output**: Detailed analysis with citations and implementation guidance

#### Deep Analysis
For exhaustive research with expert opinions:
```
@enhanced-research-mode
Deep: Analyze microservices vs monolith architecture for our SaaS platform
```

**MCP Tool Used**: `perplexity_research` (extended)
**Response Time**: ~8-12 seconds
**Output**: Comprehensive analysis with case studies and future considerations

#### Reasoning Mode
For systematic problem-solving and decision support:
```
@enhanced-research-mode
Reason: Choose the best database for high-traffic e-commerce application
```

**MCP Tool Used**: `perplexity_reason`
**Response Time**: ~6-10 seconds
**Output**: Logical analysis with justified recommendations

### Context Integration
The MCP integration automatically includes:
- Current project technology stack
- Package.json dependencies and versions
- README.md project description
- Recent git commits and changes
- Current file and directory context

## Best Practices

### 1. Task Creation
- Be specific about requirements and constraints
- Include acceptance criteria in your requests
- Mention target users and use cases
- Specify technical constraints and preferences

**Good Example**:
```
Create a task for implementing user authentication using OAuth 2.0 with Google and GitHub providers. 
The system should support role-based access control and work with our existing React/Node.js stack.
```

### 2. Task Expansion
- Start with high-level feature descriptions
- Mention any known dependencies or constraints
- Specify the level of detail needed
- Include testing and deployment considerations

**Good Example**:
```
Expand the real-time chat feature into detailed subtasks. Consider WebSocket implementation, 
message persistence, user presence, and mobile app integration.
```

### 3. Research Queries
- Be specific about your context and requirements
- Mention your technology stack and constraints
- Specify the type of information you need
- Include timeline and team considerations

**Good Example**:
```
Research API rate limiting strategies for our Node.js microservices. We expect 10k requests/minute 
and need to consider both per-user and global limits. Team has experience with Redis.
```

### 4. Complexity Analysis
- Provide detailed feature descriptions
- Mention team size and experience level
- Include timeline constraints
- Specify risk tolerance and quality requirements

**Good Example**:
```
Analyze complexity of implementing real-time collaborative editing like Google Docs. 
Team of 4 developers, 3-month timeline, high reliability requirements.
```

## Troubleshooting

### Common Issues

#### 1. Instructions Not Applied
**Problem**: Copilot doesn't seem to use the Task-Master instructions
**Solution**: 
- Ensure `.github/instructions/` files are in project root
- Restart VS Code to reload configurations
- Use specific keywords that trigger the instructions

#### 2. MCP Integration Not Working
**Problem**: Enhanced research mode falls back to standard responses
**Solution**:
- Verify MCP server is running: `npx @perplexity/mcp-server --port 3000`
- Check API key configuration: `echo $PERPLEXITY_API_KEY`
- Review VS Code MCP settings and logs

#### 3. Context Not Recognized
**Problem**: Responses don't consider project context
**Solution**:
- Ensure project has clear README.md and package.json
- Use project-context-mode chatmode for better context injection
- Explicitly mention your technology stack in queries

#### 4. Chatmodes Not Available
**Problem**: Custom chatmodes don't appear in Copilot Chat
**Solution**:
- Verify `.github/chatmodes/` files are properly formatted
- Check file extensions are `.chatmode.md`
- Restart VS Code and refresh Copilot Chat

### Performance Optimization

#### 1. Faster Responses
- Use Quick research mode for immediate answers
- Cache frequently researched topics
- Use specific, focused queries rather than broad questions

#### 2. Better Context
- Maintain clear project documentation
- Use descriptive commit messages
- Keep package.json dependencies up to date

#### 3. Quality Improvement
- Provide specific requirements and constraints
- Include examples of desired output format
- Mention team expertise and preferences

## Advanced Usage

### Custom Instructions
You can create custom instruction files for specific project needs:

1. Create new `.md` file in `.github/instructions/`
2. Follow the existing instruction format
3. Use placeholder syntax: `{VARIABLE_NAME}`
4. Include conditional logic with `{IF_CONDITION}...{END_IF}`

### Project-Specific Chatmodes
Create specialized chatmodes for your project:

1. Create new `.chatmode.md` file in `.github/chatmodes/`
2. Define specific context and workflows
3. Include project-specific examples and patterns
4. Test with your team's common use cases

### Integration with CI/CD
Integrate Task-Master prompts with your development workflow:

1. Use task creation instructions in issue templates
2. Include complexity analysis in feature planning
3. Leverage research capabilities for technology decisions
4. Document decisions and rationale for future reference

## Examples and Templates

### Task Creation Template
```
Create a task for {FEATURE_DESCRIPTION}

Requirements:
- {REQUIREMENT_1}
- {REQUIREMENT_2}
- {REQUIREMENT_3}

Constraints:
- Technology stack: {TECH_STACK}
- Timeline: {TIMELINE}
- Team size: {TEAM_SIZE}

Acceptance criteria should include:
- Functional requirements
- Performance requirements
- Security considerations
- Testing strategy
```

### Research Query Template
```
@enhanced-research-mode
Research {TOPIC} for {PROJECT_CONTEXT}

Focus areas:
- {FOCUS_AREA_1}
- {FOCUS_AREA_2}
- {FOCUS_AREA_3}

Constraints:
- {CONSTRAINT_1}
- {CONSTRAINT_2}

Please include current best practices, implementation examples, and potential challenges.
```

## Support and Resources

### Documentation
- [Task-Master Original Documentation](../taskmaster/README.md)
- [MCP Perplexity Setup Guide](../mcp-perplexity/setup/installation-guide.md)
- [Conversion Scripts Documentation](../scripts/README.md)

### Community
- GitHub Issues for bug reports and feature requests
- Discussions for questions and community support
- Examples repository with real-world usage patterns

### Updates
- Follow the repository for updates and new features
- Check release notes for breaking changes
- Subscribe to notifications for security updates

This guide provides everything you need to effectively use Task-Master prompts with GitHub Copilot, from basic setup to advanced customization and MCP integration for enhanced research capabilities.
