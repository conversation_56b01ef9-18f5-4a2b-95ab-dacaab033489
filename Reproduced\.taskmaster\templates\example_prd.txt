<context>
# Vue d'ensemble  
[Donnez une vue d'ensemble de votre produit. Expliquez quel problème il résout, à qui il s'adresse et pourquoi il est précieux.]

# Fonctionnalités principales  
[Listez et décrivez les principales fonctionnalités de votre produit. Pour chaque fonctionnalité, précisez :
- Ce qu'elle fait
- Pourquoi elle est importante
- Comment elle fonctionne à un niveau général]

# Expérience utilisateur  
[Décrivez le parcours et l'expérience utilisateur. Incluez :
- Les personas utilisateurs
- Les principaux parcours utilisateurs
- Les considérations UI/UX]
</context>
<PRD>
# Architecture technique  
[Décrivez les détails de l'implémentation technique :
- Composants du système
- Modèles de données
- APIs et intégrations
- Besoins en infrastructure]

# Feuille de route de développement  
[Décomposez le processus de développement en phases :
- Exigences MVP
- Améliorations futures
- Ne tenez pas compte des délais — l'important est de détailler précisément ce qui doit être construit à chaque phase pour pouvoir ensuite le découper en tâches]

# Chaîne logique de dépendances
[Définissez l'ordre logique de développement :
- Quelles fonctionnalités doivent être développées en premier (fondations)
- Aller le plus vite possible vers un front-end utilisable/visible qui fonctionne
- Bien rythmer et cadrer chaque fonctionnalité pour qu'elle soit atomique mais puisse aussi être améliorée au fil du développement]

# Risques et mesures d'atténuation  
[Identifiez les risques potentiels et comment ils seront traités :
- Défis techniques
- Définir le MVP sur lequel s'appuyer
- Contraintes de ressources]

# Annexe  
[Ajoutez toute information supplémentaire :
- Résultats de recherche
- Spécifications techniques]
</PRD>