# Système de gestion des prompts Task Master

Ce répertoire contient les templates de prompts centralisés pour toutes les fonctionnalités IA de Task Master.

## Vue d’ensemble

Le système de gestion des prompts offre :
- **Stockage centralisé** : Tous les prompts dans un seul emplacement (`/src/prompts`)
- **Validation par schéma JSON** : Validation complète via AJV avec des rapports d’erreurs détaillés
- **Gestion de versions** : Suivi des modifications des prompts dans le temps
- **Support des variantes** : Prompts adaptés à différents contextes (mode recherche, niveaux de complexité, etc.)
- **Variables de template** : Génération dynamique de prompts avec substitution de variables
- **Intégration IDE** : Support d’IntelliSense et validation dans VS Code

## Structure du répertoire

```
src/prompts/
├── README.md                # Ce fichier
├── schemas/                 # Schémas JSON pour la validation
│   ├── README.md           # Documentation des schémas
│   ├── prompt-template.schema.json  # Schéma principal des templates
│   ├── parameter.schema.json        # Schéma de validation des paramètres
│   └── variant.schema.json          # Schéma des variantes de prompts
├── parse-prd.json          # Prompts pour l’analyse de PRD
├── expand-task.json        # Prompts pour la décomposition de tâches
├── add-task.json           # Prompts pour la création de tâches
├── update-tasks.json       # Prompts pour la mise à jour en masse
├── update-task.json        # Prompts pour la mise à jour individuelle
├── update-subtask.json     # Prompts pour la mise à jour de sous-tâches
├── analyze-complexity.json # Prompts d’analyse de complexité
└── research.json           # Prompts pour les requêtes de recherche
```

## Validation par schéma

Tous les templates de prompts sont validés selon les schémas JSON situés dans `/src/prompts/schemas/`. Le système de validation assure :

- **Validation structurelle** : Présence des champs obligatoires et bonne imbrication
- **Vérification des types de paramètres** : Types, motifs et plages de valeurs
- **Syntaxe de template** : Validation de la syntaxe Handlebars et des références de variables
- **Versionnement sémantique** : Format de version conforme au semver
- **Validation croisée** : Correspondance entre paramètres et variables de template

### Fonctionnalités de validation
- **Champs obligatoires** : `id`, `version`, `description`, `prompts.default`
- **Sécurité des types** : Validation des types string, number, boolean, array, object
- **Validation par motif** : Regex pour les paramètres string
- **Validation de plage** : Valeurs min/max pour les paramètres numériques
- **Contraintes d’énumération** : Valeurs restreintes pour les paramètres catégoriels

## Flux de développement

### Mise en place de l’environnement
1. **Intégration VS Code** : Les schémas sont configurés automatiquement pour l’IntelliSense
2. **Dépendances** : `ajv` et `ajv-formats` sont nécessaires pour la validation
3. **Surveillance des fichiers** : Les modifications des templates déclenchent une validation automatique

### Création de nouveaux prompts
1. Créez un nouveau fichier `.json` dans `/src/prompts/`
2. Suivez la structure du schéma (voir section Structure du template)
3. Définissez les paramètres avec les bons types et validations
4. Rédigez les prompts system et user avec des variables de template
5. Testez avec le PromptManager avant de valider

### Modification de prompts existants
1. Mettez à jour le champ `version` en respectant le versionnement sémantique
2. Préservez la rétrocompatibilité si possible
3. Testez avec le code existant qui utilise le prompt
4. Mettez à jour la documentation si les paramètres changent

## Référence des modèles de prompts

### 1. parse-prd.json
**But** : Analyser un document de spécification produit (PRD) en tâches structurées  
**Variantes** : `default`, `research` (lorsque le mode recherche est activé)

**Paramètres obligatoires** :
- `numTasks` (number) : Nombre cible de tâches à générer
- `nextId` (number) : ID de départ pour les tâches
- `prdContent` (string) : Contenu du fichier PRD
- `prdPath` (string) : Chemin du fichier PRD
- `defaultTaskPriority` (string) : Priorité par défaut pour les tâches générées

**Paramètres optionnels** :
- `research` (boolean) : Active le mode recherche pour les meilleures pratiques récentes (défaut : false)

**Utilisation** : Utilisé par la commande `task-master parse-prd` pour convertir un PRD en liste de tâches actionnables.

### 2. add-task.json
**But** : Générer une nouvelle tâche à partir d'une description utilisateur  
**Variantes** : `default`, `research` (lorsque le mode recherche est activé)

**Paramètres obligatoires** :
- `prompt` (string) : Description de la tâche par l'utilisateur
- `newTaskId` (number) : ID de la nouvelle tâche

**Paramètres optionnels** :
- `existingTasks` (array) : Liste des tâches existantes pour le contexte
- `gatheredContext` (string) : Contexte issu de l'analyse du code
- `contextFromArgs` (string) : Contexte additionnel fourni manuellement
- `priority` (string) : Priorité de la tâche (high/medium/low, défaut : medium)
- `dependencies` (array) : IDs des tâches dépendantes
- `useResearch` (boolean) : Active le mode recherche (défaut : false)

**Utilisation** : Utilisé par la commande `task-master add-task` pour créer de nouvelles tâches avec l'IA.

### 3. expand-task.json
**But** : Décomposer une tâche en sous-tâches détaillées selon trois stratégies avancées  
**Variantes** : `complexity-report` (si expansionPrompt existe), `research` (mode recherche activé), `default` (cas standard)

**Paramètres obligatoires** :
- `subtaskCount` (number) : Nombre de sous-tâches à générer
- `task` (object) : Tâche à décomposer
- `nextSubtaskId` (number) : ID de départ pour les sous-tâches

**Paramètres optionnels** :
- `additionalContext` (string) : Contexte additionnel pour la décomposition (défaut : "")
- `complexityReasoningContext` (string) : Contexte d'analyse de complexité (défaut : "")
- `gatheredContext` (string) : Contexte projet collecté (défaut : "")
- `useResearch` (boolean) : Active le mode recherche (défaut : false)
- `expansionPrompt` (string) : Prompt d'expansion issu du rapport de complexité

**Stratégie de sélection des variantes** :
1. **complexity-report** : Utilisée si `expansionPrompt` existe (priorité haute)
2. **research** : Utilisée si `useResearch === true && !expansionPrompt`
3. **default** : Stratégie standard par défaut

**Utilisation** : Utilisé par la commande `task-master expand` pour décomposer les tâches complexes en sous-tâches adaptées au contexte et à la complexité.

### 4. update-task.json
**But** : Mettre à jour une tâche avec de nouvelles informations, en mode complet ou ajout  
**Variantes** : `default`, `append` (si appendMode est true), `research` (mode recherche activé)

**Paramètres obligatoires** :
- `task` (object) : Tâche à mettre à jour
- `taskJson` (string) : Représentation JSON de la tâche
- `updatePrompt` (string) : Description des modifications à appliquer

**Paramètres optionnels** :
- `appendMode` (boolean) : Ajoute aux détails ou effectue une mise à jour complète (défaut : false)
- `useResearch` (boolean) : Active le mode recherche (défaut : false)
- `currentDetails` (string) : Détails actuels de la tâche (défaut : "(No existing details)")
- `gatheredContext` (string) : Contexte projet additionnel

**Utilisation** : Utilisé par la commande `task-master update-task` pour modifier des tâches existantes.

### 5. update-tasks.json
**But** : Mettre à jour plusieurs tâches selon un nouveau contexte ou des changements  
**Variantes** : `default`, `research` (mode recherche activé)

**Paramètres obligatoires** :
- `tasks` (array) : Tableau de tâches à mettre à jour
- `updatePrompt` (string) : Description des modifications à appliquer

**Paramètres optionnels** :
- `useResearch` (boolean) : Active le mode recherche (défaut : false)
- `projectContext` (string) : Contexte projet additionnel

**Utilisation** : Utilisé par la commande `task-master update` pour la mise à jour en masse.

### 6. update-subtask.json
**But** : Ajouter des informations à une sous-tâche en générant uniquement du contenu nouveau  
**Variantes** : `default`, `research` (mode recherche activé)

**Paramètres obligatoires** :
- `parentTask` (object) : Contexte de la tâche parente
- `currentDetails` (string) : Détails actuels de la sous-tâche (défaut : "(No existing details)")
- `updatePrompt` (string) : Demande utilisateur pour l'ajout

**Paramètres optionnels** :
- `prevSubtask` (object) : Sous-tâche précédente si existante
- `nextSubtask` (object) : Sous-tâche suivante si existante
- `useResearch` (boolean) : Active le mode recherche (défaut : false)
- `gatheredContext` (string) : Contexte projet additionnel

**Utilisation** : Utilisé par la commande `task-master update-subtask` pour consigner l'avancement et les résultats sur les sous-tâches.

### 7. analyze-complexity.json
**But** : Analyser la complexité des tâches et générer des recommandations de décomposition  
**Variantes** : `default`, `research` (mode recherche activé), `batch` (si analyse de plus de 10 tâches)

**Paramètres obligatoires** :
- `tasks` (array) : Tableau de tâches à analyser

**Paramètres optionnels** :
- `gatheredContext` (string) : Contexte projet additionnel
- `threshold` (number) : Seuil de complexité pour recommander la décomposition (1-10, défaut : 5)
- `useResearch` (boolean) : Mode recherche pour analyse approfondie (défaut : false)

**Utilisation** : Utilisé par la commande `task-master analyze-complexity` pour déterminer les tâches à décomposer.

### 8. research.json
**But** : Effectuer une recherche assistée par IA avec le contexte projet  
**Variantes** : `default`, `low` (réponses concises), `medium` (équilibré), `high` (détaillé)

**Paramètres obligatoires** :
- `query` (string) : Requête de recherche

**Paramètres optionnels** :
- `gatheredContext` (string) : Contexte projet collecté
- `detailLevel` (string) : Niveau de détail (low/medium/high, défaut : medium)
- `projectInfo` (object) : Informations projet avec propriétés :
  - `root` (string) : Chemin racine du projet
  - `taskCount` (number) : Nombre de tâches concernées
  - `fileCount` (number) : Nombre de fichiers concernés

**Utilisation** : Utilisé par la commande `task-master research` pour obtenir des informations et conseils contextuels.

## Template Structure

Each prompt template is a JSON file with the following structure:

```json
{
  "id": "unique-identifier",
  "version": "1.0.0",
  "description": "What this prompt does",
  "metadata": {
    "author": "system",
    "created": "2024-01-01T00:00:00Z",
    "updated": "2024-01-01T00:00:00Z",
    "tags": ["category", "feature"],
    "category": "task"
  },
  "parameters": {
    "paramName": {
      "type": "string|number|boolean|array|object",
      "required": true|false,
      "default": "default value",
      "description": "Parameter description",
      "enum": ["option1", "option2"],
      "pattern": "^[a-z]+$",
      "minimum": 1,
      "maximum": 100
    }
  },
  "prompts": {
    "default": {
      "system": "System prompt template",
      "user": "User prompt template"
    },
    "variant-name": {
      "condition": "JavaScript expression",
      "system": "Variant system prompt",
      "user": "Variant user prompt",
      "metadata": {
        "description": "When to use this variant"
      }
    }
  }
}
```

## Fonctionnalités des templates

### Substitution de variables
Utilisez `{{variableName}}` pour injecter des valeurs dynamiques :
```
"user": "Analyse ces {{tasks.length}} tâches avec un seuil de {{threshold}}"
```

### Conditionnels
Utilisez `{{#if variable}}...{{/if}}` pour afficher du contenu conditionnellement :
```
"user": "{{#if useResearch}}Recherche et {{/if}}crée une tâche"
```

### Fonctions d’assistance (Helpers)

#### Helper d’égalité
Utilisez `{{#if (eq variable "valeur")}}...{{/if}}` pour comparer des chaînes :
```
"user": "{{#if (eq detailLevel \"low\")}}Fournir un résumé bref{{/if}}"
"user": "{{#if (eq priority \"high\")}}URGENT : {{/if}}{{taskTitle}}"
```

Le helper `eq` permet une logique conditionnelle claire basée sur les valeurs des paramètres :
- Comparer des chaînes : `(eq detailLevel "medium")`
- Comparer avec des valeurs d’énumération : `(eq status "pending")`
- Conditions multiples : `{{#if (eq level "1")}}Premier{{/if}}{{#if (eq level "2")}}Deuxième{{/if}}`

#### Helper de négation
Utilisez `{{#if (not variable)}}...{{/if}}` pour des conditions négatives :
```
"user": "{{#if (not useResearch)}}Utiliser une analyse basique{{/if}}"
"user": "{{#if (not hasSubtasks)}}Cette tâche n’a pas de sous-tâches{{/if}}"
```

Le helper `not` permet une logique négative claire :
- Négation de booléens : `(not useResearch)`
- Négation de valeurs truthy/falsy : `(not emptyArray)`
- Plus propre que des paramètres booléens inversés : inutile d’ajouter des flags `notUseResearch`

#### Helpers de comparaison numérique
Utilisez `{{#if (gt variable nombre)}}...{{/if}}` pour « supérieur à » :
```
"user": "générer {{#if (gt numTasks 0)}}environ {{numTasks}}{{else}}un nombre approprié de{{/if}} tâches de développement principales"
"user": "{{#if (gt complexity 5)}}Ceci est une tâche complexe{{/if}}"
"system": "créer {{#if (gt subtaskCount 0)}}exactement {{subtaskCount}}{{else}}un nombre approprié de{{/if}} sous-tâches"
```

Utilisez `{{#if (gte variable nombre)}}...{{/if}}` pour « supérieur ou égal à » :
```
"user": "{{#if (gte priority 8)}}HAUTE PRIORITÉ{{/if}}"
"user": "{{#if (gte threshold 1)}}Analyse activée{{/if}}"
"system": "{{#if (gte complexityScore 8)}}Utiliser une approche détaillée{{/if}}"
```

Les helpers numériques permettent une logique conditionnelle avancée :
- **Comptage dynamique** : `{{#if (gt numTasks 0)}}exactement {{numTasks}}{{else}}un nombre approprié de{{/if}}`
- **Comportement basé sur un seuil** : `(gte complexityScore 8)` pour la gestion des tâches complexes
- **Vérification de zéro** : `(gt subtaskCount 0)` pour du contenu conditionnel
- **Support des décimales** : `(gt score 7.5)` pour les comparaisons fractionnaires
- **Sophistication accrue** : Permet des prompts avancés pour parse-prd et expand-task

### Boucles
Utilisez `{{#each array}}...{{/each}}` pour itérer sur des tableaux :
```
"user": "Tâches :\n{{#each tasks}}- {{id}} : {{title}}\n{{/each}}"
```

### Variables spéciales en boucle
Dans les blocs `{{#each}}`, vous avez accès à :
- `{{@index}}` : Index courant (démarre à 0)
- `{{@first}}` : Booléen, vrai pour le premier élément
- `{{@last}}` : Booléen, vrai pour le dernier élément

```
"user": "{{#each tasks}}{{@index}}. {{title}}{{#unless @last}}\n{{/unless}}{{/each}}"
```

### Sérialisation JSON
Utilisez `{{{json variable}}}` (triple accolades) pour sérialiser des objets/tableaux en JSON :
```
"user": "Analyse ces tâches : {{{json tasks}}}"
```

### Propriétés imbriquées
Accédez aux propriétés imbriquées avec la notation pointée :
```
"user": "Projet : {{context.projectName}}"
```

## Variantes de prompts

Les variantes permettent d’adapter les prompts selon des conditions :

```json
{
  "prompts": {
    "default": {
      "system": "Prompt système par défaut",
      "user": "Prompt utilisateur par défaut"
    },
    "research": {
      "condition": "useResearch === true",
      "system": "Prompt système orienté recherche",
      "user": "Prompt utilisateur orienté recherche"
    },
    "high-complexity": {
      "condition": "complexityScore >= 8",
      "system": "Prompt pour gestion de tâches complexes",
      "user": "Demande de décomposition détaillée"
    }
  }
}
```

### Évaluation des conditions
Les conditions sont des expressions JavaScript évaluées avec les paramètres en contexte :
- Comparaisons simples : `useResearch === true`
- Comparaisons numériques : `threshold >= 5`
- Correspondance de chaînes : `priority === 'high'`
- Logique complexe : `useResearch && threshold > 7`

## Module PromptManager

Le PromptManager est implémenté dans `scripts/modules/prompt-manager.js` et fournit :
- **Chargement et cache des templates** : Les templates sont chargés une fois et mis en cache pour la performance
- **Validation par schéma** : Validation complète via AJV avec des messages d’erreur détaillés
- **Substitution de variables** : Syntaxe type Handlebars pour le contenu dynamique
- **Sélection de variantes** : Sélection automatique selon les conditions
- **Gestion des erreurs** : Fallbacks robustes et messages d’erreur explicites
- **Singleton** : Une instance par racine de projet pour l’efficacité

### Comportement de validation
- **Schéma disponible** : Validation complète avec messages d’erreur détaillés
- **Schéma manquant** : Validation structurelle basique en secours
- **Templates invalides** : Erreurs descriptives avec détails par champ
- **Validation des paramètres** : Vérification des types, motifs, plages de valeurs

## Usage in Code

### Basic Usage
```javascript
import { getPromptManager } from '../prompt-manager.js';

const promptManager = getPromptManager();
const { systemPrompt, userPrompt, metadata } = promptManager.loadPrompt('add-task', {
  // Parameters matching the template's parameter definitions
  prompt: 'Create a user authentication system',
  newTaskId: 5,
  priority: 'high',
  useResearch: false
});

// Use with AI service
const result = await generateObjectService({
  systemPrompt,
  prompt: userPrompt,
  // ... other AI parameters
});
```

### With Variants
```javascript
// Research variant will be selected automatically
const { systemPrompt, userPrompt } = promptManager.loadPrompt('expand-task', {
  useResearch: true,  // Triggers research variant
  task: taskObject,
  subtaskCount: 5
});
```

### Error Handling
```javascript
try {
  const result = promptManager.loadPrompt('invalid-template', {});
} catch (error) {
  if (error.message.includes('Schema validation failed')) {
    console.error('Template validation error:', error.message);
  } else if (error.message.includes('not found')) {
    console.error('Template not found:', error.message);
  }
}

## Ajout de nouveaux prompts

1. **Créez le fichier JSON** en suivant la structure de template
2. **Définissez les paramètres** avec les bons types, validations et descriptions
3. **Créez les prompts** avec des templates clairs pour system et user
4. **Utilisez les variables de template** pour le contenu dynamique
5. **Ajoutez des variantes** si besoin pour différents contextes
6. **Testez soigneusement** avec le PromptManager
7. **Mettez à jour cette documentation** avec les détails du nouveau prompt

### Exemple de nouveau prompt
```json
{
  "id": "new-feature",
  "version": "1.0.0",
  "description": "Générer du code pour une nouvelle fonctionnalité",
  "parameters": {
    "featureName": {
      "type": "string",
      "required": true,
      "pattern": "^[a-zA-Z][a-zA-Z0-9-]*$",
      "description": "Nom de la fonctionnalité à implémenter"
    },
    "complexity": {
      "type": "string",
      "required": false,
      "enum": ["simple", "medium", "complex"],
      "default": "medium",
      "description": "Niveau de complexité de la fonctionnalité"
    }
  },
  "prompts": {
    "default": {
      "system": "Vous êtes un ingénieur logiciel senior.",
      "user": "Créez une fonctionnalité {{complexity}} appelée {{featureName}}."
    }
  }
}
```

## Bonnes pratiques

### Conception des templates
1. **IDs clairs** : Utilisez le kebab-case, des identifiants explicites
2. **Versionnement sémantique** : Suivez semver pour la gestion des versions
3. **Paramètres complets** : Définissez tous les paramètres requis et optionnels
4. **Types sûrs** : Utilisez les bons types et validations
5. **Descriptions claires** : Documentez chaque prompt et paramètre

### Utilisation des variables
1. **Noms explicites** : Utilisez des noms de variables descriptifs
2. **Cohérence** : Respectez les conventions de nommage établies
3. **Valeurs par défaut sûres** : Fournissez des valeurs par défaut pertinentes
4. **Validation** : Utilisez patterns, enums et plages pour la validation

### Stratégie de variantes
1. **Conditions simples** : Gardez les conditions faciles à comprendre
2. **Objectif clair** : Chaque variante doit avoir un cas d’usage distinct
3. **Logique de repli** : Prévoyez toujours une variante par défaut
4. **Documentation** : Expliquez quand chaque variante est utilisée

### Performance
1. **Mise en cache** : Les templates sont automatiquement mis en cache
2. **Chargement paresseux** : Les templates se chargent à la demande
3. **Variantes minimales** : N’ajoutez pas de variantes inutiles
4. **Conditions efficaces** : Gardez l’évaluation des conditions rapide

## Tests des prompts

### Validation
```javascript
// Test de validation du schéma
const promptManager = getPromptManager();
const results = promptManager.validateAllPrompts();
console.log(`Valides : ${results.valid.length}, Erreurs : ${results.errors.length}`);
```

### Tests d’intégration
Lors de la modification des prompts, vérifiez :
- La substitution des variables avec des données réelles
- Le déclenchement correct des variantes selon les conditions
- La cohérence des réponses IA avec le comportement attendu
- La validation correcte de tous les paramètres
- La gestion des erreurs pour les entrées invalides

### Test rapide
```javascript
// Test du chargement de prompt et de la substitution de variables
const promptManager = getPromptManager();
const result = promptManager.loadPrompt('research', {
  query: 'Quelles sont les meilleures pratiques React récentes ?',
  detailLevel: 'medium',
  gatheredContext: 'Projet React avec TypeScript'
});
console.log('System :', result.systemPrompt);
console.log('User :', result.userPrompt);
console.log('Metadata :', result.metadata);
```

### Liste de vérification des tests
- [ ] Le template est valide selon le schéma
- [ ] Tous les paramètres requis sont définis
- [ ] La substitution des variables fonctionne
- [ ] Les variantes se déclenchent correctement
- [ ] Les messages d’erreur sont clairs et utiles
- [ ] Les performances sont acceptables en usage répété

## Dépannage

### Problèmes courants

**Erreurs de validation du schéma** :
- Vérifiez la présence des champs obligatoires
- Vérifiez que les types des paramètres correspondent au schéma
- Assurez-vous que la version respecte le semver
- Validez la syntaxe JSON

**Problèmes de substitution de variables** :
- Vérifiez que les noms de variables correspondent aux paramètres
- Vérifiez la syntaxe d’accès aux propriétés imbriquées
- Vérifiez la syntaxe d’itération sur les tableaux
- Testez avec des structures de données réelles

**Problèmes de sélection de variantes** :
- Vérifiez la syntaxe des conditions (JavaScript valide)
- Vérifiez que les valeurs des paramètres correspondent aux attentes
- Assurez-vous qu’une variante par défaut existe
- Testez l’évaluation des conditions avec des logs de debug

**Problèmes de performance** :
- Vérifiez l’absence de références circulaires dans les templates
- Vérifiez que la mise en cache fonctionne
- Surveillez la fréquence de chargement des templates
- Simplifiez les conditions complexes si besoin
