{"id": "analyze-complexity", "version": "1.0.0", "description": "Analyser la complexité des tâches et générer des recommandations d'expansion", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["analyse", "complexité", "expansion", "recommandations"]}, "parameters": {"tasks": {"type": "array", "required": true, "description": "Tableau de tâches à analyser"}, "gatheredContext": {"type": "string", "default": "", "description": "Contexte supplémentaire du projet"}, "threshold": {"type": "number", "default": 5, "min": 1, "max": 10, "description": "Seuil de complexité pour recommander une expansion"}, "useResearch": {"type": "boolean", "default": false, "description": "Utiliser le mode recherche pour une analyse approfondie"}}, "prompts": {"default": {"system": "Vous êtes un architecte logiciel et chef de projet expert analysant la complexité des tâches. Répondez uniquement avec le tableau JSON valide demandé.", "user": "Analysez les tâches suivantes pour déterminer leur complexité (échelle de 1 à 10) et recommandez le nombre de sous-tâches pour l'expansion. Fournissez un raisonnement bref et une invite d'expansion initiale pour chacune.{{#if useResearch}} Tenez compte des meilleures pratiques actuelles, des schémas d'implémentation courants et des standards de l'industrie dans votre analyse.{{/if}}\n\nTâches :\n{{{json tasks}}}\n{{#if gatheredContext}}\n\n# Contexte du projet\n\n{{gatheredContext}}\n{{/if}}\n\nRépondez UNIQUEMENT avec un tableau JSON valide correspondant au schéma :\n[\n  {\n    \"taskId\": <number>,\n    \"taskTitle\": \"<string>\",\n    \"complexityScore\": <number 1-10>,\n    \"recommendedSubtasks\": <number>,\n    \"expansionPrompt\": \"<string>\",\n    \"reasoning\": \"<string>\"\n  },\n  ...\n]\n\nN'incluez aucun texte explicatif, balisage markdown ou délimiteur de bloc de code avant ou après le tableau JSON."}}}