#!/usr/bin/env node

/**
 * MCP Perplexity Validation Suite
 * Validates MCP integration quality, accuracy, and performance
 */

const fs = require('fs').promises;
const path = require('path');
const https = require('https');

class MCPValidationSuite {
  constructor() {
    this.validationResults = {
      connectivity: [],
      accuracy: [],
      performance: [],
      quality: [],
      integration: []
    };
    this.config = {
      apiKey: process.env.PERPLEXITY_API_KEY,
      baseUrl: 'https://api.perplexity.ai',
      timeout: 30000,
      maxRetries: 3
    };
  }

  async runValidation() {
    console.log('🔍 Starting MCP Perplexity Validation Suite\n');

    try {
      // 1. Connectivity Validation
      console.log('📡 Validating MCP Connectivity...');
      await this.validateConnectivity();

      // 2. Accuracy Validation
      console.log('🎯 Validating Information Accuracy...');
      await this.validateAccuracy();

      // 3. Performance Validation
      console.log('⚡ Validating Performance...');
      await this.validatePerformance();

      // 4. Quality Validation
      console.log('✨ Validating Response Quality...');
      await this.validateQuality();

      // 5. Integration Validation
      console.log('🔗 Validating Tool Integration...');
      await this.validateIntegration();

      // Generate Report
      await this.generateValidationReport();

    } catch (error) {
      console.error('❌ Validation suite failed:', error.message);
      process.exit(1);
    }
  }

  async validateConnectivity() {
    const tests = [
      this.testBasicConnection,
      this.testToolAvailability,
      this.testRateLimiting,
      this.testErrorHandling
    ];

    for (const test of tests) {
      try {
        const result = await test.call(this);
        this.validationResults.connectivity.push(result);
        console.log(`  ✅ ${result.name}: ${result.status}`);
      } catch (error) {
        const result = { name: test.name, status: 'FAILED', error: error.message };
        this.validationResults.connectivity.push(result);
        console.log(`  ❌ ${test.name}: FAILED - ${error.message}`);
      }
    }
  }

  async testBasicConnection() {
    const startTime = Date.now();
    
    const response = await this.makePerplexityRequest('perplexity_ask', {
      messages: [{ role: 'user', content: 'Test connection' }]
    });

    const duration = Date.now() - startTime;

    if (!response.choices || !response.choices[0]) {
      throw new Error('Invalid response structure');
    }

    return {
      name: 'Basic Connection',
      status: 'PASSED',
      duration: `${duration}ms`,
      details: 'Successfully connected to Perplexity API'
    };
  }

  async testToolAvailability() {
    const tools = ['perplexity_ask', 'perplexity_research', 'perplexity_reason'];
    const availableTools = [];

    for (const tool of tools) {
      try {
        const response = await this.makePerplexityRequest(tool, {
          messages: [{ role: 'user', content: 'Test tool availability' }]
        });
        
        if (response.choices && response.choices[0]) {
          availableTools.push(tool);
        }
      } catch (error) {
        console.warn(`Tool ${tool} not available: ${error.message}`);
      }
    }

    if (availableTools.length !== tools.length) {
      throw new Error(`Missing tools: ${tools.filter(t => !availableTools.includes(t)).join(', ')}`);
    }

    return {
      name: 'Tool Availability',
      status: 'PASSED',
      details: `All tools available: ${availableTools.join(', ')}`
    };
  }

  async testRateLimiting() {
    // Test rate limiting behavior
    const requests = [];
    const startTime = Date.now();

    // Make multiple rapid requests
    for (let i = 0; i < 5; i++) {
      requests.push(
        this.makePerplexityRequest('perplexity_ask', {
          messages: [{ role: 'user', content: `Rate limit test ${i}` }]
        }).catch(error => ({ error: error.message }))
      );
    }

    const results = await Promise.all(requests);
    const errors = results.filter(r => r.error);
    const duration = Date.now() - startTime;

    return {
      name: 'Rate Limiting',
      status: 'PASSED',
      duration: `${duration}ms`,
      details: `${results.length - errors.length}/${results.length} requests succeeded`
    };
  }

  async testErrorHandling() {
    // Test error handling with invalid request
    try {
      await this.makePerplexityRequest('invalid_tool', {
        messages: [{ role: 'user', content: 'Test error handling' }]
      });
      
      throw new Error('Expected error for invalid tool');
    } catch (error) {
      if (error.message.includes('Expected error')) {
        throw error;
      }
      
      return {
        name: 'Error Handling',
        status: 'PASSED',
        details: 'Properly handles invalid requests'
      };
    }
  }

  async validateAccuracy() {
    const tests = [
      this.testInformationCurrency,
      this.testFactualAccuracy,
      this.testSourceCredibility,
      this.testCitationQuality
    ];

    for (const test of tests) {
      try {
        const result = await test.call(this);
        this.validationResults.accuracy.push(result);
        console.log(`  ✅ ${result.name}: ${result.status}`);
      } catch (error) {
        const result = { name: test.name, status: 'FAILED', error: error.message };
        this.validationResults.accuracy.push(result);
        console.log(`  ❌ ${test.name}: FAILED - ${error.message}`);
      }
    }
  }

  async testInformationCurrency() {
    // Test that information is current (post-training cutoff)
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'What are the latest features in TypeScript 5.3 released in 2023?' 
      }]
    });

    const content = response.choices[0].message.content;
    
    // Check for recent information indicators
    const hasRecentInfo = content.includes('2023') || 
                         content.includes('2024') || 
                         content.includes('latest') ||
                         content.includes('recent');

    if (!hasRecentInfo) {
      throw new Error('Response does not contain recent information');
    }

    return {
      name: 'Information Currency',
      status: 'PASSED',
      details: 'Response contains current information beyond training cutoff'
    };
  }

  async testFactualAccuracy() {
    // Test factual accuracy with verifiable information
    const response = await this.makePerplexityRequest('perplexity_ask', {
      messages: [{ 
        role: 'user', 
        content: 'What is the current stable version of Node.js?' 
      }]
    });

    const content = response.choices[0].message.content;
    
    // Check for version number format
    const hasVersionNumber = /\d+\.\d+\.\d+/.test(content);
    
    if (!hasVersionNumber) {
      throw new Error('Response does not contain specific version information');
    }

    return {
      name: 'Factual Accuracy',
      status: 'PASSED',
      details: 'Response contains specific, verifiable information'
    };
  }

  async testSourceCredibility() {
    // Test source credibility and citations
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'Research React 18 concurrent features with sources' 
      }]
    });

    const content = response.choices[0].message.content;
    
    // Check for citations and sources
    const hasCitations = content.includes('http') || 
                        content.includes('source') || 
                        content.includes('according to') ||
                        content.includes('[');

    if (!hasCitations) {
      throw new Error('Response does not contain proper citations');
    }

    return {
      name: 'Source Credibility',
      status: 'PASSED',
      details: 'Response includes proper citations and sources'
    };
  }

  async testCitationQuality() {
    // Test citation format and quality
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'Research best practices for API security with citations' 
      }]
    });

    const content = response.choices[0].message.content;
    
    // Check for quality indicators
    const hasQualityCitations = content.includes('official') || 
                               content.includes('documentation') || 
                               content.includes('OWASP') ||
                               content.includes('RFC');

    return {
      name: 'Citation Quality',
      status: hasQualityCitations ? 'PASSED' : 'WARNING',
      details: hasQualityCitations ? 'Citations include authoritative sources' : 'Citations could be more authoritative'
    };
  }

  async validatePerformance() {
    const tests = [
      this.testResponseTime,
      this.testThroughput,
      this.testConcurrency,
      this.testResourceUsage
    ];

    for (const test of tests) {
      try {
        const result = await test.call(this);
        this.validationResults.performance.push(result);
        console.log(`  ✅ ${result.name}: ${result.status}`);
      } catch (error) {
        const result = { name: test.name, status: 'FAILED', error: error.message };
        this.validationResults.performance.push(result);
        console.log(`  ❌ ${test.name}: FAILED - ${error.message}`);
      }
    }
  }

  async testResponseTime() {
    const measurements = [];
    
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      await this.makePerplexityRequest('perplexity_ask', {
        messages: [{ role: 'user', content: `Performance test ${i}` }]
      });
      
      measurements.push(Date.now() - startTime);
    }

    const avgTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    const maxTime = Math.max(...measurements);

    if (avgTime > 10000) { // 10 second threshold
      throw new Error(`Average response time too slow: ${avgTime}ms`);
    }

    return {
      name: 'Response Time',
      status: 'PASSED',
      details: `Average: ${Math.round(avgTime)}ms, Max: ${maxTime}ms`
    };
  }

  async testThroughput() {
    const startTime = Date.now();
    const requests = [];

    // Test throughput with parallel requests
    for (let i = 0; i < 3; i++) {
      requests.push(
        this.makePerplexityRequest('perplexity_ask', {
          messages: [{ role: 'user', content: `Throughput test ${i}` }]
        })
      );
    }

    await Promise.all(requests);
    const duration = Date.now() - startTime;
    const throughput = (requests.length / duration) * 1000; // requests per second

    return {
      name: 'Throughput',
      status: 'PASSED',
      details: `${throughput.toFixed(2)} requests/second`
    };
  }

  async testConcurrency() {
    // Test concurrent request handling
    const concurrentRequests = 3;
    const requests = [];

    for (let i = 0; i < concurrentRequests; i++) {
      requests.push(
        this.makePerplexityRequest('perplexity_ask', {
          messages: [{ role: 'user', content: `Concurrency test ${i}` }]
        })
      );
    }

    const results = await Promise.all(requests);
    const successful = results.filter(r => r.choices && r.choices[0]).length;

    if (successful !== concurrentRequests) {
      throw new Error(`Only ${successful}/${concurrentRequests} concurrent requests succeeded`);
    }

    return {
      name: 'Concurrency',
      status: 'PASSED',
      details: `${successful}/${concurrentRequests} concurrent requests succeeded`
    };
  }

  async testResourceUsage() {
    // Monitor resource usage during requests
    const initialMemory = process.memoryUsage();
    
    // Make several requests
    for (let i = 0; i < 5; i++) {
      await this.makePerplexityRequest('perplexity_ask', {
        messages: [{ role: 'user', content: `Resource test ${i}` }]
      });
    }

    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

    return {
      name: 'Resource Usage',
      status: 'PASSED',
      details: `Memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`
    };
  }

  async validateQuality() {
    const tests = [
      this.testResponseRelevance,
      this.testResponseCompleteness,
      this.testResponseClarity,
      this.testContextIntegration
    ];

    for (const test of tests) {
      try {
        const result = await test.call(this);
        this.validationResults.quality.push(result);
        console.log(`  ✅ ${result.name}: ${result.status}`);
      } catch (error) {
        const result = { name: test.name, status: 'FAILED', error: error.message };
        this.validationResults.quality.push(result);
        console.log(`  ❌ ${test.name}: FAILED - ${error.message}`);
      }
    }
  }

  async testResponseRelevance() {
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'Research React state management solutions for e-commerce applications' 
      }]
    });

    const content = response.choices[0].message.content.toLowerCase();
    
    // Check for relevant keywords
    const relevantKeywords = ['react', 'state', 'redux', 'context', 'e-commerce', 'shopping'];
    const foundKeywords = relevantKeywords.filter(keyword => content.includes(keyword));

    if (foundKeywords.length < relevantKeywords.length * 0.6) {
      throw new Error('Response not sufficiently relevant to query');
    }

    return {
      name: 'Response Relevance',
      status: 'PASSED',
      details: `Found ${foundKeywords.length}/${relevantKeywords.length} relevant keywords`
    };
  }

  async testResponseCompleteness() {
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'Compare Node.js frameworks: Express, Fastify, and Koa' 
      }]
    });

    const content = response.choices[0].message.content.toLowerCase();
    
    // Check that all frameworks are mentioned
    const frameworks = ['express', 'fastify', 'koa'];
    const mentionedFrameworks = frameworks.filter(framework => content.includes(framework));

    if (mentionedFrameworks.length !== frameworks.length) {
      throw new Error('Response does not cover all requested frameworks');
    }

    return {
      name: 'Response Completeness',
      status: 'PASSED',
      details: `Covers all ${frameworks.length} requested frameworks`
    };
  }

  async testResponseClarity() {
    const response = await this.makePerplexityRequest('perplexity_ask', {
      messages: [{ 
        role: 'user', 
        content: 'Explain the difference between REST and GraphQL APIs' 
      }]
    });

    const content = response.choices[0].message.content;
    
    // Check for clarity indicators
    const hasStructure = content.includes('\n') || content.includes('•') || content.includes('-');
    const hasExamples = content.includes('example') || content.includes('for instance');
    const hasComparison = content.includes('vs') || content.includes('whereas') || content.includes('while');

    const clarityScore = [hasStructure, hasExamples, hasComparison].filter(Boolean).length;

    return {
      name: 'Response Clarity',
      status: clarityScore >= 2 ? 'PASSED' : 'WARNING',
      details: `Clarity score: ${clarityScore}/3`
    };
  }

  async testContextIntegration() {
    // Test context-aware responses
    const response = await this.makePerplexityRequest('perplexity_research', {
      messages: [{ 
        role: 'user', 
        content: 'Research authentication solutions for a React TypeScript application with Node.js backend' 
      }]
    });

    const content = response.choices[0].message.content.toLowerCase();
    
    // Check for context integration
    const contextKeywords = ['react', 'typescript', 'node.js', 'javascript'];
    const foundContext = contextKeywords.filter(keyword => content.includes(keyword));

    return {
      name: 'Context Integration',
      status: foundContext.length >= 2 ? 'PASSED' : 'WARNING',
      details: `Context keywords found: ${foundContext.length}/${contextKeywords.length}`
    };
  }

  async validateIntegration() {
    const tests = [
      this.testGitHubCopilotIntegration,
      this.testCursorIntegration,
      this.testClaudeCodeIntegration
    ];

    for (const test of tests) {
      try {
        const result = await test.call(this);
        this.validationResults.integration.push(result);
        console.log(`  ✅ ${result.name}: ${result.status}`);
      } catch (error) {
        const result = { name: test.name, status: 'FAILED', error: error.message };
        this.validationResults.integration.push(result);
        console.log(`  ❌ ${test.name}: FAILED - ${error.message}`);
      }
    }
  }

  async testGitHubCopilotIntegration() {
    // Validate GitHub Copilot integration files
    const files = [
      '.github/chatmodes/enhanced-research-mode.chatmode.md',
      '.github/instructions/mcp-research.instructions.md'
    ];

    for (const file of files) {
      const filePath = path.join(__dirname, '..', '..', file);
      try {
        const content = await fs.readFile(filePath, 'utf8');
        if (!content.includes('perplexity')) {
          throw new Error(`File ${file} missing Perplexity integration`);
        }
      } catch (error) {
        throw new Error(`GitHub Copilot integration file missing: ${file}`);
      }
    }

    return {
      name: 'GitHub Copilot Integration',
      status: 'PASSED',
      details: 'All integration files present and configured'
    };
  }

  async testCursorIntegration() {
    // Validate Cursor integration files
    const files = [
      '.cursor/mcp-integration/perplexity-research.cursorrules',
      '.cursor/modes/task-master-mode.json'
    ];

    for (const file of files) {
      const filePath = path.join(__dirname, '..', '..', file);
      try {
        await fs.access(filePath);
      } catch (error) {
        throw new Error(`Cursor integration file missing: ${file}`);
      }
    }

    return {
      name: 'Cursor Integration',
      status: 'PASSED',
      details: 'All integration files present'
    };
  }

  async testClaudeCodeIntegration() {
    // Validate Claude-Code integration files
    const files = [
      'claude-code/mcp-integration/perplexity-agents.md',
      'claude-code/commands/slash-commands.md'
    ];

    for (const file of files) {
      const filePath = path.join(__dirname, '..', '..', file);
      try {
        const content = await fs.readFile(filePath, 'utf8');
        if (!content.includes('MCP')) {
          throw new Error(`File ${file} missing MCP integration`);
        }
      } catch (error) {
        throw new Error(`Claude-Code integration file missing: ${file}`);
      }
    }

    return {
      name: 'Claude-Code Integration',
      status: 'PASSED',
      details: 'All integration files present and configured'
    };
  }

  async makePerplexityRequest(tool, params) {
    // Simulate Perplexity API request
    // In real implementation, this would make actual HTTP requests
    return {
      choices: [{
        message: {
          content: `Mock response for ${tool} with query: ${params.messages[0].content}`
        }
      }]
    };
  }

  async generateValidationReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateValidationSummary(),
      results: this.validationResults,
      recommendations: this.generateRecommendations()
    };

    const reportPath = path.join(__dirname, 'validation-results.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 Validation Summary:');
    console.log(`  Total Validations: ${report.summary.total}`);
    console.log(`  Passed: ${report.summary.passed}`);
    console.log(`  Failed: ${report.summary.failed}`);
    console.log(`  Warnings: ${report.summary.warnings}`);
    console.log(`  Success Rate: ${report.summary.successRate}%`);
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    if (report.summary.failed > 0) {
      console.log('\n❌ Some validations failed. Check the detailed report for more information.');
      process.exit(1);
    } else {
      console.log('\n✅ All validations passed successfully!');
    }
  }

  generateValidationSummary() {
    const allResults = [
      ...this.validationResults.connectivity,
      ...this.validationResults.accuracy,
      ...this.validationResults.performance,
      ...this.validationResults.quality,
      ...this.validationResults.integration
    ];

    const total = allResults.length;
    const passed = allResults.filter(r => r.status === 'PASSED').length;
    const warnings = allResults.filter(r => r.status === 'WARNING').length;
    const failed = allResults.filter(r => r.status === 'FAILED').length;
    const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;

    return { total, passed, failed, warnings, successRate };
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Analyze results and generate recommendations
    const failedTests = [
      ...this.validationResults.connectivity,
      ...this.validationResults.accuracy,
      ...this.validationResults.performance,
      ...this.validationResults.quality,
      ...this.validationResults.integration
    ].filter(r => r.status === 'FAILED');

    if (failedTests.length > 0) {
      recommendations.push('Address failed validations before production deployment');
    }

    const warningTests = [
      ...this.validationResults.connectivity,
      ...this.validationResults.accuracy,
      ...this.validationResults.performance,
      ...this.validationResults.quality,
      ...this.validationResults.integration
    ].filter(r => r.status === 'WARNING');

    if (warningTests.length > 0) {
      recommendations.push('Review warning validations for potential improvements');
    }

    return recommendations;
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new MCPValidationSuite();
  validator.runValidation().catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

module.exports = MCPValidationSuite;
