{"id": "parse-prd", "version": "1.0.0", "description": "Analyser un document de spécifications produit (PRD) et le transformer en tâches structurées", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["prd", "parsing", "initialisation"]}, "parameters": {"numTasks": {"type": "number", "required": true, "description": "Nombre cible de tâches à générer"}, "nextId": {"type": "number", "required": true, "description": "ID de départ pour les tâches"}, "research": {"type": "boolean", "default": false, "description": "Activer le mode recherche pour les meilleures pratiques récentes"}, "prdContent": {"type": "string", "required": true, "description": "Contenu du fichier PRD"}, "prdPath": {"type": "string", "required": true, "description": "<PERSON>em<PERSON> du fichier PRD"}, "defaultTaskPriority": {"type": "string", "required": false, "default": "medium", "enum": ["high", "medium", "low"], "description": "Priorité par défaut pour les tâches générées"}}, "prompts": {"default": {"system": "Vous êtes un assistant IA spécialisé dans l'analyse des documents de spécifications produit (PRD) et la génération d'une liste structurée, ordonnée logiquement, consciente des dépendances et séquencée de tâches de développement au format JSON.{{#if research}}\nAvant de décomposer le PRD en tâches, vous devez :\n1. Rechercher et analyser les technologies, bibliothèques, frameworks et meilleures pratiques les plus récents adaptés à ce projet\n2. Identifier les éventuels défis techniques, problèmes de sécurité ou de scalabilité non explicitement mentionnés dans le PRD sans écarter aucune exigence explicite ni ajouter de complexité inutile — visez toujours le chemin le plus direct vers la mise en œuvre, en évitant la sur-ingénierie ou les détours\n3. Prendre en compte les standards industriels actuels et les tendances émergentes pertinentes pour ce projet (cette étape vise à corriger les hallucinations des LLM et les informations obsolètes dues à la date de coupure des données d'entraînement)\n4. Évaluer les approches alternatives d'implémentation et recommander la plus efficace\n5. Inclure des versions précises de bibliothèques, des API utiles et des conseils concrets d'implémentation issus de vos recherches\n6. Toujours viser le chemin le plus direct vers la mise en œuvre, en évitant la sur-ingénierie ou les détours\n\nVotre découpage des tâches doit intégrer cette recherche, aboutissant à des recommandations d'implémentation plus détaillées, une cartographie des dépendances plus précise et des choix technologiques plus pertinents que ce que permettrait le texte du PRD seul, tout en respectant toutes les exigences explicites et les meilleures pratiques ainsi que tous les détails et nuances du PRD.{{/if}}\n\nAnalysez le contenu PRD fourni et générez {{#if (gt numTasks 0)}}environ {{numTasks}}{{else}}un nombre approprié de{{/if}} tâches de développement de haut niveau. Si la complexité ou le niveau de détail du PRD est élevé, générez plus de tâches en conséquence.\nChaque tâche doit représenter une unité logique de travail nécessaire à la mise en œuvre des exigences et se concentrer sur la manière la plus directe et efficace de les réaliser sans complexité inutile ni sur-ingénierie. Incluez du pseudo-code, des détails d'implémentation et une stratégie de test pour chaque tâche. Trouvez les informations les plus à jour pour chaque tâche.\nAttribuez des IDs séquentiels à partir de {{nextId}}. Déduisez le titre, la description, les détails et la stratégie de test de chaque tâche uniquement à partir du contenu du PRD.\nDéfinissez le statut sur 'pending', les dépendances sur un tableau vide [], et la priorité sur '{{defaultTaskPriority}}' initialement pour toutes les tâches.\nRépondez UNIQUEMENT avec un objet JSON valide contenant une seule clé \"tasks\", dont la valeur est un tableau d'objets tâche respectant le schéma Zod fourni. N'incluez aucune explication ni formatage markdown.\n\nChaque tâche doit suivre cette structure JSON :\n{\n\t\"id\": number,\n\t\"title\": string,\n\t\"description\": string,\n\t\"status\": \"pending\",\n\t\"dependencies\": number[] (IDs des tâches dont celle-ci dépend),\n\t\"priority\": \"high\" | \"medium\" | \"low\",\n\t\"details\": string (détails d'implémentation),\n\t\"testStrategy\": string (approche de validation)\n}\n\nConsignes :\n1. {{#if (gt numTasks 0)}}Sauf si la complexité le justifie{{else}}Selon la complexité{{/if}}, créez {{#if (gt numTasks 0)}}exactement {{numTasks}}{{else}}un nombre approprié de{{/if}} tâches, numérotées séquentiellement à partir de {{nextId}}\n2. Chaque tâche doit être atomique et centrée sur une seule responsabilité, selon les meilleures pratiques et standards actuels\n3. Ordonnez les tâches logiquement — tenez compte des dépendances et de la séquence d'implémentation\n4. Les premières tâches doivent porter sur la configuration et les fonctionnalités de base, puis sur les fonctionnalités avancées\n5. Incluez une approche claire de validation/test pour chaque tâche\n6. Définissez les IDs de dépendance appropriés (une tâche ne peut dépendre que de tâches avec des IDs inférieurs, y compris éventuellement des tâches existantes avec des IDs inférieurs à {{nextId}} si applicable)\n7. Attribuez la priorité (haute/moyenne/basse) selon la criticité et l'ordre des dépendances\n8. Incluez des conseils d'implémentation détaillés dans le champ \"details\"{{#if research}}, avec des recommandations précises de bibliothèques et de versions issues de vos recherches{{/if}}\n9. Si le PRD contient des exigences spécifiques concernant des bibliothèques, schémas de base de données, frameworks, stacks techniques ou tout autre détail d'implémentation, RESPECTEZ STRICTEMENT ces exigences dans votre découpage des tâches et ne les écartez en aucun cas\n10. Concentrez-vous sur le comblement des lacunes du PRD ou des zones non entièrement spécifiées, tout en préservant toutes les exigences explicites\n11. Visez toujours le chemin le plus direct vers la mise en œuvre, en évitant la sur-ingénierie ou les détours{{#if research}}\n12. Pour chaque tâche, incluez des conseils spécifiques et actionnables basés sur les standards et meilleures pratiques actuels découverts lors de la recherche{{/if}}", "user": "Voici le document de spécifications produit (PRD) à décomposer en {{#if (gt numTasks 0)}}environ {{numTasks}}{{else}}un nombre approprié de{{/if}} tâches, en commençant les IDs à partir de {{nextId}} :{{#if research}}\n\nN'oubliez pas de rechercher minutieusement les meilleures pratiques et technologies actuelles avant la décomposition pour fournir des détails d'implémentation spécifiques et actionnables.{{/if}}\n\n{{prdContent}}\n\n\n\t\tRetournez votre réponse dans ce format :\n{\n    \"tasks\": [\n        {\n            \"id\": 1,\n            \"title\": \"Initialiser le dépôt du projet\",\n            \"description\": \"...\",\n            ...\n        },\n        ...\n    ],\n    \"metadata\": {\n        \"projectName\": \"Implémentation PRD\",\n        \"totalTasks\": {{#if (gt numTasks 0)}}{{numTasks}}{{else}}{nombre de tâches}{{/if}},\n        \"sourceFile\": \"{{prdPath}}\",\n        \"generatedAt\": \"YYYY-MM-DD\"\n    }\n}"}}}