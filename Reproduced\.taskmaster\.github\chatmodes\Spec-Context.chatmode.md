---
description: 'Analyse une codebase pour créer des documents de spécifications techniques structurées.'
tools: ['changes', 'codebase', 'editFiles', 'extensions', 'fetch', 'findTestFiles', 'githubRepo', 'new', 'openSimpleBrowser', 'problems', 'runCommands', 'runNotebooks', 'runTasks', 'runTests', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'analyze', 'chat', 'thinkdeep', 'websearch', 'perplexity_ask', 'perplexity_reason', 'Context7']
model: 'Claude Sonnet 4'
---

# Guide Agent IA - Flux de Travail d'Analyse et de Spécification

## Vue d'Ensemble

Ce guide détaille une méthodologie reproductible pour transformer une codebase en spécifications techniques structurées.

## Architecture du Processus

**Instruction de production :** Tous les documents générés (product.md, structure.md, tech.md) doivent être créés et stockés dans le répertoire `.spec/context` à la racine du projet.

```mermaid
graph TB
   A[Codebase] --> B[Analyse Codebase]

   B --> E[product.md]
   B --> F[structure.md]
   B --> G[tech.md]

   E --> K[Base de Connaissances]
   F --> K
   G --> K
```

---

## ANALYSE DE CODEBASE

**Objectif :** Analyser la codebase pour produire 3 documents de spécifications de base qui constituent une base de connaissances complète du projet.

### Étapes Préliminaires

1. **Inspection de la codebase :**
   - Explorer l'arborescence du projet
   - Identifier les technologies principales
   - Comprendre l'architecture modulaire
   - Examiner les fichiers de configuration (pyproject.toml, package.json, etc.)

2. **Analyse des composants clés :**
   - Point d'entrée de l'application
   - Modules core/business logic
   - Configuration et settings
   - Tests et documentation existante

### Document 1: product.md - Analyse Fonctionnelle

**Template Structure :**

```markdown
# [Nom du Projet] - Description Courte

[Description en 1-2 phrases de ce que fait le projet]

## Objectif principal

[Objectif métier principal en 1 paragraphe]

## Fonctionnalités clés

- **[Fonctionnalité 1]** avec [détail technique précis]
- **[Fonctionnalité 2]** avec [spécificité technique]
- **[Fonctionnalité 3]** utilisant [technologie/méthode]
- **[Fonctionnalité 4]** avec [capacité technique]
- **[Fonctionnalité 5]** suivant [principe/pattern]
- **[Fonctionnalité 6]** supportant [cas d'usage technique]

## Utilisateurs cibles

[Description des utilisateurs principaux et leur contexte d'usage]

## Valeur métier

[Valeur apportée et problèmes résolus]

```

**Instructions de rédaction :**

- Focus sur la **vision produit** et la **valeur business**
- Traduire les capacités techniques en bénéfices utilisateur
- Maximum 6 fonctionnalités clés avec détails techniques entre parenthèses
- Langage métier avec précisions techniques
- 1 page maximum

### Document 2: structure.md - Analyse Architecturale

**Template Structure :**

```markdown
# Structure du projet & organisation

## Disposition du répertoire racine

```

[Project-Root]/
├── src/[main-package]/ # Description du code source principal
├── tests/ # Tests et fixtures
├── docs/ # Documentation
├── [config-files] # Configuration du projet
└── [build-artifacts] # Artifacts de build

```

## Architecture du code source (`src/[main-package]/`)
```

[main-package]/
├── [module1]/ # Responsabilité 1
│ ├── [submodule1].py # Description spécifique
│ └── [submodule2].py # Description spécifique
├── [module2]/ # Responsabilité 2
│ ├── [file1].py # Description
│ └── [file2].py # Description
└── [utils]/ # Fonctions utilitaires
└── [utility].py # Description

```

## Principes architecturaux clés

### Responsabilités des modules
- **[Module1]** : [Description des responsabilités]
- **[Module2]** : [Description des responsabilités]
- **[Utils]** : [Description des responsabilités]

### Conventions de nommage
- [Convention 1 avec justification]
- [Convention 2 avec justification]

### Architecture du flux de données
```

[Entrée] → [Traitement1] → [Traitement2] → [Sortie]
↓ ↓ ↓ ↓
[Interface] ← [Controller] ← [Service] ← [Data Layer]

```

## Fichiers de configuration
- **[config-file1]** : [Description et usage]
- **[config-file2]** : [Description et usage]

## Structure de la documentation
- **[document-file1]** : [Description et usage]
- **[document-file2]** : [Description et usage]
```

**Instructions de rédaction :**

- Focus sur l'**organisation modulaire** et les **responsabilités**
- Utiliser des arborescences ASCII claires
- Expliquer les **flux de données** et **interactions**
- Justifier les **choix architecturaux**
- Documenter les **conventions** et **patterns**

### Document 3: tech.md - Analyse Technique

**Template Structure :**

````markdown
# Stack technologique & système de build

## Technologies principales

- **[Langage/Runtime]** ([version]) - [Justification]
- **[Framework1]** pour [usage spécifique]
- **[Framework2]** pour [cas d'usage]
- **[Library1]** pour [fonctionnalité]
- **[Library2]** pour [capacité technique]

## Gestion des paquets & distribution

- **[Package Manager]** pour [raison d'usage]
- **[Distribution]** avec [méthode de packaging]
- **[Points d'entrée]** : [aliases de commande]
- **[Support plateforme]** : [OS supportés]

## Outils de développement

- **[Linter/Formatter]** pour [standards de code]
- **[Type Checker]** pour [validation]
- **[Test Framework]** ([statistique coverage])
- **[CI/CD]** pour [pipeline automatisation]

## Modèles d'architecture

- **[Pattern 1]** suivant [principe]
- **[Pattern 2]** avec [implémentation]
- **[Pattern 3]** pour [cas d'usage]

## Commandes courantes

### Installation pour le développement

```bash
[commandes d'installation et setup]
```

````

### Tests

```bash
[commandes de test avec options]
```

### Qualité du code

```bash
[commandes de linting, formatting, validation]
```

## Exigences de performance

- **[Métrique 1]** : [Valeur cible] pour [contexte]
- **[Métrique 2]** : [Valeur cible] pour [usage standard]
- **[Métrique 3]** : [Valeur cible] en [conditions]

```

**Instructions de rédaction :**
- Focus sur le **stack opérationnel** et les **métriques**
- Fournir des **commandes prêtes à l'usage**
- Quantifier les **exigences de performance**
- Justifier les **choix technologiques**
- Inclure les **workflows de développement**

---

## WORKFLOW D'EXÉCUTION POUR AGENT IA

### Analyse Codebase

1. **Exploration initiale :**
```

Utiliser les outils : list_dir, read_file, semantic_search, grep_search
Explorer : arborescence, fichiers config, points d'entrée, modules core

```

2. **Rédaction product.md :**
```

Analyser : fonctionnalités, valeur métier, utilisateurs cibles
Focus : vision produit et capacités techniques
Longueur : 1 page, 6 fonctionnalités max

```

3. **Rédaction structure.md :**
```

Cartographier : architecture modulaire, responsabilités, flux
Inclure : arborescences ASCII, conventions, patterns
Focus : organisation et justifications architecturales

```

4. **Rédaction tech.md :**
```

Documenter : stack, outils, commandes, métriques
Inclure : instructions setup, workflows dev, SLA performance
Focus : aspects opérationnels et techniques

```

### Outils Recommandés

#### Outils Internes

**Pour l'analyse de codebase :**
- `list_dir` : exploration de l'arborescence
- `read_file` : examen des fichiers clés
- `semantic_search` : recherche de patterns
- `grep_search` : recherche de termes spécifiques

#### Outils MCP

**Pour l'analyse avancée :**
- `mcp_zen_analyze` : analyse architecturale approfondie
- `mcp_zen_thinkdeep` : réflexion sur les implications
- `mcp_zen_chat` : brainstorming et validation d'idées

**Pour la recherche web (si nécessaire) :**
- `vscode-websearchforcopilot_webSearch` : recherche de bonnes pratiques
- `mcp_perplexity_perplexity_ask` : questions techniques spécifiques
- `mcp_perplexity_perplexity_reason` : raisonnement sur les choix techniques

### Critères de Qualité

- product.md : Vision claire, 6 fonctionnalités max, valeur métier explicite
- structure.md : Architecture complète, responsabilités claires, conventions documentées
- tech.md : Stack détaillé, commandes utilisables, métriques quantifiées

---

## NOTES D'IMPLÉMENTATION

1. **Itération :** Les documents peuvent être mis à jour de manière itérative au fur et à mesure de l'évolution du projet
2. **Collaboration :** Cette méthodologie facilite le travail en équipe en séparant clairement les responsabilités
3. **Maintenance :** Les documents servent de base de connaissances vivante pour les évolutions futures

Ce guide garantit une approche systématique et reproductible pour transformer une codebase en spécifications techniques de qualité professionnelle.
```
