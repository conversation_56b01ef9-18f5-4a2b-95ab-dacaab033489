{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-preview-05-06", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview-04-17", "maxTokens": 1048000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 20, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "French", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}