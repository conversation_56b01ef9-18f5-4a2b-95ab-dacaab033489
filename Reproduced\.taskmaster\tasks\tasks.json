{"master": {"tasks": [{"id": 1, "title": "Définir l'interface abstraite `ConversationStorage`", "description": "Définir une interface Python abstraite `ConversationStorage` qui formalise les méthodes requises (`get`, `setex`, `delete`, etc.) pour tous les backends de stockage. Cela garantira la compatibilité et l'interchangeabilité.", "details": "C<PERSON>er un nouveau fichier, par exemple `utils/storage_interface.py`. Utiliser le module `abc` de Python pour définir `ConversationStorage` comme une classe de base abstraite (ABC). Déclarer les méthodes `@abstractmethod` pour `get(key)`, `setex(key, ttl, value)` et `delete(key)`. Cette interface servira de contrat pour `SQLiteStorage` et `InMemoryStorage`.", "testStrategy": "Vérifier qu'instancier directement `ConversationStorage` lève une `TypeError`. Créer une classe de test qui hérite de l'interface mais n'implémente pas toutes les méthodes pour s'assurer que cela échoue également.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Implémenter la fabrique de backend de stockage", "description": "C<PERSON>er une fonction fabrique `get_storage_backend` qui sélectionne et instancie dynamiquement le backend de stockage approprié (SQLite ou en mémoire) en fonction des variables d'environnement.", "details": "Dans `utils/storage_backend.py`, créer une fonction `get_storage_backend()`. Elle lira la variable d'environnement `MCP_STORAGE_BACKEND`. Si la valeur est 'sqlite', elle importera et instanciera `SQLiteStorage`. Sinon, elle instanciera `InMemoryStorage` par défaut. La fonction retournera une instance conforme à l'interface `ConversationStorage`.", "testStrategy": "Tests unitaires pour la fabrique : mocker les variables d'environnement pour vérifier que `get_storage_backend()` retourne une instance de `SQLiteStorage` lorsque `MCP_STORAGE_BACKEND='sqlite'` et une instance de `InMemoryStorage` dans les autres cas.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "<PERSON><PERSON>er la classe `SQLiteStorage` et initialiser le schéma", "description": "<PERSON><PERSON><PERSON> la classe `SQLiteStorage` qui se connecte à une base de données SQLite et initialise le schéma de la table `conversations` si elle n'existe pas.", "details": "Créer la classe `SQLiteStorage` dans `utils/storage_backend.py`. Le constructeur (`__init__`) prendra le chemin du fichier de la base de données (depuis `MCP_SQLITE_PATH`, avec un défaut comme `./data/zen_mcp.db`). Il utilisera le module `sqlite3` pour se connecter et exécutera la commande `CREATE TABLE IF NOT EXISTS` avec le schéma spécifié dans le PRD. Implémenter la gestion des locks (`threading.Lock`) pour garantir la sécurité des threads.", "testStrategy": "Tests unitaires pour vérifier la création du fichier de base de données et de la table `conversations` avec les bonnes colonnes et contraintes lors de la première instanciation. Utiliser une base de données en mémoire (`:memory:`) pour les tests.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implémenter la méthode `setex` dans `SQLiteStorage`", "description": "Implémenter la méthode `setex` dans `SQLiteStorage` pour insérer ou mettre à jour des enregistrements de conversation, en gérant la sérialisation du contexte et le TTL.", "details": "La méthode `setex(key, ttl, value)` utilisera une requête `INSERT OR REPLACE INTO conversations (...) VALUES (...)`. Le `key` sera le `thread_id`. Le `value` (contexte de la conversation) sera sérialisé en JSON et stocké dans `context_json`. Le `ttl` sera utilisé pour calculer une date d'expiration qui sera stockée dans une colonne `expires_at` (à ajouter au schéma initial pour simplifier la gestion TTL).", "testStrategy": "Tester l'insertion d'une nouvelle conversation. Vérifier que les données sont correctement stockées, y compris le JSON. Tester la mise à jour d'une conversation existante. Valider que le `last_updated_at` est bien mis à jour.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implémenter la méthode `get` dans `SQLiteStorage`", "description": "Implémenter la méthode `get` dans `SQLiteStorage` pour récupérer une conversation par son `thread_id` et la désérialiser.", "details": "La méthode `get(key)` exécutera une requête `SELECT context_json FROM conversations WHERE thread_id = ?`. Si un enregistrement est trouvé, elle désérialisera la chaîne JSON de la colonne `context_json` en un objet Python avant de le retourner. Si aucun enregistrement n'est trouvé ou si l'enregistrement est expiré (basé sur `expires_at`), elle retournera `None`.", "testStrategy": "Tester la récupération d'une conversation existante et vérifier que l'objet retourné correspond à l'original. Tester la récupération d'un ID inexistant (doit retourner `None`). Tester la récupération d'une conversation expirée (doit retourner `None`).", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 6, "title": "Adapter `InMemoryStorage` à l'interface `ConversationStorage`", "description": "Adapter la classe `InMemoryStorage` existante pour qu'elle se conforme à la nouvelle interface `ConversationStorage`.", "details": "Modifier `utils/storage_backend.py` ou le fichier où se trouve `InMemoryStorage`. S'assurer que la classe hérite de `ConversationStorage` et que ses méthodes (`get`, `setex`, etc.) correspondent exactement à la signature définie dans l'interface abstraite. La logique interne reste basée sur un dictionnaire en mémoire.", "testStrategy": "Exécuter les tests unitaires existants pour `InMemoryStorage` pour s'assurer qu'aucune régression n'a été introduite. Ajouter un test pour vérifier que `isinstance(InMemoryStorage(), ConversationStorage)` est `True`.", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 7, "title": "Intégrer la fabrique de backend dans `conversation_memory.py`", "description": "Remplacer l'instanciation directe de `InMemoryStorage` dans le code de l'application par un appel à la nouvelle fabrique `get_storage_backend()`.", "details": "Localiser l'endroit où le backend de stockage est initialisé dans `utils/conversation_memory.py`. Remplacer `storage = InMemoryStorage()` par `storage = get_storage_backend()`. <PERSON><PERSON> rendra le choix du backend transparent pour le reste de l'application.", "testStrategy": "Tests d'intégration pour s'assurer que le serveur démarre et fonctionne correctement avec la configuration par défaut (en mémoire) et avec la configuration SQLite. Vérifier que les conversations sont bien gérées dans les deux modes.", "priority": "high", "dependencies": [2, 6], "status": "pending", "subtasks": []}, {"id": 8, "title": "<PERSON><PERSON><PERSON> la configuration via les variables d'environnement", "description": "Mettre en œuvre la lecture des variables d'environnement `MCP_STORAGE_BACKEND` et `MCP_SQLITE_PATH` pour configurer le backend de stockage.", "details": "Utiliser `os.getenv()` dans la fonction `get_storage_backend()` pour lire les variables. `MCP_STORAGE_BACKEND` déterminera le type de backend ('sqlite' ou 'memory'). `MCP_SQLITE_PATH` fournira le chemin du fichier de base de données à `SQLiteStorage`, avec une valeur par défaut comme `./data/zen_mcp.db`.", "testStrategy": "Lancer l'application avec différentes variables d'environnement et vérifier via les logs ou le comportement que le bon backend est utilisé. Par exemple, vérifier la création du fichier `.db` lorsque `MCP_STORAGE_BACKEND=sqlite` est défini.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 9, "title": "Développer le module d'extraction de métadonnées", "description": "Développer un module utilitaire pour extraire des métadonnées structurées (chemin du projet, prompt initial, résumé) à partir de l'objet de contexte de conversation non structuré.", "details": "<PERSON><PERSON><PERSON> `utils/metadata_extractor.py`. Implémenter des fonctions utilisant des heuristiques et des expressions régulières pour trouver des informations clés dans le contexte JSON. Par exemple, `extract_project_path` pourrait chercher des clés comme `cwd` ou `project_root`. `extract_initial_prompt` prendrait le premier message de l'utilisateur.", "testStrategy": "Tests unitaires avec divers exemples de contextes de conversation (complets, incomplets, avec différents formats) pour valider la robustesse de l'extraction de chaque métadonnée.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 10, "title": "Intégrer l'extraction de métadonnées dans `SQLiteStorage.setex`", "description": "Modifier la méthode `SQLiteStorage.setex` pour appeler l'extracteur de métadonnées avant l'insertion et stocker les résultats dans les colonnes dédiées.", "details": "Dans `SQLiteStorage.setex`, avant d'exécuter la requête `INSERT OR REPLACE`, appeler les fonctions de `metadata_extractor` en passant le contexte de la conversation. Utiliser les valeurs retournées pour peupler les colonnes `project_path`, `initial_prompt`, `summary`, et `tool_name` dans la requête SQL.", "testStrategy": "Après avoir appelé `setex`, interroger directement la base de données pour vérifier que les colonnes de métadonnées (`project_path`, `initial_prompt`, etc.) ont été correctement remplies en plus de `context_json`.", "priority": "high", "dependencies": [4, 9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Créer la structure de base de l'outil MCP `@history`", "description": "<PERSON><PERSON><PERSON> le fichier et la classe de base pour le nouvel outil MCP `@history`, en le déclarant dans le système pour qu'il soit reconnaissable par le serveur.", "details": "C<PERSON>er un nouveau fichier `tools/history.py`. Définir une classe `HistoryTool` qui hérite de la classe de base des outils MCP. Implémenter les propriétés de base comme `name = '@history'` et une méthode `run()` vide pour le moment. Utiliser `argparse` pour définir les futurs arguments de ligne de commande (`--tool`, `--project`, `--limit`).", "testStrategy": "D<PERSON><PERSON><PERSON> le serveur et vérifier que l'outil `@history` est listé dans les outils disponibles. Exécuter `@history --help` pour s'assurer que les arguments sont correctement définis.", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 12, "title": "Implémenter la logique de recherche filtrée dans `SQLiteStorage`", "description": "Ajouter une nouvelle méthode à `SQLiteStorage` pour rechercher des conversations en fonction de plusieurs critères de filtrage (outil, projet, date, contenu).", "details": "Créer une méthode `search_conversations(filters)` dans `SQLiteStorage`. La méthode construira dynamiquement une requête `SELECT` avec une clause `WHERE` basée sur les filtres fournis (ex: `tool_name = ?`, `project_path LIKE ?`). La recherche par contenu se fera avec `context_json LIKE ?` ou `initial_prompt LIKE ?`. La méthode doit utiliser des requêtes paramétrées pour éviter les injections SQL.", "testStrategy": "Tests unitaires pour la méthode `search_conversations`. Insérer un jeu de données de test varié et appeler la méthode avec différents filtres (un seul filtre, plusieurs filtres, filtres vides) pour valider que les résultats sont corrects et complets.", "priority": "high", "dependencies": [10], "status": "pending", "subtasks": []}, {"id": 13, "title": "Implémenter les filtres et l'affichage pour l'outil `@history`", "description": "Connecter les arguments de l'outil `@history` à la méthode de recherche du backend et formater les résultats dans un tableau lisible pour l'utilisateur.", "details": "Dans la méthode `run()` de `HistoryTool`, ré<PERSON><PERSON>rer les arguments de la ligne de commande. Appeler la méthode `storage.search_conversations()` avec ces filtres. Itérer sur les résultats et utiliser une bibliothèque comme `rich.table` ou un formatage de chaîne manuel pour afficher les conversations dans un tableau structuré avec les colonnes : `continuation_id` (thread_id), `created_at`, `tool_name`, `initial_prompt`.", "testStrategy": "Test d'intégration de bout en bout : peupler la base de données avec des conversations de test, exécuter des commandes `@history` avec différents filtres (`@history --tool analyze --limit 5`) et vérifier que la sortie console est correctement formatée et contient les bonnes données.", "priority": "high", "dependencies": [11, 12], "status": "pending", "subtasks": []}, {"id": 14, "title": "Implémenter la reprise de conversation via `continuation_id`", "description": "Permettre aux utilisateurs de reprendre une conversation passée en utilisant un `continuation_id` fourni par l'outil `@history`.", "details": "L'outil `@history` affichera le `thread_id` comme `continuation_id`. Modifier les outils principaux (comme `@analyze`) pour qu'ils acceptent un argument `--continuation_id`. Si cet argument est fourni, l'outil utilisera `storage.get(continuation_id)` pour charger le contexte de la conversation précédente avant de continuer.", "testStrategy": "1. Utiliser `@history` pour trouver un `continuation_id`. 2. Exécuter un autre outil avec cet ID (ex: `@analyze --continuation_id <uuid> 'continue from here'`). 3. Vérifier que le contexte de la conversation chargée est bien celui de la conversation d'origine.", "priority": "medium", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Configurer les optimisations de performance SQLite (WAL, PRAGMA)", "description": "Configurer la connexion SQLite pour utiliser le mode WAL (Write-Ahead Logging) et d'autres PRAGMA pour améliorer les performances et la concurrence.", "details": "Dans le constructeur de `SQLiteStorage`, juste après la connexion, exécuter les commandes PRAGMA suivantes : `PRAGMA journal_mode=WAL;`, `PRAGMA synchronous=NORMAL;`, `PRAGMA cache_size=-2000;` (2MB). Cela améliorera significativement les performances en écriture et la lecture concurrente.", "testStrategy": "Vérifier que le mode WAL est actif en inspectant les fichiers de la base de données (présence d'un fichier `-wal`). Mener des tests de charge simples (écritures et lectures concurrentes) pour mesurer l'amélioration des performances par rapport à la configuration par défaut.", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 16, "title": "Développer le worker de maintenance pour le nettoyage TTL", "description": "C<PERSON>er un thread en arrière-plan qui s'exécute périodiquement pour supprimer les conversations expirées de la base de données.", "details": "Dans `SQLiteStorage`, d<PERSON><PERSON>rer un `threading.Thread` en mode daemon. Ce thread se réveillera toutes les heures (configurable), et exécutera une requête `DELETE FROM conversations WHERE expires_at <= ?` en utilisant la date et l'heure actuelles. Ajouter une colonne `expires_at DATETIME` au schéma de la table.", "testStrategy": "Insérer des conversations avec des TTL courts (quelques secondes). Attendre que le TTL passe et que le worker s'exécute, puis vérifier que les enregistrements ont bien été supprimés de la base de données.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 17, "title": "Implémenter la commande `VACUUM` dans le worker de maintenance", "description": "Ajouter une tâche de `VACUUM` périodique au worker de maintenance pour défragmenter la base de données et récupérer l'espace disque.", "details": "Étendre le worker de maintenance. Après le nettoyage des TTL, vérifier si la dernière opération de `VACUUM` est suffisamment ancienne (ex: une fois par jour). Si c'est le cas, exécuter la commande `VACUUM;`. Cette opération doit être effectuée pendant les heures creuses car elle peut bloquer la base de données.", "testStrategy": "Simuler une grande quantité d'insertions et de suppressions pour fragmenter la base de données. Déclencher manuellement le worker et vérifier que la taille du fichier de la base de données sur le disque diminue après l'exécution de `VACUUM`.", "priority": "low", "dependencies": [16], "status": "pending", "subtasks": []}, {"id": 18, "title": "Mettre en place la gestion des erreurs et le logging", "description": "Intégrer une gestion d'erreurs robuste et un logging détaillé dans toutes les opérations de base de données pour faciliter le débogage et la maintenance.", "details": "Utiliser des blocs `try...except sqlite3.Error` autour de toutes les exécutions de requêtes dans `SQLiteStorage`. Logger les erreurs avec le module `logging` de Python, en incluant des détails contextuels comme la requête échouée (sans les données sensibles). Ajouter des logs d'information pour les opérations réussies.", "testStrategy": "Tests unitaires pour simuler des erreurs de base de données (ex: syntaxe SQL incorrecte, contraintes violées). Vérifier que les exceptions sont correctement capturées et que des messages de log pertinents sont émis.", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 19, "title": "Configurer le montage de volume Docker pour la persistance", "description": "Mettre à jour la configuration Docker (ex: `docker-compose.yml`) pour monter un volume persistant pour le répertoire `./data`.", "details": "Modifier le fichier `docker-compose.yml` ou le `Dockerfile`. Ajouter une section `volumes` pour le service `zen-mcp-server`. Mapper un volume local ou nommé au chemin du conteneur où la base de données SQLite est stockée (ex: `./data:/app/data`). Cela garantira que le fichier `.db` persiste entre les redémarrages du conteneur.", "testStrategy": "1. <PERSON><PERSON> le conteneur avec le volume monté. 2. <PERSON><PERSON>er des conversations. 3. <PERSON><PERSON><PERSON><PERSON> et redémarrer le conteneur (`docker-compose down && docker-compose up`). 4. Utiliser `@history` pour vérifier que les conversations précédentes sont toujours présentes.", "priority": "high", "dependencies": [8], "status": "pending", "subtasks": []}, {"id": 20, "title": "Rédiger la documentation utilisateur et le guide de migration", "description": "Rédiger une documentation complète pour les utilisateurs finaux, expliquant comment activer et configurer la persistance SQLite et comment utiliser le nouvel outil `@history`.", "details": "<PERSON><PERSON><PERSON> ou mettre à jour un fichier `README.md` ou une page de documentation. Décrire la nouvelle fonctionnalité, les variables d'environnement (`MCP_STORAGE_BACKEND`, `MCP_SQLITE_PATH`), et fournir des exemples clairs d'utilisation de l'outil `@history` avec ses différents filtres. Inclure un guide de migration pour les utilisateurs existants.", "testStrategy": "Faire relire la documentation par un pair ou un utilisateur non impliqué dans le développement pour s'assurer de sa clarté et de son exactitude. Vérifier que tous les exemples fournis fonctionnent comme décrit.", "priority": "medium", "dependencies": [8, 13], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-28T12:33:18.595Z", "updated": "2025-07-28T12:33:18.596Z", "description": "Tasks for master context"}}}