# Rapport d'Adaptation Task-Master : Analyse Zen

## Résumé Exécutif

L'analyse approfondie du système de prompts Task-Master révèle une architecture exceptionnellement robuste et bien conçue, parfaitement adaptée à l'intégration multi-outils. Le système présente une séparation claire entre la **définition** des prompts (fichiers JSON) et leur **exécution** (PromptManager), créant un fondement solide pour l'adaptation à différents outils IA.

**Recommandation stratégique principale :** Adopter un modèle **Cœur + Adaptateurs** préservant intégralement l'architecture Task-Master tout en créant des couches d'adaptation spécifiques pour chaque outil cible.

---

## Architecture Task-Master : Forces Identifiées

### 🏗️ Séparation des Préoccupations (CRITIQUE)
**Constat :** Découplage exemplaire entre définition (JSON) et exécution (PromptManager)
- **Structure standardisée :** Tous les prompts suivent le schéma `id/version/description/metadata/parameters/prompts`
- **Logique centralisée :** Le PromptManager orchestre la validation, la résolution de templates et la sélection de variantes
- **Impact :** Permet l'évolution indépendante du cœur métier et des adaptateurs

### 🔧 Système de Templating Sophistiqué
**Constat :** Templates Handlebars avec logique conditionnelle avancée
```handlebars
{{#if useResearch}}
Recherchez les meilleures pratiques...
{{/if}}
{{#if (gt subtaskCount 0)}}exactement {{subtaskCount}}{{else}}un nombre approprié de{{/if}}
```
- **Variables dynamiques :** `{{variable}}`, `{{gatheredContext}}`
- **Conditions complexes :** `{{#if}}`, `{{#gt}}`, helpers numériques
- **Variantes contextuelles :** `default`, `research`, `complexity-report`

### 🛡️ Validation et Robustesse
**Constat :** Validation stricte par schémas JSON (AJV)
- **Paramètres typés :** `string`, `number`, `array`, `object`, `boolean`
- **Contraintes avancées :** énums, patterns regex, min/max
- **Versioning sémantique :** Suivi des modifications
- **Contrats stricts :** Formats de sortie JSON rigoureusement définis

---

## Analyse par Catégorie d'Outils

### 🖥️ IDE IA (GitHub Copilot, Cursor, Trae)

#### **Défis Identifiés**
- **Séparation system/user :** Pas de support natif → concaténation nécessaire
- **Sortie JSON enrobée :** Post-traitement obligatoire pour extraire le JSON des réponses conversationnelles
- **Contexte limité :** Dépendance aux capacités d'injection de contexte de chaque IDE

#### **Stratégie d'Adaptation**
```javascript
// Exemple d'adaptateur IDE IA
class IDEAdapter {
  async executePrompt(promptId, parameters) {
    // 1. Résolution via PromptManager
    const resolved = await this.promptManager.resolve(promptId, parameters);
    
    // 2. Concaténation system + user
    const finalPrompt = `${resolved.system}\n\n---\n\n${resolved.user}`;
    
    // 3. Appel IDE + post-traitement
    const response = await this.callIDE(finalPrompt);
    return this.extractJSON(response);
  }
  
  extractJSON(response) {
    // Extraction robuste du JSON depuis réponse conversationnelle
    const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/);
    return jsonMatch ? JSON.parse(jsonMatch[1]) : null;
  }
}
```

#### **Recommandations d'Implémentation**
- **Option Simple :** Scripts générateurs de prompts pour le presse-papiers
- **Option Avancée :** Commandes custom/recipes exécutant le PromptManager en arrière-plan

---

### 🔌 Extensions VS Code (RooCode/Kilocode, Cline)

#### **Défi Critique**
**APIs programmatiques inconnues** - Nécessite une recherche approfondie des capacités d'intégration de chaque extension.

#### **Stratégie Recommandée**
1. **Extension VS Code dédiée "Task-Master"**
2. **Commandes centralisées :** `taskmaster.addTask`, `taskmaster.expandTask`
3. **Intégration conditionnelle :** API si disponible, sinon presse-papiers

```typescript
// Extension VS Code Task-Master
export function activate(context: vscode.ExtensionContext) {
    const addTaskCommand = vscode.commands.registerCommand('taskmaster.addTask', async () => {
        // 1. Collecter le contexte VS Code
        const context = await this.gatherVSCodeContext();
        
        // 2. Exécuter PromptManager
        const prompt = await this.promptManager.resolve('add-task', context);
        
        // 3. Déléguer à l'extension cible
        await this.delegateToExtension('roocode', prompt);
    });
}
```

#### **Recherche Nécessaire**
- **RooCode/Kilocode :** APIs programmatiques disponibles ?
- **Cline :** Capacités d'automatisation ?
- **Fallback :** Génération pour presse-papiers si pas d'API

---

### 💻 CLI (Claude-Code, Gemini-CLI)

#### **Avantages (Cible Idéale)**
- **Support system/user natif :** Flags `--system-instruction` couramment disponibles
- **Mode JSON fiable :** Flags `--json` pour sortie stricte
- **Contrôle total :** Gestion complète du pipeline d'exécution

#### **Implémentation Recommandée**
```bash
#!/bin/bash
# tm-add.sh - Script wrapper Task-Master
PROMPT_DATA=$(node -e "
  const pm = require('./taskmaster/prompt-manager');
  const params = { prompt: '$1', newTaskId: $2 };
  pm.resolve('add-task', params).then(p => 
    console.log(JSON.stringify(p))
  );
")

SYSTEM=$(echo $PROMPT_DATA | jq -r '.system')
USER=$(echo $PROMPT_DATA | jq -r '.user')

claude-code --system-instruction "$SYSTEM" --json "$USER"
```

#### **Scripts Recommandés**
- `tm-add.js` : Création de tâches
- `tm-expand.js` : Décomposition en sous-tâches  
- `tm-analyze.js` : Analyse de complexité
- `tm-research.js` : Recherche contextuelle

---

### 🚀 Outils Spécialisés (Traycer, Perplexity MCP, Zen-MCP-Server)

#### **Zen-MCP-Server**
**Avantage :** API structurée permettant intégration directe
```javascript
// Client Zen-MCP
class ZenMCPAdapter {
  async executePrompt(promptId, parameters) {
    const resolved = await this.promptManager.resolve(promptId, parameters);
    
    return await fetch('http://zen-mcp-server/api/execute', {
      method: 'POST',
      body: JSON.stringify({
        system: resolved.system,
        user: resolved.user,
        model: parameters.model || 'gemini-2.5-pro-preview-05-06'
      })
    });
  }
}
```

#### **Perplexity MCP**
**Spécialisation :** Recherche web intégrée
- **Optimisation :** Variantes `useResearch: true` parfaitement adaptées
- **Adaptation :** Simplification des prompts research.json pour tirer parti des capacités natives

#### **Traycer**
**Particularité :** Domaine spécialisé (debug/tracing)
- **Nouveaux templates :** `analyze-trace.json`, `suggest-fix.json`
- **Conservation :** Structure Task-Master, contenu adapté au vocabulaire Traycer

---

## Architecture d'Adaptation Recommandée

### 🏛️ Modèle Cœur + Adaptateurs

```
┌─────────────────────────────────────┐
│           CŒUR TASK-MASTER          │
│  ┌─────────────┐ ┌───────────────┐  │
│  │   Prompts   │ │ PromptManager │  │
│  │  (JSON)     │ │  (Résolution) │  │
│  └─────────────┘ └───────────────┘  │
└─────────────────┬───────────────────┘
                  │
    ┌─────────────┼─────────────┐
    │             │             │
┌───▼──┐      ┌───▼──┐      ┌───▼──┐
│ CLI  │      │ IDE  │      │ MCP  │
│Adapt.│      │Adapt.│      │Adapt.│
└──────┘      └──────┘      └──────┘
```

### 📋 Responsabilités des Adaptateurs

1. **Collecte de contexte :** Variables d'environnement spécifiques à l'outil
2. **Invocation du cœur :** Appel au PromptManager avec les paramètres
3. **Formatage spécifique :** Adaptation aux exigences de l'outil cible
4. **Post-traitement :** Validation et extraction des réponses

---

## Défis Techniques Cruciaux

### 🔧 Résolution de Templates
**Impératif :** Templates Handlebars DOIVENT être résolus avant soumission
```javascript
// ❌ INCORRECT : Envoyer le template brut
const prompt = "{{#if useResearch}}Recherchez...{{/if}}";

// ✅ CORRECT : Résolu via PromptManager
const resolved = await promptManager.resolve(promptId, parameters);
```

### 📤 Extraction de JSON
**Défi :** Modèles conversationnels enrobent souvent le JSON
```javascript
// Pattern d'extraction robuste
function extractJSON(response) {
  // Tentative 1: JSON pur
  try { return JSON.parse(response.trim()); } catch {}
  
  // Tentative 2: Bloc markdown
  const mdMatch = response.match(/```json\n([\s\S]*?)\n```/);
  if (mdMatch) return JSON.parse(mdMatch[1]);
  
  // Tentative 3: Recherche par patterns
  const jsonMatch = response.match(/\{[\s\S]*\}/);
  if (jsonMatch) return JSON.parse(jsonMatch[0]);
  
  throw new Error('JSON non extractible');
}
```

### 🌐 Gestion du Contexte
**Variabilité :** Collecte différente selon l'environnement
- **VS Code :** `workspace`, `activeTextEditor`, sélections
- **CLI :** Arguments, fichiers, variables d'environnement  
- **MCP :** Contexte réseau, état de session

---

## Plan d'Implémentation Recommandé

### 🎯 Phase 1 : Validation du Concept (CLI)
**Objectif :** Prouver l'architecture avec l'outil le plus simple
1. Développer l'adaptateur Claude-Code ou Gemini-CLI
2. Implémenter la résolution de templates
3. Valider l'extraction JSON et la validation de sortie

### 🔧 Phase 2 : Outillage de Debug
**Objectif :** Faciliter le développement d'adaptateurs
1. Script CLI de debug des templates
2. Validateur de sortie JSON
3. Documentation des patterns d'adaptation

### 🏗️ Phase 3 : Extension des Capacités
**Objectif :** Couvrir les outils prioritaires
1. Extension VS Code Task-Master
2. Adaptateurs MCP servers  
3. Intégration IDE IA (commandes custom)

### 📚 Phase 4 : Spécialisations
**Objectif :** Outils de niche et cas particuliers
1. Templates spécialisés pour Traycer
2. Optimisations Perplexity MCP
3. Nouveaux prompts selon besoins émergents

---

## Recommandations Stratégiques Finales

### ✅ Actions Immédiates
1. **Créer un script de debug** pour tester la résolution de templates
2. **Documenter le pattern d'adaptateur** dans le README officiel
3. **Identifier et tester** les APIs des extensions VS Code critiques

### 🎯 Priorités Techniques
1. **Commencer par CLI** - Cible la plus fiable et prévisible
2. **Développer la couche de validation** - Essentielle pour tous les adaptateurs
3. **Créer des adaptateurs modulaires** - Réutilisabilité maximale

### 🔮 Vision Long Terme
L'architecture Task-Master est conçue pour évoluer. Le modèle Cœur + Adaptateurs permet :
- **Ajout transparent** de nouveaux outils sans modification du cœur
- **Evolution indépendante** des prompts et des intégrations
- **Maintenance centralisée** de la logique métier
- **Extensibilité illimitée** vers de nouveaux domaines d'application

---

*Rapport généré par analyse Zen - Architecture validée par expert système*
