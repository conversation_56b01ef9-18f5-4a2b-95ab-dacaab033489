{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://github.com/eyaltoledano/claude-task-master/blob/main/src/prompts/schemas/parameter.schema.json", "version": "1.0.0", "title": "Paramètre de l'invite Task Master", "description": "Schéma pour les paramètres individuels des modèles d'invite", "type": "object", "required": ["type", "description"], "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "Le type de données attendu pour ce paramètre"}, "description": {"type": "string", "minLength": 1, "description": "Description lisible par l'humain du paramètre"}, "required": {"type": "boolean", "default": false, "description": "Ce paramètre est-il obligatoire"}, "default": {"description": "Valeur par défaut pour les paramètres optionnels"}, "enum": {"type": "array", "description": "Valeurs valides pour les paramètres de type chaîne", "items": {"type": "string"}}, "pattern": {"type": "string", "description": "Expression régulière pour la validation des chaînes"}, "minimum": {"type": "number", "description": "Valeur minimale pour les paramètres numériques"}, "maximum": {"type": "number", "description": "Valeur maximale pour les paramètres numériques"}}}