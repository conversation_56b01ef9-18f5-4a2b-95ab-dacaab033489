# MCP Perplexity Setup Guide

## Overview
This guide provides step-by-step instructions for setting up Perplexity MCP server integration across GitHub Copilot, Cursor, and Claude-Code environments.

## Prerequisites

### Required Software
- Node.js 18+ installed
- npm or yarn package manager
- Valid Perplexity API key
- Target AI tools (GitHub Copilot, Cursor, or Claude-Code)

### API Key Setup
1. Obtain Perplexity API key from [Perplexity Developer Portal](https://docs.perplexity.ai/)
2. Set environment variable:
   ```bash
   # Windows
   set PERPLEXITY_API_KEY=your_api_key_here
   
   # macOS/Linux
   export PERPLEXITY_API_KEY=your_api_key_here
   ```
3. For persistent setup, add to your shell profile (.bashrc, .zshrc, etc.)

## Installation Steps

### 1. Install MCP Perplexity Server
```bash
# Global installation (recommended)
npm install -g @perplexity/mcp-server

# Or use npx for on-demand usage
npx -y @perplexity/mcp-server --version
```

### 2. Verify Installation
```bash
# Test basic functionality
npx @perplexity/mcp-server --help

# Test API connectivity (requires API key)
npx @perplexity/mcp-server --test-connection
```

## Tool-Specific Configuration

### GitHub Copilot Integration

#### Method 1: VS Code Extension Configuration
1. Open VS Code settings (Ctrl/Cmd + ,)
2. Search for "GitHub Copilot"
3. Add MCP server configuration:
   ```json
   {
     "github.copilot.advanced": {
       "mcpServers": {
         "perplexity": {
           "command": "npx",
           "args": ["-y", "@perplexity/mcp-server"],
           "env": {
             "PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"
           }
         }
       }
     }
   }
   ```

#### Method 2: Workspace Configuration
1. Create `.vscode/settings.json` in your project root:
   ```json
   {
     "github.copilot.advanced": {
       "mcpServers": {
         "perplexity": {
           "command": "npx",
           "args": ["-y", "@perplexity/mcp-server"],
           "env": {
             "PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"
           }
         }
       }
     }
   }
   ```

#### Method 3: Chatmode Integration
1. Copy chatmode files to `.github/chatmodes/`
2. Chatmodes will automatically detect and use MCP Perplexity when available
3. Use research-focused chatmodes for enhanced capabilities

### Cursor Integration

#### Native MCP Support
1. Open Cursor settings
2. Navigate to "Extensions" → "MCP Servers"
3. Add Perplexity server configuration:
   ```json
   {
     "perplexity": {
       "command": "npx",
       "args": ["-y", "@perplexity/mcp-server"],
       "env": {
         "PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"
       }
     }
   }
   ```

#### Project-Level Configuration
1. Create `.cursor/mcp-config.json`:
   ```json
   {
     "servers": {
       "perplexity": {
         "command": "npx",
         "args": ["-y", "@perplexity/mcp-server"],
         "env": {
           "PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"
         }
       }
     }
   }
   ```

#### Rules Integration
1. Cursor rules automatically detect MCP availability
2. Research-enhanced rules will use Perplexity when configured
3. No additional configuration needed for rule files

### Claude-Code Integration

#### Agent Configuration
1. Agents automatically detect MCP server availability
2. Configure agent routing in `claude-code/CLAUDE.md`
3. Set research preferences in agent configurations

#### Manual Configuration
1. Create `claude-code/mcp-config.json`:
   ```json
   {
     "mcpServers": {
       "perplexity": {
         "command": "npx",
         "args": ["-y", "@perplexity/mcp-server"],
         "env": {
           "PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"
         }
       }
     }
   }
   ```

## Configuration Validation

### Test MCP Connection
```bash
# Test basic connectivity
curl -X POST http://localhost:3000/mcp/test \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Test Perplexity API
curl -X POST http://localhost:3000/mcp/perplexity/ask \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Test connection"}
    ]
  }'
```

### Validate Tool Integration

#### GitHub Copilot Validation
1. Open VS Code with GitHub Copilot enabled
2. Start a chat session
3. Use research-focused chatmode
4. Verify Perplexity responses include citations and web search results

#### Cursor Validation
1. Open Cursor with MCP configured
2. Use research-enhanced rules
3. Verify comprehensive research responses
4. Check for real-time web information

#### Claude-Code Validation
1. Use research agent commands
2. Verify enhanced research capabilities
3. Check agent coordination with MCP tools
4. Test slash command integration

## Troubleshooting

### Common Issues

#### API Key Not Found
```
Error: PERPLEXITY_API_KEY environment variable not set
```
**Solution**: Verify environment variable is set correctly
```bash
echo $PERPLEXITY_API_KEY  # Should display your API key
```

#### MCP Server Not Starting
```
Error: Failed to start MCP server
```
**Solutions**:
1. Check Node.js version: `node --version` (requires 18+)
2. Reinstall MCP server: `npm uninstall -g @perplexity/mcp-server && npm install -g @perplexity/mcp-server`
3. Check firewall settings

#### Rate Limiting Issues
```
Error: Rate limit exceeded
```
**Solutions**:
1. Check API usage in Perplexity dashboard
2. Implement request queuing (automatic in configuration)
3. Upgrade API plan if needed

#### Tool Integration Issues
```
Error: MCP server not detected by tool
```
**Solutions**:
1. Restart the AI tool after configuration
2. Verify configuration file syntax
3. Check tool-specific MCP support documentation

### Debug Mode
Enable debug logging for troubleshooting:
```bash
# Set debug environment variable
export DEBUG=mcp:*

# Run with verbose logging
npx @perplexity/mcp-server --verbose
```

### Log Analysis
Check logs for common issues:
```bash
# View recent logs
tail -f ~/.mcp/logs/perplexity.log

# Search for errors
grep -i error ~/.mcp/logs/perplexity.log
```

## Performance Optimization

### Caching Configuration
```json
{
  "caching": {
    "enabled": true,
    "ttl": 3600,
    "maxSize": "100MB"
  }
}
```

### Request Optimization
```json
{
  "optimization": {
    "requestBatching": true,
    "connectionPooling": true,
    "responseCompression": true
  }
}
```

### Rate Limiting
```json
{
  "rateLimiting": {
    "requestsPerMinute": 60,
    "requestsPerHour": 1000,
    "burstLimit": 10
  }
}
```

## Security Considerations

### API Key Security
- Never commit API keys to version control
- Use environment variables or secure key management
- Rotate API keys regularly
- Monitor API usage for anomalies

### Network Security
- Use HTTPS for all API communications
- Implement request validation
- Enable response validation
- Consider VPN for sensitive projects

### Data Privacy
- Review Perplexity data usage policies
- Implement data encryption for sensitive projects
- Consider local caching implications
- Monitor data transmission logs

## Monitoring and Maintenance

### Health Checks
```bash
# Automated health check script
#!/bin/bash
curl -f http://localhost:3000/health || exit 1
```

### Usage Monitoring
- Monitor API quota usage
- Track response times
- Analyze error rates
- Review cache hit ratios

### Regular Maintenance
- Update MCP server monthly
- Review and rotate API keys quarterly
- Clean cache storage regularly
- Update tool configurations as needed

## Support and Resources

### Documentation
- [Perplexity API Documentation](https://docs.perplexity.ai/)
- [MCP Protocol Specification](https://spec.modelcontextprotocol.io/)
- [Tool-specific MCP guides](https://github.com/modelcontextprotocol)

### Community Support
- GitHub Issues for MCP server
- Tool-specific community forums
- Stack Overflow with relevant tags

### Professional Support
- Perplexity Enterprise support
- Tool vendor support channels
- Professional consulting services
