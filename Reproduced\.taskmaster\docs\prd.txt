<context>
# Vue d'ensemble  
Ce PRD décrit l'implémentation d'un système de persistance SQLite pour les conversations dans zen-mcp-server. Actuellement, le système utilise un stockage en mémoire (`InMemoryStorage`) qui présente des limitations critiques : les conversations sont perdues au redémarrage du serveur, ont une durée limitée (TTL de 3 heures), et aucun historique consultable n'est disponible pour les utilisateurs. Cette fonctionnalité transformera zen-mcp-server d'un outil de session en un véritable assistant persistant en permettant de conserver les conversations au-delà des redémarrages et d'offrir un historique consultable aux utilisateurs.

# Fonctionnalités principales  
## 1. Backend de stockage SQLite persistant
- Remplacement du système `InMemoryStorage` actuel par une nouvelle classe `SQLiteStorage`
- Conservation des conversations au-delà des redémarrages du serveur
- Maintien de la compatibilité avec l'interface existante (`setex`, `get`)
- Configuration dynamique via variables d'environnement (SQLite vs mémoire)

## 2. Schéma de base de données optimisé
- Table `conversations` avec métadonnées indexées pour recherche rapide
- Stockage hybride : métadonnées extraites + JSON blob pour le contexte complet
- Index sur dates, outils, projets pour performance de recherche
- Support TTL automatique avec nettoyage en arrière-plan

## 3. Outil MCP d'historique des conversations
- Nouvel outil `@history` pour lister et rechercher les conversations passées
- Filtrage par outil, projet, date, et contenu
- Reprise de conversations via `continuation_id`
- Interface utilisateur structurée avec tableau formaté

## 4. Extraction intelligente de métadonnées
- Extraction automatique du chemin projet depuis les contextes de conversation
- Génération de résumés automatiques
- Identification du prompt initial pour recherche rapide
- Heuristiques robustes pour gérer les contextes incomplets

# Expérience utilisateur  
## Personas utilisateurs
- **Développeurs utilisant zen-mcp-server** : Bénéficient de la persistance pour reprendre des analyses complexes
- **Équipes collaboratives** : Peuvent partager et consulter l'historique des interactions IA
- **Utilisateurs power** : Exploitent l'historique pour traçabilité et productivité

## Principaux parcours utilisateurs
1. **Configuration initiale** : Activation de SQLite via variable d'environnement
2. **Usage normal** : Conversations automatiquement persistées de manière transparente
3. **Recherche d'historique** : `@history --tool analyze --limit 5` pour retrouver conversations passées
4. **Reprise de conversation** : `@analyze --continuation_id <uuid>` pour continuer une analyse

## Considérations UI/UX
- Migration transparente depuis stockage mémoire
- Aucun changement dans l'expérience de conversation normale
- Interface de recherche intuitive avec filtres multiples
- Format de sortie structuré pour faciliter la lecture
</context>
<PRD>
# Architecture technique  
## Composants du système
- **SQLiteStorage** : Nouvelle classe backend implémentant l'interface `ConversationStorage`
- **Fabrique de backend** : `get_storage_backend()` pour sélection dynamique (SQLite/mémoire)
- **Extracteur de métadonnées** : Utilitaires pour indexation et recherche
- **Outil History** : Nouvel outil MCP pour interface utilisateur d'historique
- **Worker de maintenance** : Nettoyage TTL et optimisation BDD en arrière-plan

## Modèles de données
```sql
CREATE TABLE conversations (
    thread_id TEXT PRIMARY KEY,
    parent_thread_id TEXT,
    tool_name TEXT NOT NULL,
    project_path TEXT,
    created_at TEXT NOT NULL,
    last_updated_at TEXT NOT NULL,
    context_json TEXT NOT NULL,
    summary TEXT,
    initial_prompt TEXT
);
```

## APIs et intégrations
- Conservation de l'interface Redis-like existante (`setex`, `get`)
- Intégration transparente avec `utils/conversation_memory.py`
- Nouveaux endpoints MCP pour outil d'historique
- Configuration via variables d'environnement `MCP_STORAGE_BACKEND` et `MCP_SQLITE_PATH`

## Besoins en infrastructure
- Base de données SQLite locale avec optimisations WAL
- Montage de volume Docker pour persistance des fichiers
- Dossier `./data/` dédié pour stockage des bases de données
- Tâches de maintenance automatique (nettoyage, VACUUM, backup)

# Feuille de route de développement  
## Phase 1 - MVP : Backend SQLite fondamental (Semaines 1-2)
- Interface abstraite `ConversationStorage` 
- Implémentation `SQLiteStorage` avec schéma de base
- Migration `InMemoryStorage` vers nouveau système modulaire
- Configuration dynamique backend via variables d'environnement
- Tests unitaires pour opérations de base (setex, get, TTL)
- Fabrique de backend avec fallback mémoire

## Phase 2 - Outil d'historique et métadonnées (Semaine 3)
- Extracteur de métadonnées intelligent (projet, résumé, prompt initial)
- Nouvel outil MCP `HistoryTool` avec interface de recherche
- Filtrage par outil, projet, date et contenu
- Format de sortie structuré avec instructions de reprise
- Tests d'intégration outil/backend SQLite

## Phase 3 - Optimisation et maintenance (Semaine 4)
- Worker de maintenance automatique (nettoyage TTL, VACUUM)
- Configuration SQLite optimisée (WAL, pragmas performance)
- Gestion d'erreurs robuste et logging détaillé
- Tests de performance et charge
- Support Docker avec montage de volumes

## Phase 4 - Documentation et déploiement (Semaine 5)
- Documentation utilisateur complète (configuration, usage)
- Guide de migration mémoire vers SQLite
- Tests de régression et validation
- Déploiement progressif avec monitoring
- Procédures de backup et restauration

# Chaîne logique de dépendances
## Ordre de développement fondamental
1. **Interface et fabrique de stockage** : Base architecturale pour interchangeabilité des backends
2. **Implémentation SQLiteStorage** : Cœur du système de persistance avec schéma BDD
3. **Extracteur de métadonnées** : Nécessaire pour indexation et recherche efficace
4. **Migration InMemoryStorage** : Isolation et compatibilité rétrograde
5. **Configuration dynamique** : Variables d'environnement et sélection de backend

## Front-end utilisable rapide
6. **Outil History basique** : Interface utilisateur immédiatement fonctionnelle
7. **Tests d'intégration** : Validation bout-en-bout du système complet
8. **Configuration Docker** : Déploiement et persistance dans conteneurs

## Amélioration incrémentale
9. **Optimisations performance** : WAL, index, VACUUM pour production
10. **Worker de maintenance** : Automatisation nettoyage et optimisation
11. **Documentation et guides** : Adoption utilisateur et migration

# Risques et mesures d'atténuation  
## Défis techniques
- **Corruption de base SQLite** : Mode WAL, backup automatique, validation données
- **Performance dégradée sur gros volumes** : Index optimisés, pagination, VACUUM périodique
- **Migration données existantes** : Stratégie de déploiement progressif avec fallback
- **Sécurité injection SQL** : Requêtes paramétrées exclusivement, validation entrées

## MVP sur lequel s'appuyer
- Backend de stockage interchangeable avec fallback mémoire garanti
- Interface existante préservée pour compatibilité totale
- Configuration par variable d'environnement pour contrôle utilisateur
- Tests unitaires complets avant déploiement production

## Contraintes de ressources
- **Espace disque** : TTL configuré, nettoyage automatique, compression
- **Temps de développement** : Phases parallélisables, tests automatisés
- **Compatibilité** : Interface abstraite maintient rétrocompatibilité

# Annexe  
## Résultats de recherche
- Analyse approfondie du code existant dans `utils/storage_backend.py` et `utils/conversation_memory.py`
- Étude des patterns d'outils MCP existants (`tools/listmodels.py`, `tools/version.py`)
- Évaluation de l'infrastructure de test avec mocks et fixtures temporaires
- Configuration Docker existante pour montage de volumes

## Spécifications techniques
- **Thread Safety** : Utilisation de locks Python pour accès concurrent SQLite
- **Performance SQLite** : PRAGMA optimisés (journal_mode=WAL, synchronous=NORMAL, mmap_size)
- **Extraction métadonnées** : Heuristiques robustes pour project_path, génération résumé IA
- **Maintenance automatique** : Thread dédié pour nettoyage TTL et VACUUM périodique
- **Sécurité** : Permissions fichiers, validation UUID, requêtes paramétrées uniquement
</PRD>