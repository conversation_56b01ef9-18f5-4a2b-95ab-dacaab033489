# Rapport d'analyse des prompts Task-Master pour adaptation multi-outils

Ce document analyse les prompts de Task-Master et propose des adaptations détaillées pour différents outils d'IA de développement.

## Vue d'ensemble

Task-Master utilise un système sophistiqué de gestion de prompts avec :
- **Templates JSON structurés** avec validation par schéma AJV
- **Système de variables** avec syntaxe Handlebars ({{variable}}, {{#if}}, {{#each}})
- **Variantes conditionnelles** selon le contexte (research, complexity-report, batch)
- **Paramètres typés** avec validation (string, number, boolean, array, object)
- **Support de la recherche** et du contexte projet
- **Helpers avancés** (eq, not, gt, gte, json)

## Structure des prompts analysés

### Prompts identifiés et leurs fonctionnalités

1. **add-task.json** - Création de nouvelles tâches
   - Paramètres : prompt, newTaskId, existingTasks, gatheredContext, priority, dependencies, useResearch
   - Variante : research (si useResearch === true)

2. **expand-task.json** - Décomposition en sous-tâches (3 stratégies)
   - Paramètres : subtaskCount, task, nextSubtaskId, useResearch, expansionPrompt, additionalContext
   - Variantes : complexity-report, research, default

3. **update-task.json** - Mise à jour de tâches individuelles
   - Paramètres : task, taskJson, updatePrompt, appendMode, useResearch, currentDetails
   - Variantes : append, research, default

4. **update-tasks.json** - Mise à jour en masse
   - Paramètres : tasks, updatePrompt, useResearch, projectContext
   - Variante : research

5. **update-subtask.json** - Mise à jour de sous-tâches
   - Paramètres : parentTask, currentDetails, updatePrompt, prevSubtask, nextSubtask, useResearch
   - Variante : research

6. **analyze-complexity.json** - Analyse de complexité
   - Paramètres : tasks, gatheredContext, threshold, useResearch
   - Variantes : research, batch (si > 10 tâches), default

7. **parse-prd.json** - Analyse de documents PRD
   - Paramètres : numTasks, nextId, prdContent, prdPath, defaultTaskPriority, research
   - Variante : research

8. **research.json** - Recherche assistée par IA
   - Paramètres : query, gatheredContext, detailLevel, projectInfo
   - Variantes : low, medium, high (selon detailLevel)

## Adaptations par outil

### 1. IDE IA

#### GitHub Copilot
**Méthode d'adaptation** : Création de fichiers d'instructions et de Chatmodes

**Défis identifiés** :
- Pas de support natif des variables Handlebars
- Système de variantes limité
- Contexte de conversation limité

**Solutions proposées** :
- Convertir les templates Handlebars en instructions statiques avec placeholders
- Créer des Chatmodes séparés pour chaque variante principale
- Utiliser les commentaires de code pour injecter le contexte

**Structure d'adaptation** :
```
.github/
├── instructions/
│   ├── add-task.instructions.md
│   ├── expand-task.instructions.md
│   ├── update-task.instructions.md
│   └── research.instructions.md
└── chatmodes/
    ├── task-creation.chatmode.md
    ├── task-expansion.chatmode.md
    └── research-mode.chatmode.md
```

**Exemple d'adaptation pour add-task** :
```markdown
# Task Creation Instructions

You are a helpful assistant that creates well-structured tasks for software development projects.

## Context Variables to Replace:
- {PROMPT}: User's task description
- {NEW_TASK_ID}: ID for the new task
- {EXISTING_TASKS}: List of existing tasks for context
- {PRIORITY}: Task priority (high/medium/low)

## Instructions:
Generate a single new task from the user description, strictly following the JSON schema provided...
```

#### Cursor
**Méthode d'adaptation** : Création de fichiers d'instructions et de Modes Personnalisés

**Avantages** :
- Support des instructions personnalisées
- Contexte de projet intégré
- Modes personnalisés avec état

**Solutions proposées** :
- Créer des fichiers .cursorrules pour chaque type de prompt
- Utiliser les modes personnalisés pour les variantes
- Exploiter l'intégration du contexte de projet

**Structure d'adaptation** :
```
.cursor/
├── rules/
│   ├── task-management.mdc
│   ├── research.mdc
│   └── complexity-analysis.mdc
└── modes/
    ├── task-creator.json
    ├── task-expander.json
    └── researcher.json
```

#### Trae
**Méthode d'adaptation** : Modes Personnalisés avec injection de contexte

**Avantages** :
- Support avancé des modes personnalisés
- Injection de contexte sophistiquée
- Variables d'environnement

**Solutions proposées** :
- Créer des modes pour chaque prompt principal
- Utiliser les variables d'environnement pour les paramètres
- Exploiter l'injection automatique de contexte

### 2. Extensions VS Code IA

#### RooCode / Kilocode
**Méthode d'adaptation** : Modes Personnalisés

**Défis identifiés** :
- Interface limitée pour les paramètres complexes
- Pas de support des variantes conditionnelles

**Solutions proposées** :
- Créer des modes séparés pour chaque variante
- Utiliser des prompts de sélection pour les paramètres
- Simplifier la logique conditionnelle

#### Cline
**Méthode d'adaptation** : Fichiers d'instructions

**Avantages** :
- Support des instructions détaillées
- Intégration avec le système de fichiers
- Contexte de projet automatique

**Solutions proposées** :
- Créer des fichiers d'instructions markdown
- Utiliser des templates avec substitution manuelle
- Exploiter l'accès au système de fichiers pour le contexte

### 3. Outils IA en ligne de commande

#### Claude-Code
**Méthode d'adaptation** : Fichiers d'instructions, commandes slash et sous-agents

**Avantages** :
- Support complet des instructions complexes
- Système de sous-agents pour les variantes
- Commandes slash pour l'interaction

**Solutions proposées** :
- Créer des sous-agents pour chaque type de prompt
- Utiliser les commandes slash pour les paramètres
- Implémenter la logique de variantes dans les sous-agents

**Structure d'adaptation** :
```
.claude/
├── agents/
│   ├── task-creator.md
│   ├── task-expander.md
│   └── researcher.md
├── commands/
│   ├── add-task.md
│   ├── expand-task.md
│   └── research.md
└── CLAUDE.md
```

#### Gemini-CLI
**Méthode d'adaptation** : Fichiers d'instructions `GEMINI.md`

**Défis identifiés** :
- Interface en ligne de commande limitée
- Pas de support des variantes complexes

**Solutions proposées** :
- Créer des fichiers d'instructions simples
- Utiliser des arguments CLI pour les paramètres
- Simplifier la logique conditionnelle

### 4. Outils spécialisés

#### Traycer
**Méthode d'adaptation** : Extension des capacités de planification

**Avantages** :
- Spécialisé dans la génération de plans de développement
- Intégration VS Code native
- Support des phases de développement

**Solutions proposées** :
- Adapter les prompts d'expansion et d'analyse de complexité
- Créer des templates de planification par phase
- Intégrer avec les prompts de décomposition de tâches

#### Perplexity MCP
**Méthode d'adaptation** : Intégration des capacités de recherche

**Avantages** :
- Accès aux modèles Sonar pour la recherche
- Recherche web, réflexion et approfondie
- Intégration MCP

**Solutions proposées** :
- Adapter le prompt research.json pour les outils Sonar
- Créer des wrappers MCP pour les prompts de recherche
- Exploiter les différents niveaux de recherche (web, réflexion, approfondie)

#### Zen-MCP-Server
**Méthode d'adaptation** : Exposition des prompts comme outils MCP

**Avantages** :
- Exposition de nombreux outils et modèles LLM
- Architecture MCP extensible
- Boost des capacités de l'agent IA

**Solutions proposées** :
- Créer des outils MCP pour chaque prompt Task-Master
- Implémenter la logique de variantes dans les outils
- Exploiter les multiples modèles LLM disponibles

## Défis techniques communs

### 1. Gestion des variables Handlebars
**Problème** : La plupart des outils ne supportent pas nativement Handlebars
**Solutions** :
- Conversion en templates simples avec placeholders
- Implémentation de logique de substitution personnalisée
- Utilisation de scripts de préprocessing

### 2. Variantes conditionnelles
**Problème** : Logique conditionnelle complexe difficile à reproduire
**Solutions** :
- Création de modes/agents séparés pour chaque variante
- Simplification de la logique conditionnelle
- Utilisation de sélection manuelle des variantes

### 3. Validation des paramètres
**Problème** : Pas de validation automatique des types et contraintes
**Solutions** :
- Validation manuelle dans les instructions
- Création de scripts de validation séparés
- Documentation claire des contraintes

### 4. Contexte de projet
**Problème** : Injection du contexte projet variable selon l'outil
**Solutions** :
- Utilisation des capacités natives de chaque outil
- Création de scripts d'extraction de contexte
- Documentation des méthodes d'injection

## Recommandations d'implémentation

### Phase 1 : Outils prioritaires
1. **Claude-Code** - Support le plus complet des fonctionnalités
2. **Cursor** - Intégration IDE excellente
3. **Perplexity MCP** - Capacités de recherche avancées

### Phase 2 : Extensions et outils spécialisés
1. **Cline** - Simplicité d'implémentation
2. **Traycer** - Spécialisation planification
3. **GitHub Copilot** - Large adoption

### Phase 3 : Outils complémentaires
1. **RooCode/Kilocode** - Modes personnalisés
2. **Gemini-CLI** - Interface ligne de commande
3. **Zen-MCP-Server** - Architecture extensible

### Stratégie de test
1. **Tests unitaires** pour chaque adaptation
2. **Tests d'intégration** avec les outils cibles
3. **Tests de régression** pour maintenir la cohérence
4. **Tests utilisateur** pour valider l'expérience

## Exemples d'implémentation détaillés

### GitHub Copilot - Exemple add-task

**Fichier : .github/copilot/instructions/add-task.md**
```markdown
# Task Creation Assistant

## Role
You are a software development task creation assistant. Generate well-structured tasks from user descriptions.

## Input Format
- TASK_DESCRIPTION: {user input}
- TASK_ID: {numeric id}
- EXISTING_TASKS: {context tasks}
- PRIORITY: {high|medium|low}
- USE_RESEARCH: {true|false}

## Output Format
Return ONLY a JSON object with this exact structure:
{
  "title": "Task title here",
  "description": "1-2 sentence description",
  "details": "Detailed implementation steps",
  "testStrategy": "Specific verification steps",
  "dependencies": [1, 3] // Array of task IDs
}

## Instructions
1. Create actionable, specific tasks
2. Include relevant dependencies from EXISTING_TASKS
3. If USE_RESEARCH is true, incorporate current best practices
4. Focus on software development context
5. Ensure dependencies are logical prerequisites
```

### Cursor - Exemple expand-task

**Fichier : .cursor/rules/task-expansion.cursorrules**
```markdown
# Task Expansion Rules

## Context
You are expanding software development tasks into subtasks.

## Variables
- SUBTASK_COUNT: Number of subtasks to generate
- PARENT_TASK: Task object to expand
- NEXT_ID: Starting ID for subtasks
- EXPANSION_TYPE: complexity-report|research|default

## Behavior
When expanding tasks:
1. Generate exactly SUBTASK_COUNT subtasks
2. Use sequential IDs starting from NEXT_ID
3. Create logical dependency chains using format "parentId.subtaskNumber"
4. Include implementation details and test strategies
5. Ensure subtasks cover all aspects of parent task

## Output
Return JSON with "subtasks" array containing objects with:
- id, title, description, dependencies, details, testStrategy
```

### Claude-Code - Exemple research agent

**Fichier : claude-code/agents/researcher.md**
```markdown
# Research Agent

## Purpose
Perform AI-assisted research with project context awareness.

## Parameters
- query: Research query (required)
- gatheredContext: Project context (optional)
- detailLevel: low|medium|high (default: medium)
- projectInfo: Object with root, taskCount, fileCount

## Behavior Variants

### Low Detail
- Provide brief, targeted responses (2-4 paragraphs max)
- Focus on essential information
- Use bullet points for key items
- Avoid long explanations
- Direct, actionable information only

### Medium Detail
- Provide structured, comprehensive responses (4-8 paragraphs)
- Include relevant examples and explanations
- Balance depth with readability
- Use headers and lists for organization

### High Detail
- Provide thorough, exhaustive analysis (8+ paragraphs)
- Include multiple perspectives and approaches
- Provide detailed examples and code snippets
- Cover edge cases and potential pitfalls
- Use clear structure with headers and subheaders

## Context Integration
Always consider project context when formulating responses:
- Reference specific project tasks, files, or elements when relevant
- Provide actionable advice applicable to the project
- Suggest how research applies to existing project tasks
- Use markdown formatting for better readability
```

### Perplexity MCP - Exemple d'intégration

**Fichier : perplexity-mcp/tools/task-research.json**
```json
{
  "name": "task_research",
  "description": "Research assistant for Task-Master with Sonar models",
  "parameters": {
    "query": {
      "type": "string",
      "description": "Research query"
    },
    "context": {
      "type": "string",
      "description": "Project context"
    },
    "detail_level": {
      "type": "string",
      "enum": ["low", "medium", "high"],
      "default": "medium"
    },
    "search_type": {
      "type": "string",
      "enum": ["web", "reflection", "deep"],
      "default": "web"
    }
  },
  "implementation": {
    "model_mapping": {
      "web": "sonar-pro",
      "reflection": "sonar-pro",
      "deep": "sonar-pro"
    },
    "prompt_template": "research.json",
    "context_injection": "automatic"
  }
}
```

## Matrice de compatibilité des fonctionnalités

| Fonctionnalité | GitHub Copilot | Cursor | Trae | RooCode | Cline | Claude-Code | Gemini-CLI | Traycer | Perplexity MCP | Zen-MCP |
|----------------|----------------|--------|------|---------|-------|-------------|------------|---------|----------------|---------|
| Variables Handlebars | ❌ | ⚠️ | ✅ | ❌ | ⚠️ | ✅ | ❌ | ⚠️ | ⚠️ | ✅ |
| Variantes conditionnelles | ❌ | ⚠️ | ✅ | ❌ | ❌ | ✅ | ❌ | ⚠️ | ⚠️ | ✅ |
| Validation paramètres | ❌ | ⚠️ | ⚠️ | ❌ | ❌ | ✅ | ❌ | ❌ | ⚠️ | ✅ |
| Contexte projet | ✅ | ✅ | ✅ | ⚠️ | ✅ | ✅ | ❌ | ✅ | ⚠️ | ✅ |
| Helpers avancés | ❌ | ❌ | ⚠️ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ |
| Modes multiples | ⚠️ | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ | ⚠️ | ❌ | ✅ |

**Légende :**
- ✅ Support complet
- ⚠️ Support partiel/avec adaptations
- ❌ Pas de support

## Scripts d'aide à l'adaptation

### Script de conversion Handlebars vers templates simples

**Fichier : scripts/convert-handlebars.js**
```javascript
function convertHandlebarsToSimple(template, parameters) {
  let converted = template;

  // Remplacer les variables simples
  Object.keys(parameters).forEach(param => {
    const regex = new RegExp(`{{${param}}}`, 'g');
    converted = converted.replace(regex, `{${param.toUpperCase()}}`);
  });

  // Convertir les conditionnels simples
  converted = converted.replace(
    /{{#if\s+(\w+)}}(.*?){{\/if}}/gs,
    'IF_$1: $2'
  );

  // Convertir les boucles
  converted = converted.replace(
    /{{#each\s+(\w+)}}(.*?){{\/each}}/gs,
    'FOR_EACH_$1: $2'
  );

  return converted;
}
```

### Script de génération de modes Cursor

**Fichier : scripts/generate-cursor-modes.js**
```javascript
function generateCursorMode(promptData) {
  const mode = {
    name: promptData.id,
    description: promptData.description,
    instructions: convertToInstructions(promptData.prompts.default),
    parameters: Object.keys(promptData.parameters || {}),
    variants: Object.keys(promptData.prompts).filter(k => k !== 'default')
  };

  return JSON.stringify(mode, null, 2);
}

function convertToInstructions(prompt) {
  return {
    system: convertHandlebarsToSimple(prompt.system),
    user: convertHandlebarsToSimple(prompt.user)
  };
}
```

## Conclusion

L'adaptation des prompts Task-Master nécessite une approche différenciée selon les capacités de chaque outil. Les outils les plus avancés (Claude-Code, Cursor, Zen-MCP) peuvent reproduire fidèlement la logique complexe, tandis que d'autres nécessitent des simplifications significatives.

La stratégie recommandée est une implémentation progressive en commençant par les outils offrant le meilleur support des fonctionnalités avancées, puis en adaptant progressivement pour les outils plus limités en simplifiant la logique tout en préservant l'essence fonctionnelle des prompts originaux.
