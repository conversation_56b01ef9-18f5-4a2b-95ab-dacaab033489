# Cursor Rules: Perplexity MCP Research Integration

## Overview
Enhanced research capabilities for <PERSON>urs<PERSON> using Perplexity MCP server integration. Leverages Cursor's native MCP support for real-time web search, comprehensive analysis, and advanced reasoning.

## MCP Configuration
```yaml
mcpServer: "perplexity"
tools: ["perplexity_ask", "perplexity_research", "perplexity_reason"]
models: ["sonar-pro", "sonar-reasoning-pro"]
contextIntegration: true
projectAware: true
```

## Core Research Capabilities

### Real-Time Web Research
- Access current information through Sonar models
- Validate information against multiple sources
- Include recent developments and trends
- Provide citations with publication dates

### Context-Aware Analysis
- Integrate with <PERSON><PERSON><PERSON>'s project understanding
- Consider current codebase and architecture
- Adapt recommendations to existing technology stack
- Include migration and integration strategies

### Advanced Reasoning
- Systematic problem-solving approach
- Trade-off analysis with justified recommendations
- Risk assessment and mitigation strategies
- Optimization guidance and decision frameworks

## Research Modes

### Quick Research Mode
**Trigger**: @research-quick or "quick research on..."
**MCP Tool**: perplexity_ask
**Approach**:
- Fast web search for immediate answers
- Concise, actionable information
- Focus on current best practices
- Include relevant documentation links

**Example Usage**:
```
@research-quick TypeScript 5.3 new features
```

### Comprehensive Research Mode
**Trigger**: @research or "research..." (default)
**MCP Tool**: perplexity_research
**Approach**:
- Multi-source comprehensive analysis
- Detailed implementation guidance
- Include examples and case studies
- Consider project-specific factors

**Example Usage**:
```
@research state management options for React e-commerce app
```

### Deep Analysis Mode
**Trigger**: @research-deep or "deep analysis of..."
**MCP Tool**: perplexity_research (extended)
**Approach**:
- Exhaustive research across multiple perspectives
- Detailed comparison of alternatives
- Include expert opinions and recent studies
- Cover edge cases and future considerations

**Example Usage**:
```
@research-deep microservices vs monolith for our platform
```

### Reasoning Mode
**Trigger**: @research-reason or "analyze decision..."
**MCP Tool**: perplexity_reason
**Approach**:
- Logical step-by-step analysis
- Systematic evaluation of options
- Consider constraints and requirements
- Provide justified recommendations

**Example Usage**:
```
@research-reason database choice for high-traffic application
```

## Context Integration with Cursor

### Project Context Detection
Automatically include in MCP queries:
- Current file and directory context
- Project type and technology stack
- Package.json dependencies and scripts
- README.md project description
- Recent git commits and changes

### Codebase Awareness
- Analyze current code patterns and architecture
- Consider existing dependencies and versions
- Evaluate compatibility with proposed changes
- Include refactoring and migration strategies

### File-Specific Research
When researching in context of specific files:
- Include file type and framework context
- Consider current implementation patterns
- Suggest improvements based on current code
- Include testing and validation strategies

## Research Workflows

### Technology Evaluation Workflow
```
1. Detect current technology stack from project
2. Research alternatives with MCP perplexity_research
3. Compare options based on project requirements
4. Include migration complexity assessment
5. Provide implementation roadmap
6. Consider team expertise and timeline
```

### Problem-Solving Workflow
```
1. Analyze current code and error context
2. Research solutions with MCP perplexity_reason
3. Evaluate options systematically
4. Consider project constraints and architecture
5. Provide step-by-step resolution guidance
6. Include prevention and monitoring strategies
```

### Best Practices Workflow
```
1. Identify current implementation approach
2. Research current best practices with MCP
3. Compare with existing implementation
4. Suggest improvements and optimizations
5. Include testing and validation approaches
6. Provide refactoring guidance
```

## MCP Query Enhancement

### Context-Enriched Queries
Automatically enhance MCP queries with:
- Project technology stack information
- Current file and directory context
- Relevant dependencies and versions
- Project constraints and requirements
- Team size and experience level

### Dynamic Query Construction
```typescript
// Example of context-aware query construction
const enhancedQuery = `
Research ${userQuery} for ${projectType} project using:
- Framework: ${detectedFramework}
- Language: ${primaryLanguage}
- Dependencies: ${keyDependencies.join(', ')}
- Context: ${currentFileContext}
- Focus: ${focusAreas.join(', ')}
`;
```

### Result Processing
- Filter results based on project relevance
- Prioritize information matching current stack
- Include implementation examples for detected technologies
- Adapt recommendations to project constraints

## Advanced Features

### Intelligent Caching
- Cache research results based on project context
- Invalidate cache when dependencies change
- Share cache across similar project contexts
- Optimize for frequently researched topics

### Progressive Research
- Start with quick overview using perplexity_ask
- Expand to comprehensive analysis if needed
- Use reasoning mode for complex decisions
- Adapt depth based on user engagement

### Multi-Modal Research
- Combine web search with code analysis
- Include documentation and community insights
- Consider performance benchmarks and case studies
- Integrate with Cursor's code understanding

## Research Templates

### Technology Comparison Template
```markdown
## {Technology} Comparison for {ProjectType}

### Current Landscape (via MCP)
[Real-time web research results]

### Options Analysis
| Option | Pros | Cons | Fit Score |
|--------|------|------|-----------|
| {Option1} | {Pros} | {Cons} | {Score}/10 |

### Recommendation
Based on your {ProjectContext}, I recommend {Choice} because:
- {Reason1}
- {Reason2}
- {Reason3}

### Implementation Plan
1. {Step1}
2. {Step2}
3. {Step3}

### Migration Strategy
[If applicable, based on current codebase analysis]
```

### Problem Resolution Template
```markdown
## Problem Analysis: {Issue}

### Root Cause Analysis (via MCP Reasoning)
[Systematic analysis of the problem]

### Solution Options
1. **{Solution1}**
   - Implementation: {Details}
   - Pros: {Advantages}
   - Cons: {Limitations}

### Recommended Approach
{Justified recommendation based on project context}

### Implementation Steps
[Step-by-step guidance with code examples]

### Testing Strategy
[Validation and testing approaches]

### Prevention
[Strategies to avoid similar issues]
```

## Integration Examples

### React Component Optimization
```
User: "How can I optimize this React component for performance?"

Cursor Action:
1. Analyze current component code
2. MCP Query: "React component performance optimization techniques 2024, considering {componentType} with {dependencies}"
3. Research current best practices
4. Compare with existing implementation
5. Provide specific optimization recommendations
6. Include performance measurement strategies
```

### API Design Research
```
User: "Research REST API design patterns for our Node.js service"

Cursor Action:
1. Detect Node.js project with Express/Fastify
2. MCP Query: "REST API design patterns and best practices for Node.js {framework} applications, focusing on {projectRequirements}"
3. Research current standards and patterns
4. Analyze existing API structure
5. Suggest improvements and new patterns
6. Include testing and documentation strategies
```

### Database Migration Analysis
```
User: "Should we migrate from PostgreSQL to MongoDB?"

Cursor Action:
1. Analyze current database usage patterns
2. MCP Query: "PostgreSQL vs MongoDB migration analysis for {projectType} with {dataPatterns} and {scalingRequirements}"
3. Use reasoning mode for systematic evaluation
4. Consider migration complexity and risks
5. Provide justified recommendation
6. Include migration strategy if recommended
```

## Error Handling

### MCP Server Unavailable
```
If Perplexity MCP server is not available:
1. Display clear message about limitation
2. Fall back to Cursor's built-in knowledge
3. Suggest manual research approaches
4. Include disclaimer about information currency
```

### Rate Limiting
```
If MCP rate limits are exceeded:
1. Queue research requests intelligently
2. Use cached results when available
3. Prioritize critical research needs
4. Suggest breaking complex queries into parts
```

### Invalid Responses
```
If MCP returns invalid or incomplete data:
1. Retry with simplified query
2. Fall back to alternative research approaches
3. Clearly indicate limitations to user
4. Suggest manual verification steps
```

## Performance Optimization

### Query Optimization
- Batch related research requests
- Use appropriate MCP tool for query type
- Include relevant context without over-specification
- Cache frequently requested information

### Response Processing
- Stream results for better user experience
- Process and format responses efficiently
- Integrate seamlessly with Cursor's UI
- Provide progressive disclosure of information

### Resource Management
- Monitor MCP server usage and performance
- Implement intelligent request queuing
- Optimize for Cursor's memory and processing constraints
- Balance thoroughness with response time

## Quality Assurance

### Information Validation
- Cross-reference multiple sources through MCP
- Verify compatibility with current project setup
- Include publication dates and source credibility
- Validate recommendations against project constraints

### Relevance Filtering
- Prioritize information relevant to current project
- Filter out outdated or incompatible solutions
- Focus on actionable and implementable recommendations
- Consider team expertise and project timeline

### Continuous Improvement
- Learn from user feedback and interactions
- Adapt research strategies based on project success
- Refine query construction and result processing
- Optimize for common research patterns and needs
