{"mcpServers": {"perplexity": {"command": "npx", "args": ["-y", "@perplexity/mcp-server"], "env": {"PERPLEXITY_API_KEY": "${PERPLEXITY_API_KEY}"}, "capabilities": {"tools": ["perplexity_ask", "perplexity_research", "perplexity_reason"], "models": ["sonar-pro", "sonar-reasoning-pro"]}, "configuration": {"defaultModel": "sonar-pro", "maxTokens": 4000, "temperature": 0.1, "timeout": 30000, "retryAttempts": 3, "rateLimiting": {"requestsPerMinute": 60, "requestsPerHour": 1000}}}}, "toolConfigurations": {"perplexity_ask": {"description": "General purpose conversational AI with web search capabilities", "model": "sonar-pro", "parameters": {"messages": {"type": "array", "required": true, "description": "Array of conversation messages"}}, "useCases": ["quick-questions", "general-research", "technology-overview", "best-practices-lookup"]}, "perplexity_research": {"description": "Deep research with comprehensive analysis and citations", "model": "sonar-pro", "parameters": {"messages": {"type": "array", "required": true, "description": "Array of conversation messages for research context"}}, "useCases": ["comprehensive-analysis", "technology-comparison", "market-research", "academic-research", "implementation-strategies"]}, "perplexity_reason": {"description": "Advanced reasoning and problem-solving capabilities", "model": "sonar-reasoning-pro", "parameters": {"messages": {"type": "array", "required": true, "description": "Array of conversation messages for reasoning context"}}, "useCases": ["complex-problem-solving", "architectural-decisions", "trade-off-analysis", "debugging-assistance", "optimization-strategies"]}}, "integrationSettings": {"github-copilot": {"enabled": true, "accessMethod": "chatmode-integration", "configuration": {"defaultTool": "perplexity_ask", "researchTool": "perplexity_research", "reasoningTool": "perplexity_reason", "contextInjection": true, "resultFormatting": "markdown"}}, "cursor": {"enabled": true, "accessMethod": "native-mcp", "configuration": {"defaultTool": "perplexity_research", "contextAware": true, "projectIntegration": true, "cacheResults": true, "resultFormatting": "structured"}}, "claude-code": {"enabled": true, "accessMethod": "agent-integration", "configuration": {"routingLogic": "automatic", "toolSelection": "context-based", "resultProcessing": "agent-specific", "errorHandling": "graceful-degradation"}}}, "researchProfiles": {"quick": {"tool": "perplexity_ask", "model": "sonar-pro", "maxTokens": 1000, "temperature": 0.1, "focusAreas": ["overview", "key-points", "actionable-insights"], "timeTarget": "1-2 minutes"}, "standard": {"tool": "perplexity_research", "model": "sonar-pro", "maxTokens": 2500, "temperature": 0.1, "focusAreas": ["comprehensive-analysis", "best-practices", "implementation-guidance"], "timeTarget": "5-10 minutes"}, "deep": {"tool": "perplexity_research", "model": "sonar-pro", "maxTokens": 4000, "temperature": 0.05, "focusAreas": ["detailed-analysis", "comparative-study", "future-considerations", "edge-cases"], "timeTarget": "15-30 minutes"}, "reasoning": {"tool": "perplexity_reason", "model": "sonar-reasoning-pro", "maxTokens": 3000, "temperature": 0.1, "focusAreas": ["problem-solving", "logical-analysis", "decision-support", "optimization"], "timeTarget": "10-20 minutes"}}, "contextTemplates": {"task-creation": {"systemPrompt": "You are assisting with software development task creation. Focus on practical, actionable insights that will help create well-structured development tasks.", "contextFields": ["projectType", "technologyStack", "teamSize", "timeline", "complexity"]}, "research": {"systemPrompt": "You are conducting comprehensive software development research. Provide detailed, accurate, and current information with practical implementation guidance.", "contextFields": ["projectConstraints", "technologyStack", "performanceRequirements", "securityRequirements", "scalabilityNeeds"]}, "complexity-analysis": {"systemPrompt": "You are analyzing software development complexity. Focus on identifying challenges, risks, and mitigation strategies.", "contextFields": ["technicalComplexity", "integrationChallenges", "teamExperience", "timeConstraints", "riskTolerance"]}, "task-expansion": {"systemPrompt": "You are helping break down complex software development tasks. Focus on logical decomposition and dependency management.", "contextFields": ["taskComplexity", "teamCapabilities", "developmentMethodology", "integrationPoints", "testingStrategy"]}}, "errorHandling": {"apiKeyMissing": {"action": "graceful-degradation", "fallback": "local-knowledge-base", "message": "Perplexity API key not configured. Using local knowledge for research."}, "rateLimitExceeded": {"action": "queue-request", "retryDelay": 60000, "maxRetries": 3, "message": "Rate limit exceeded. Request queued for retry."}, "networkError": {"action": "retry-with-backoff", "backoffMultiplier": 2, "maxRetries": 3, "message": "Network error. Retrying with exponential backoff."}, "invalidResponse": {"action": "request-clarification", "fallback": "simplified-request", "message": "Invalid response received. Attempting simplified request."}}, "caching": {"enabled": true, "strategy": "intelligent", "configuration": {"ttl": {"quick-research": 3600, "standard-research": 7200, "deep-research": 14400, "reasoning-results": 7200}, "invalidation": {"onProjectChange": true, "onTechnologyStackChange": true, "onRequirementsChange": true, "manual": true}, "storage": {"location": "local", "encryption": true, "compression": true, "maxSize": "100MB"}}}, "monitoring": {"enabled": true, "metrics": ["request-count", "response-time", "error-rate", "cache-hit-ratio", "token-usage"], "logging": {"level": "info", "includeRequestDetails": false, "includeResponseDetails": false, "retentionDays": 30}, "alerts": {"highErrorRate": {"threshold": 0.1, "action": "notify-admin"}, "slowResponse": {"threshold": 30000, "action": "log-warning"}, "quotaExceeded": {"threshold": 0.9, "action": "notify-user"}}}, "security": {"apiKeyStorage": "environment-variable", "requestValidation": true, "responseValidation": true, "dataEncryption": {"inTransit": true, "atRest": true}, "accessControl": {"userAuthentication": false, "requestLogging": true, "rateLimiting": true}}, "optimization": {"requestBatching": {"enabled": true, "maxBatchSize": 5, "batchTimeout": 1000}, "responseCompression": true, "connectionPooling": {"enabled": true, "maxConnections": 10, "keepAliveTimeout": 30000}, "requestDeduplication": {"enabled": true, "timeWindow": 5000, "hashFunction": "sha256"}}}