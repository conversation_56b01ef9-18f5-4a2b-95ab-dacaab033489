[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Reproduire les capacités de Task-Master avec GitHub Copilot, Curs<PERSON> et Claude-Code DESCRIPTION:Adapter les 8 prompts principaux de Task-Master pour fonctionner avec GitHub Copilot, <PERSON>urs<PERSON> et Claude-<PERSON>, en exploitant le serveur MCP Perplexity pour les capacités de recherche
--[/] NAME:Phase 1 : Préparation et analyse DESCRIPTION:Analyser les prompts existants et préparer l'infrastructure pour les adaptations
---[/] NAME:Analyser la structure des prompts Task-Master DESCRIPTION:Examiner en détail les 8 prompts JSON pour comprendre leurs paramètres, variantes et logique conditionnelle
---[ ] NAME:Créer la structure de répertoires pour les adaptations DESCRIPTION:Mettre en place l'arborescence de fichiers pour GitHub Copilot (.github/), <PERSON><PERSON><PERSON> (.cursor/) et Claude-<PERSON> (.claude/)
---[ ] NAME:Développer les scripts de conversion Handlebars DESCRIPTION:Créer des utilitaires pour convertir les templates Handlebars en formats compatibles avec chaque outil
---[ ] NAME:Documenter les correspondances de fonctionnalités DESCRIPTION:Créer une matrice détaillée des fonctionnalités supportées par chaque outil et les adaptations nécessaires
--[ ] NAME:Phase 2 : Adaptation GitHub Copilot DESCRIPTION:Créer les fichiers d'instructions et chatmodes pour GitHub Copilot
---[ ] NAME:Créer les fichiers d'instructions GitHub Copilot DESCRIPTION:Adapter les 8 prompts principaux en fichiers .instructions.md pour GitHub Copilot avec placeholders simples
---[ ] NAME:Développer les chatmodes GitHub Copilot DESCRIPTION:Créer des chatmodes spécialisés (.chatmode.md) pour les différentes variantes (research, complexity, etc.)
---[ ] NAME:Implémenter la gestion du contexte projet DESCRIPTION:Créer des mécanismes pour injecter le contexte projet dans les instructions GitHub Copilot
---[ ] NAME:Tester l'intégration GitHub Copilot DESCRIPTION:Valider le fonctionnement des instructions et chatmodes avec des cas d'usage réels
--[ ] NAME:Phase 3 : Adaptation Cursor DESCRIPTION:Créer les règles et modes personnalisés pour Cursor
---[ ] NAME:Créer les fichiers .cursorrules DESCRIPTION:Adapter les prompts en règles Cursor (.mdc) avec support des variables et contexte projet intégré
---[ ] NAME:Développer les modes personnalisés Cursor DESCRIPTION:Créer des modes JSON pour les différentes variantes avec gestion d'état et paramètres
---[ ] NAME:Intégrer les capacités de recherche Cursor DESCRIPTION:Exploiter l'intégration native du contexte projet et les modes de recherche avancés
---[ ] NAME:Optimiser les performances Cursor DESCRIPTION:Ajuster les prompts pour tirer parti des capacités spécifiques de Cursor (cache, indexation)
--[ ] NAME:Phase 4 : Adaptation Claude-Code DESCRIPTION:Créer les agents et commandes pour Claude-Code
---[ ] NAME:Créer les agents Claude-Code DESCRIPTION:Développer des sous-agents spécialisés pour chaque type de prompt avec support complet des variantes
---[ ] NAME:Implémenter les commandes slash DESCRIPTION:Créer des commandes slash pour l'interaction utilisateur avec paramètres et validation
---[ ] NAME:Développer le fichier CLAUDE.md principal DESCRIPTION:Créer les instructions centrales avec logique de routage vers les agents spécialisés
---[ ] NAME:Intégrer la logique conditionnelle avancée DESCRIPTION:Implémenter le support complet des helpers Handlebars et des variantes conditionnelles
--[ ] NAME:Phase 5 : Intégration MCP Perplexity DESCRIPTION:Intégrer les capacités de recherche Perplexity dans tous les outils
---[ ] NAME:Configurer les outils MCP Perplexity DESCRIPTION:Mettre en place la configuration MCP pour accéder aux modèles Sonar (web, réflexion, approfondie)
---[ ] NAME:Adapter les prompts de recherche pour Sonar DESCRIPTION:Modifier le prompt research.json pour exploiter les capacités spécifiques des modèles Sonar
---[ ] NAME:Intégrer la recherche dans GitHub Copilot DESCRIPTION:Créer des mécanismes pour utiliser Perplexity MCP depuis les chatmodes GitHub Copilot
---[ ] NAME:Intégrer la recherche dans Cursor DESCRIPTION:Exploiter les capacités MCP de Cursor pour accéder à Perplexity dans les modes personnalisés
---[ ] NAME:Intégrer la recherche dans Claude-Code DESCRIPTION:Implémenter l'accès MCP Perplexity dans les agents Claude-Code avec routage automatique
--[ ] NAME:Phase 6 : Tests et validation DESCRIPTION:Tester toutes les adaptations et valider leur fonctionnement
---[ ] NAME:Créer les tests unitaires pour chaque adaptation DESCRIPTION:Développer des tests pour valider la conversion correcte des prompts et le fonctionnement des variantes
---[ ] NAME:Effectuer les tests d'intégration avec les outils DESCRIPTION:Tester le fonctionnement réel avec GitHub Copilot, Cursor et Claude-Code dans des scénarios concrets
---[ ] NAME:Valider l'intégration MCP Perplexity DESCRIPTION:Tester les capacités de recherche dans chaque outil et valider la qualité des résultats
---[ ] NAME:Créer la documentation utilisateur DESCRIPTION:Rédiger des guides d'utilisation pour chaque outil avec exemples et bonnes pratiques
---[ ] NAME:Optimiser les performances et corriger les bugs DESCRIPTION:Affiner les adaptations basées sur les résultats des tests et optimiser l'expérience utilisateur