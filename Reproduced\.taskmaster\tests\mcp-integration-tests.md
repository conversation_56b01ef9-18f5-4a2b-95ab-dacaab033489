# MCP Perplexity Integration Tests

## Overview
Comprehensive test suite for validating MCP Perplexity integration across GitHub Copilot, Cursor, and Claude-Code environments.

## Test Categories

### 1. MCP Server Connectivity Tests

#### Test 1.1: Basic MCP Server Connection
**Objective**: Verify MCP server starts and responds to basic requests
**Prerequisites**: 
- Perplexity API key configured
- MCP server installed
**Test Steps**:
1. Start MCP server with `npx @perplexity/mcp-server`
2. Send test ping request
3. Verify server responds with status OK
**Expected Result**: Server responds within 5 seconds with valid status
**Success Criteria**: HTTP 200 response with server metadata

#### Test 1.2: API Key Validation
**Objective**: Verify API key authentication works correctly
**Test Steps**:
1. Configure valid API key
2. Send authenticated request to perplexity_ask
3. Verify successful response
4. Test with invalid API key
5. Verify appropriate error handling
**Expected Result**: Valid key succeeds, invalid key fails gracefully
**Success Criteria**: Proper authentication flow and error messages

#### Test 1.3: Tool Availability Check
**Objective**: Verify all required MCP tools are available
**Test Steps**:
1. Query available tools from MCP server
2. Verify perplexity_ask is available
3. Verify perplexity_research is available
4. Verify perplexity_reason is available
**Expected Result**: All three tools are listed and functional
**Success Criteria**: Tools respond to capability queries

### 2. GitHub Copilot Integration Tests

#### Test 2.1: Enhanced Research Chatmode
**Objective**: Verify enhanced research chatmode works with MCP
**Test Steps**:
1. Open VS Code with GitHub Copilot enabled
2. Activate enhanced-research-mode chatmode
3. Ask: "Quick research on React 18 concurrent features"
4. Verify MCP perplexity_ask is called
5. Check response includes current information and citations
**Expected Result**: Real-time web search results with sources
**Success Criteria**: Response includes recent information not in training data

#### Test 2.2: MCP Research Instructions
**Objective**: Verify MCP-enhanced research instructions work correctly
**Test Steps**:
1. Use research instruction with MCP integration
2. Test query: "Research TypeScript 5.3 features for enterprise project"
3. Verify appropriate MCP tool selection (perplexity_research)
4. Check context integration with project information
**Expected Result**: Comprehensive research with project-specific recommendations
**Success Criteria**: Response includes current TypeScript 5.3 information with enterprise considerations

#### Test 2.3: Fallback Behavior
**Objective**: Verify graceful degradation when MCP is unavailable
**Test Steps**:
1. Disable MCP server
2. Use enhanced research chatmode
3. Verify fallback to training data knowledge
4. Check user notification about MCP unavailability
**Expected Result**: Functional research with clear limitations indicated
**Success Criteria**: User informed of offline mode, research still functional

### 3. Cursor Integration Tests

#### Test 3.1: Native MCP Integration
**Objective**: Verify Cursor's native MCP support works with Perplexity
**Test Steps**:
1. Configure Cursor with MCP Perplexity server
2. Use @research command in Cursor
3. Verify MCP tools are accessible
4. Test context-aware research with project files
**Expected Result**: Seamless MCP integration with project context
**Success Criteria**: Research results consider current project structure and dependencies

#### Test 3.2: Enhanced Cursor Rules
**Objective**: Verify MCP-enhanced Cursor rules function correctly
**Test Steps**:
1. Apply perplexity-research.cursorrules
2. Test research workflow with real project
3. Verify automatic tool selection based on query type
4. Check project context integration
**Expected Result**: Intelligent tool routing and context-aware results
**Success Criteria**: Appropriate MCP tool selected, project context included

#### Test 3.3: Task Master Mode with MCP
**Objective**: Verify enhanced task-master-mode.json works with MCP
**Test Steps**:
1. Activate task-master mode in Cursor
2. Use research sub-mode with MCP integration
3. Test different research profiles (quick, standard, deep, reasoning)
4. Verify tool mapping works correctly
**Expected Result**: Each profile uses appropriate MCP tool
**Success Criteria**: Tool selection matches profile configuration

### 4. Claude-Code Integration Tests

#### Test 4.1: MCP-Enhanced Agents
**Objective**: Verify Claude-Code agents work with MCP integration
**Test Steps**:
1. Load MCP-enhanced research agent
2. Test automatic MCP tool routing
3. Verify context enhancement with project information
4. Check result processing and formatting
**Expected Result**: Agents seamlessly integrate MCP capabilities
**Success Criteria**: Enhanced responses with real-time information

#### Test 4.2: Enhanced Slash Commands
**Objective**: Verify MCP-enhanced slash commands function correctly
**Test Steps**:
1. Test `/research-quick "Node.js 20 features"`
2. Test `/research "React state management" deep comparison`
3. Test `/research-reason "database choice" "high-traffic,budget-limited"`
4. Verify appropriate MCP tool selection for each command
**Expected Result**: Commands route to correct MCP tools with proper parameters
**Success Criteria**: Tool selection matches command intent, results are relevant

#### Test 4.3: Agent Coordination with MCP
**Objective**: Verify multiple agents can coordinate MCP usage
**Test Steps**:
1. Use task-creator-agent with MCP research
2. Use complexity-analyzer-agent with MCP reasoning
3. Test agent handoff with MCP context preservation
4. Verify no conflicts in MCP tool usage
**Expected Result**: Smooth agent coordination with shared MCP resources
**Success Criteria**: No resource conflicts, context preserved across agents

### 5. Performance and Reliability Tests

#### Test 5.1: Response Time Performance
**Objective**: Verify MCP integration doesn't significantly impact response times
**Test Steps**:
1. Measure baseline response times without MCP
2. Measure response times with MCP integration
3. Test with different query complexities
4. Compare performance across tools
**Expected Result**: MCP adds <3 seconds to response time
**Success Criteria**: Acceptable performance degradation, user experience maintained

#### Test 5.2: Rate Limiting Handling
**Objective**: Verify proper handling of API rate limits
**Test Steps**:
1. Generate requests to exceed rate limits
2. Verify graceful degradation
3. Test request queuing functionality
4. Check user notification of delays
**Expected Result**: Smooth handling of rate limits with user feedback
**Success Criteria**: No errors, clear communication to user

#### Test 5.3: Error Recovery
**Objective**: Verify robust error handling and recovery
**Test Steps**:
1. Test with network connectivity issues
2. Test with invalid API responses
3. Test with MCP server crashes
4. Verify automatic retry mechanisms
**Expected Result**: Graceful error handling with appropriate fallbacks
**Success Criteria**: System remains functional, errors are logged and reported

### 6. Quality and Accuracy Tests

#### Test 6.1: Information Currency
**Objective**: Verify MCP provides more current information than training data
**Test Steps**:
1. Research recent technology releases (post-training cutoff)
2. Compare MCP results with training data knowledge
3. Verify citations include recent sources
4. Check for current version information
**Expected Result**: MCP provides information beyond training cutoff
**Success Criteria**: Recent information with proper citations

#### Test 6.2: Source Quality and Citations
**Objective**: Verify quality of sources and proper citation format
**Test Steps**:
1. Analyze sources provided in MCP responses
2. Verify links are valid and accessible
3. Check citation format consistency
4. Evaluate source credibility and relevance
**Expected Result**: High-quality, credible sources with proper citations
**Success Criteria**: >90% of links valid, sources are authoritative

#### Test 6.3: Context Relevance
**Objective**: Verify MCP results are relevant to project context
**Test Steps**:
1. Research technology options for specific project type
2. Verify recommendations consider project constraints
3. Check for technology stack compatibility
4. Evaluate implementation guidance relevance
**Expected Result**: Highly relevant, context-aware recommendations
**Success Criteria**: Recommendations align with project requirements

### 7. Integration Workflow Tests

#### Test 7.1: End-to-End Research Workflow
**Objective**: Verify complete research workflow across tools
**Test Steps**:
1. Start research in GitHub Copilot chatmode
2. Continue analysis in Cursor with enhanced rules
3. Create tasks in Claude-Code based on research
4. Verify context preservation across tools
**Expected Result**: Seamless workflow with consistent context
**Success Criteria**: Information flows correctly between tools

#### Test 7.2: Multi-Tool Comparison
**Objective**: Compare MCP integration effectiveness across tools
**Test Steps**:
1. Perform same research query in all three tools
2. Compare response quality and relevance
3. Evaluate user experience and interface
4. Assess tool-specific advantages
**Expected Result**: Each tool leverages MCP effectively with unique strengths
**Success Criteria**: All tools provide value-added MCP integration

#### Test 7.3: Project Lifecycle Integration
**Objective**: Verify MCP integration supports full project lifecycle
**Test Steps**:
1. Use MCP for initial technology research
2. Apply findings to task creation and planning
3. Use MCP for implementation guidance
4. Leverage MCP for troubleshooting and optimization
**Expected Result**: MCP supports all project phases effectively
**Success Criteria**: Consistent value throughout project lifecycle

## Test Execution Framework

### Automated Tests
```bash
# Run basic connectivity tests
npm run test:mcp-connectivity

# Run integration tests for each tool
npm run test:github-copilot-mcp
npm run test:cursor-mcp
npm run test:claude-code-mcp

# Run performance tests
npm run test:mcp-performance

# Run full test suite
npm run test:mcp-full
```

### Manual Test Checklist
- [ ] MCP server starts successfully
- [ ] API authentication works
- [ ] All MCP tools are available
- [ ] GitHub Copilot integration functional
- [ ] Cursor integration functional
- [ ] Claude-Code integration functional
- [ ] Error handling works correctly
- [ ] Performance is acceptable
- [ ] Information quality is high
- [ ] Context integration works
- [ ] Fallback mechanisms function
- [ ] Rate limiting is handled properly

### Success Metrics
- **Connectivity**: 100% of MCP tools accessible
- **Performance**: <3 second response time increase
- **Accuracy**: >95% of information is current and relevant
- **Reliability**: <1% error rate under normal conditions
- **User Experience**: Seamless integration with existing workflows

### Failure Criteria
- MCP server fails to start or respond
- API authentication consistently fails
- Response times exceed 10 seconds
- Error rate exceeds 5%
- Information quality is poor or outdated
- Integration breaks existing tool functionality

## Test Environment Setup

### Prerequisites
- Node.js 18+ installed
- Valid Perplexity API key
- GitHub Copilot subscription
- Cursor IDE with MCP support
- Claude-Code environment configured

### Configuration Files
- `.env` with PERPLEXITY_API_KEY
- MCP server configuration
- Tool-specific integration configs
- Test data and scenarios

### Test Data
- Sample project structures
- Test queries for different scenarios
- Expected response patterns
- Performance benchmarks

This comprehensive test suite ensures the MCP Perplexity integration works reliably across all target tools and provides significant value to users through enhanced research capabilities.
