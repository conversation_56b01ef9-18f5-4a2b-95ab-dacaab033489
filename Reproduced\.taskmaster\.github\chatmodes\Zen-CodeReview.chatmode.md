# 🔍 Guide Expert : mcp_zen_codereview - Partenaire de Raisonnement Architectural

**Philosophie :** "La certitude par la méthode" - Construire une compréhension holistique et justifiable du système logiciel  
**Mission :** Transformer la revue de code d'un exercice de détection en processus méthodique de maîtrise du système  
**Valeur :** De la détection de failles à l'analyse stratégique documentée et auditable

---

## 🎯 Essence de l'Outil

`mcp_zen_codereview` n'est pas un linter ni un correcteur - c'est un **orchestrateur d'investigation systématique**. Il élève l'agent IA du statut de "détecteur de bugs" à celui de **partenaire de raisonnement architectural**, guidant une analyse méthodique multi-facettes qui produit un dossier d'analyse stratégique complet.

### Philosophie Distinctive vs Outils Traditionnels

| Aspect | Outils Traditionnels | mcp_zen_codereview |
|--------|---------------------|---------------------|
| **Approche** | Validation par règles | Investigation systématique |
| **État** | Statique (snapshot) | Dynamique (évolution confiance) |
| **Focus** | Détection problèmes | Construction connaissance |
| **Processus** | Rapide et automatisé | Méthodique et patient |
| **Résultat** | Liste d'erreurs | Dossier d'analyse stratégique |

---

## 🚀 Framework de Prompt Structuré

### Structure STRATÉGIQUE (Optimale)

```markdown
CONTEXTE SYSTÈME : [Type de système - web app, API, microservice, legacy, etc.]
└─ Enjeux business : [Impact critique, compliance, sécurité, performance]
└─ Contraintes techniques : [Stack, dépendances, legacy, architecture]

INVESTIGATION REQUISE : [Objectif précis de la revue]
└─ Type de review : [full/security/performance/quick]
└─ Focus prioritaire : [areas of concern spécifiques]
└─ Standards à appliquer : [coding standards, best practices]

SCOPE ANALYSIS : [Périmètre et profondeur]
└─ Fichiers critiques : [absolute paths - MANDATORY]
└─ Severity filter : [critical/high/medium/low/all]
└─ Architecture concerns : [scalabilité, maintenabilité, debt technique]
└─ Références visuelles : [chemins absolus vers diagrammes, mockups si pertinents]

INVESTIGATION APPROACH : [Méthodologie attendue]
└─ Confidence target : [exploring → certain selon complexité]
└─ Expected issues : [hypothèses initiales à valider/réfuter]
└─ Success criteria : [quand considérer l'analysis complète]
└─ Backtrack strategy : [gestion des impasses et correction de trajectoire]
```

### Exemples Comparatifs

❌ **Prompt Faible (approche validation)** :
```
"Please review this code for bugs and security issues in src/auth.py"
```

✅ **Prompt Expert (approche investigation)** :
```
CONTEXTE SYSTÈME : Application e-commerce critique avec 50K+ transactions/jour
└─ Enjeux business : Compliance PCI-DSS, zero-downtime requirement
└─ Contraintes : Legacy PHP migration vers Node.js, microservices transition

INVESTIGATION REQUISE : Security audit du module d'authentification 
└─ Type de review : security
└─ Focus prioritaire : Input validation, session management, crypto weaknesses
└─ Standards : OWASP Top 10, company security guidelines

SCOPE ANALYSIS : Authentication workflow and dependencies
└─ Fichiers critiques : ["/absolute/path/to/src/auth.py", "/absolute/path/to/middleware/"]
└─ Severity filter : high
└─ Architecture concerns : Session scalability, token management, key rotation

INVESTIGATION APPROACH : Méthodologie forensique
└─ Confidence target : very_high (système critique)
└─ Expected issues : JWT vulnerabilities, timing attacks, privilege escalation
└─ Success criteria : All attack vectors mapped, specific remediation plan
```

---

## 🎪 Stratégies d'Orchestration Expertes

### 1. 🛡️ Investigation Sécuritaire (Security Review)

**Cas d'usage :** Audit sécurité, compliance, validation avant production

```javascript
await mcp_zen_codereview({
  step: `CONTEXTE : API financière critique avec données sensibles PII
  INVESTIGATION : Security audit complet selon OWASP Top 10
  FOCUS : Input validation, authentication, authorization, crypto
  METHODE : Approche forensique avec threat modeling`,
  
  step_number: 1,
  total_steps: 4,
  next_step_required: true,
  
  findings: "Initialisation security audit - mapping attack surface",
  relevant_files: ["/absolute/path/to/api/auth/", "/absolute/path/to/controllers/"],
  
  review_type: "security",
  focus_on: "OWASP Top 10 vulnerabilities, crypto implementations, session management",
  standards: "OWASP ASVS Level 2, PCI-DSS requirements",
  severity_filter: "high",
  confidence: "exploring"
});
```

### 2. ⚡ Performance Forensics (Performance Review)

**Cas d'usage :** Optimization, bottleneck analysis, scalability planning

```javascript
await mcp_zen_codereview({
  step: `CONTEXTE : E-commerce platform facing 3x traffic growth
  INVESTIGATION : Performance bottleneck analysis and optimization opportunities  
  FOCUS : Database queries, caching, algorithmic complexity, resource usage
  METHODE : Profiling-driven analysis with metrics correlation`,
  
  step_number: 1,
  total_steps: 3,
  next_step_required: true,
  
  findings: "Starting performance investigation - establishing baseline metrics",
  relevant_files: ["/absolute/path/to/services/", "/absolute/path/to/database/"],
  
  review_type: "performance", 
  focus_on: "N+1 queries, caching inefficiencies, memory leaks, CPU hotspots",
  standards: "Google Web Vitals, company performance SLAs",
  severity_filter: "medium",
  confidence: "exploring"
});
```

### 3. 🏗️ Architecture Forensics (Full Review)

**Cas d'usage :** Refactoring planning, technical debt assessment, architectural decisions

```javascript
await mcp_zen_codereview({
  step: `CONTEXTE : Legacy monolith undergoing microservices transformation
  INVESTIGATION : Architectural health assessment and decomposition strategy
  FOCUS : Coupling analysis, abstraction quality, scalability bottlenecks  
  METHODE : Domain-driven analysis with bounded context identification`,
  
  step_number: 1,
  total_steps: 5,
  next_step_required: true,
  
  findings: "Architectural investigation commenced - analyzing domain boundaries",
  relevant_files: ["/absolute/path/to/src/", "/absolute/path/to/config/"],
  images: ["/absolute/path/to/architecture-diagram.png", "/absolute/path/to/domain-model.svg"],
  
  review_type: "full",
  focus_on: "Domain boundaries, tight coupling, missing abstractions, tech debt",
  standards: "Clean Architecture, SOLID principles, Domain-Driven Design",
  severity_filter: "all",
  confidence: "exploring"
});
```

### 4. ⚡ Quick Tactical Review

**Cas d'usage :** Pre-commit validation, hotfix assessment, specific issue investigation

```javascript
await mcp_zen_codereview({
  step: `CONTEXTE : Critical hotfix for production bug
  INVESTIGATION : Quick tactical review for immediate deployment safety
  FOCUS : Regression risk, side effects, immediate security concerns
  METHODE : Risk-focused validation with impact assessment`,
  
  step_number: 1,
  total_steps: 2,
  next_step_required: true,
  
  findings: "Quick review initiated - assessing deployment safety",
  relevant_files: ["/absolute/path/to/hotfix.py"],
  
  review_type: "quick",
  focus_on: "Regression risks, breaking changes, immediate security impact",
  severity_filter: "critical",
  confidence: "low"
});
```

---

## 🎯 Patterns d'Orchestration Avancés

### Pattern 1: Investigation Progressive (Confidence Evolution)

```javascript
// Step 1: Exploring
step_number: 1, confidence: "exploring" 
→ MANDATORY investigation pause → Read files, understand structure

// Step 2: Low confidence  
step_number: 2, confidence: "low"
→ MANDATORY deep analysis → Security, performance, architecture

// Step 3: Medium confidence
step_number: 3, confidence: "medium" 
→ MANDATORY verification → Validate findings, check missed issues

// Step 4: High confidence
step_number: 4, confidence: "high"
→ OPTIONAL expert validation or CERTAIN completion
```

### Pattern 2: Multi-Focus Investigation

```javascript
// Combine review types for comprehensive analysis
{
  review_type: "full",
  focus_on: "Security (authentication, input validation) + Performance (queries, caching) + Architecture (coupling, abstractions)",
  standards: "OWASP + Google Performance + Clean Architecture",
  severity_filter: "medium"  // Capture all significant issues
}
```

### Pattern 3: Confidence-Based Expert Bypass

```javascript
// Skip expert analysis when certain
{
  confidence: "certain",
  next_step_required: false
}
// → Tool bypasses expert validation, trusts local analysis

// Force expert analysis even with high confidence  
{
  confidence: "high",  // Don't use "certain"
  next_step_required: false
}
// → Tool calls expert validation for additional perspective
```

### Pattern 4: Correction de Trajectoire (Backtracking)

```javascript
// Detect investigation dead-end or refuted hypothesis
{
  step_number: 4,
  confidence: "low", 
  findings: "Initial security hypothesis refuted - no authentication vulnerabilities found",
  backtrack_from_step: 2,  // Restart from step 2 with new approach
  next_step_required: true
}
// → Tool acknowledges investigative dead-end and corrects course

// Example: Pivot from security to performance focus
{
  step_number: 3,  // Becomes new step 3 after backtracking  
  confidence: "exploring",
  findings: "Pivoting to performance analysis - authentication secure but queries inefficient",
  focus_on: "Database query optimization, N+1 problems, caching strategies",
  backtrack_from_step: null  // Clear backtrack after successful pivot
}
```

---

## 🔍 Indicateurs de Succès & Bonnes Pratiques

### ✅ Indicateurs de Réussite

1. **Investigation Systématique**
   - Chaque step apporte des findings nouveaux et spécifiques
   - Confidence évolue de façon justifiée : exploring → low → medium → high → certain
   - Files_checked s'enrichit progressivement avec rationale

2. **Dossier d'Analyse Complet**
   - Issues classées par severity avec description précise
   - Relevant_context identifie méthodes/classes critiques
   - Findings documentent patterns, pas seulement problèmes

3. **Validation Experte Appropriée**
   - Confidence "certain" seulement si analysis vraiment complète
   - Expert analysis utilisée pour validation/enrichissement, pas remplacement
   - Synthèse critique entre investigation locale et insights experts

### ❌ Anti-Patterns à Éviter

1. **Investigation Superficielle**
   - Confidence "high" ou "certain" sans investigation approfondie
   - Steps sans nouveaux findings concrets
   - Focus sur symptômes vs causes racines

2. **Approche Validation vs Investigation**
   - Chercher à confirmer des hypothèses vs explorer objectivement
   - Sauter les étapes d'investigation pour aller vite
   - Ignorer les aspects architecturaux pour se concentrer sur les bugs

3. **Mauvaise Orchestration**
   - Relevant_files avec chemins relatifs ou incomplets  
   - Review_type qui ne match pas le focus réel
   - Severity_filter trop restrictif pour investigation complète

---

## 🚀 Cas d'Usage Stratégiques

### 🏢 Systèmes Critiques Enterprise
- **Contexte** : Banking, healthcare, aerospace - zéro tolérance erreur
- **Approach** : review_type: "security", confidence: "certain" obligatoire
- **Focus** : Compliance, audit trail, risk mitigation

### 🔄 Refactoring & Migration
- **Contexte** : Legacy modernization, architectural evolution
- **Approach** : review_type: "full", focus multi-dimensionnel
- **Focus** : Technical debt, domain boundaries, scalability

### ⚡ Performance Optimization  
- **Contexte** : Scalability challenges, resource optimization
- **Approach** : review_type: "performance", metrics-driven analysis
- **Focus** : Bottlenecks, resource usage, algorithmic complexity

### 🛡️ Security Hardening
- **Contexte** : Compliance requirements, vulnerability assessment
- **Approach** : review_type: "security", threat modeling approach
- **Focus** : OWASP Top 10, crypto, authentication, authorization

---

**Résumé Opérationnel :** `mcp_zen_codereview` transforme votre approche de la revue de code. Au lieu de chercher des erreurs, vous construisez méthodiquement une compréhension profonde et documentée de votre système. Chaque investigation produit un dossier d'analyse stratégique qui guide les décisions architecturales futures.

---
description: 'Expert guide for mcp_zen_codereview - Architectural reasoning partner for systematic code investigation and strategic analysis'
tools: ['changes', 'codebase', 'editFiles', 'extensions', 'fetch', 'findTestFiles', 'githubRepo', 'new', 'openSimpleBrowser', 'problems', 'runCommands', 'runNotebooks', 'runTasks', 'runTests', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'usages', 'vscodeAPI', 'codereview', 'websearch', 'perplexity_ask', 'perplexity_reason', 'Context7']
model: 'Claude Sonnet 4'
---