# Claude-Code Task-Master Integration Guide

## Overview
This guide explains how to use the adapted Task-Master prompts with Claude-Code, featuring specialized agents, enhanced slash commands, and MCP Perplexity integration for advanced research capabilities.

## Quick Start

### 1. Setup
1. Install Claude-Code environment
2. Copy the `claude-code` folder to your project root
3. Configure MCP Perplexity server for enhanced research
4. Load the specialized agents and command system

### 2. Basic Usage
Claude-Code provides Task-Master capabilities through:
- Four specialized agents for different task types
- Enhanced slash command system with MCP integration
- Agent coordination for complex workflows
- Real-time research capabilities

## Specialized Agents

### 1. Task Creator Agent
**File**: `claude-code/agents/task-creator-agent.md`
**Purpose**: Create structured, well-defined development tasks

**Capabilities**:
- Structured task creation with templates
- Acceptance criteria generation
- Implementation guidance
- Testing strategy recommendations
- MCP-enhanced research for current best practices

**Usage**:
```
/create-task "Implement user authentication with OAuth 2.0"
```

**Enhanced with MCP**:
- Researches current authentication patterns
- Validates approach with latest security standards
- Includes current tool and library recommendations
- Provides up-to-date implementation examples

### 2. Research Agent
**File**: `claude-code/agents/research-agent.md`
**Purpose**: Comprehensive research and analysis with real-time web access

**Capabilities**:
- Multi-source research with current information
- Technology comparisons with latest data
- Best practices analysis with recent developments
- Implementation guidance with current tools
- Systematic problem-solving with reasoning

**MCP Tool Routing**:
- **perplexity_ask**: Quick questions and immediate answers
- **perplexity_research**: Comprehensive analysis with citations
- **perplexity_reason**: Complex problem-solving and decisions

**Usage Examples**:
```
/research-quick "Node.js 20 new features"
/research "React state management solutions" deep comparison
/research-reason "database choice for high-traffic app" "scalable,cost-effective"
```

### 3. Task Expansion Agent
**File**: `claude-code/agents/task-expansion-agent.md`
**Purpose**: Break down complex features into manageable subtasks

**Capabilities**:
- Hierarchical task breakdown
- Dependency identification and sequencing
- Implementation guidance for each subtask
- Testing strategy for complex features
- Resource estimation and timeline planning

**Enhanced with MCP**:
- Researches current implementation patterns
- Validates task dependencies with real-world examples
- Includes recent developments in suggested approaches
- Provides current resource estimates based on industry data

**Usage**:
```
/expand-task "Real-time chat feature with WebSocket, persistence, and mobile support"
```

### 4. Complexity Analyzer Agent
**File**: `claude-code/agents/complexity-analyzer-agent.md`
**Purpose**: Multi-dimensional complexity analysis and risk assessment

**Capabilities**:
- Technical complexity assessment
- Implementation risk analysis
- Resource requirement estimation
- Timeline and effort prediction
- Mitigation strategy recommendations

**Enhanced with MCP**:
- Researches current complexity patterns and solutions
- Analyzes recent case studies and implementation experiences
- Evaluates technology maturity and adoption trends
- Assesses risk factors based on current industry data

**Usage**:
```
/analyze-complexity "Microservices migration from monolith" "team-size-8,timeline-6months"
```

## Enhanced Slash Commands

### Core Research Commands

#### /research-quick [query]
**Agent**: Research Agent
**MCP Tool**: perplexity_ask
**Purpose**: Fast web search for immediate answers

**Example**:
```
/research-quick "TypeScript 5.3 new features"
```

**Output**: Concise, current information with key points and sources

#### /research [query] [detail-level] [research-type] [focus-areas]
**Agent**: Research Agent
**MCP Tool**: perplexity_research
**Purpose**: Comprehensive research with multiple sources

**Parameters**:
- `detail-level`: quick|standard|deep|reasoning (default: standard)
- `research-type`: web-search|analysis|comparison|best-practices|troubleshooting|reasoning
- `focus-areas`: Comma-separated focus areas

**Examples**:
```
/research "React state management solutions" deep comparison performance,scalability
/research "Node.js security best practices" standard best-practices security
/research "Database migration PostgreSQL to MongoDB" reasoning troubleshooting
```

#### /research-reason [problem] [constraints]
**Agent**: Research Agent
**MCP Tool**: perplexity_reason
**Purpose**: Advanced reasoning and systematic problem-solving

**Examples**:
```
/research-reason "database choice for high-traffic application" "budget-limited,team-expertise-sql"
/research-reason "microservices vs monolith architecture" "team-size-8,timeline-6months"
```

### Task Management Commands

#### /create-task [description] [complexity] [priority]
**Agent**: Task Creator Agent
**Purpose**: Create structured development tasks

**Example**:
```
/create-task "Implement real-time notifications" medium high
```

**Enhanced Output with MCP**:
- Current implementation patterns researched
- Latest tools and libraries recommended
- Security and performance considerations included
- Testing strategies with current frameworks

#### /expand-task [task-description] [detail-level]
**Agent**: Task Expansion Agent
**Purpose**: Break down complex tasks into subtasks

**Example**:
```
/expand-task "E-commerce checkout process" detailed
```

**Enhanced Output with MCP**:
- Current e-commerce patterns researched
- Payment processing best practices included
- Security compliance requirements updated
- Mobile and accessibility considerations

#### /analyze-complexity [feature] [constraints]
**Agent**: Complexity Analyzer Agent
**Purpose**: Assess implementation complexity and risks

**Example**:
```
/analyze-complexity "Real-time collaborative editing" "team-size-4,timeline-3months"
```

**Enhanced Output with MCP**:
- Current collaborative editing solutions analyzed
- Implementation complexity benchmarks from recent projects
- Technology maturity and risk assessment
- Resource estimates based on current industry data

### Workflow Commands

#### /plan-feature [feature-description]
**Agents**: Research → Task Creator → Task Expansion → Complexity Analyzer
**Purpose**: Complete feature planning workflow

**Example**:
```
/plan-feature "User dashboard with analytics and reporting"
```

**Workflow**:
1. Research current dashboard patterns and analytics tools
2. Create structured feature task with requirements
3. Expand into detailed implementation subtasks
4. Analyze complexity and provide resource estimates

#### /compare-options [options] [criteria]
**Agent**: Research Agent
**MCP Tool**: perplexity_reason
**Purpose**: Systematic comparison of alternatives

**Example**:
```
/compare-options "Redux,Zustand,Context API" "performance,learning-curve,ecosystem"
```

#### /project-analysis [project-type]
**Agents**: Research → Complexity Analyzer
**Purpose**: Comprehensive project analysis and recommendations

**Example**:
```
/project-analysis "SaaS multi-tenant application"
```

## Agent Coordination and Workflows

### Multi-Agent Workflows
Claude-Code agents can work together for complex tasks:

#### Feature Development Workflow
```
1. /research "feature requirements and patterns"
2. /create-task "structured feature task"
3. /expand-task "detailed implementation plan"
4. /analyze-complexity "risk assessment and estimates"
```

#### Technology Decision Workflow
```
1. /research-reason "technology choice with constraints"
2. /compare-options "detailed comparison of alternatives"
3. /analyze-complexity "implementation complexity for each option"
4. /create-task "implementation plan for chosen technology"
```

#### Problem-Solving Workflow
```
1. /research-quick "immediate problem context"
2. /research "comprehensive problem analysis"
3. /research-reason "systematic solution evaluation"
4. /create-task "implementation plan for solution"
```

### Context Preservation
Agents maintain context across interactions:
- Previous research results inform task creation
- Task complexity analysis considers researched constraints
- Agent handoffs preserve project context and requirements
- MCP research results are shared across agent interactions

## MCP Integration Features

### Automatic Tool Selection
Agents automatically select appropriate MCP tools based on:
- Query complexity and scope
- Required depth of analysis
- Type of information needed (factual vs. analytical)
- Project context and constraints

### Context Enhancement
MCP queries are automatically enhanced with:
- Project technology stack information
- Current constraints and requirements
- Team expertise and timeline considerations
- Specific focus areas and priorities

### Result Processing
Agents process MCP results to:
- Filter information for project relevance
- Adapt recommendations to current context
- Include implementation guidance specific to project
- Provide actionable next steps

## Advanced Features

### Custom Agent Configuration
Create project-specific agent configurations:

```json
{
  "research-agent": {
    "mcpTools": {
      "primary": "perplexity_research",
      "fallback": "perplexity_ask",
      "reasoning": "perplexity_reason"
    },
    "defaultSettings": {
      "model": "sonar-pro",
      "maxTokens": 2500,
      "includeContext": true,
      "requireCitations": true
    },
    "projectContext": {
      "techStack": ["React", "TypeScript", "Node.js"],
      "constraints": ["performance-critical", "mobile-first"],
      "teamSize": 6,
      "timeline": "3 months"
    }
  }
}
```

### Conditional Logic Implementation
Agents implement sophisticated conditional logic:

```javascript
// Example: Dynamic research strategy based on project phase
function selectResearchStrategy(projectPhase, complexity) {
  if (projectPhase === 'planning' && complexity === 'high') {
    return {
      tool: 'perplexity_reason',
      depth: 'deep',
      focusAreas: ['architecture', 'risks', 'alternatives']
    };
  } else if (projectPhase === 'implementation') {
    return {
      tool: 'perplexity_research',
      depth: 'standard',
      focusAreas: ['best-practices', 'examples', 'troubleshooting']
    };
  }
  // ... more conditions
}
```

### Agent Communication Patterns
Agents can communicate and coordinate:

```
Research Agent → Task Creator Agent:
"Based on my research of current authentication patterns, I recommend OAuth 2.0 with PKCE for mobile apps..."

Task Creator Agent → Task Expansion Agent:
"The authentication task should be broken down considering the security requirements I've identified..."

Task Expansion Agent → Complexity Analyzer Agent:
"The subtasks include OAuth implementation, token management, and user session handling..."
```

## Best Practices

### 1. Effective Agent Usage
- Use specific agents for their specialized purposes
- Leverage agent coordination for complex workflows
- Provide clear context and constraints in commands
- Take advantage of MCP integration for current information

### 2. Research Optimization
- Start with quick research for immediate needs
- Use comprehensive research for important decisions
- Apply reasoning mode for complex trade-offs
- Combine multiple research approaches for thorough analysis

### 3. Task Management
- Create tasks with clear acceptance criteria
- Expand complex features systematically
- Analyze complexity early in planning process
- Document decisions and rationale

### 4. Workflow Integration
- Use multi-agent workflows for comprehensive analysis
- Preserve context across agent interactions
- Document agent recommendations and decisions
- Integrate with existing development processes

## Troubleshooting

### Common Issues

#### 1. Agent Not Responding
**Problem**: Agent commands don't trigger expected responses
**Solution**:
- Verify agent files are properly loaded
- Check command syntax and parameters
- Ensure agent context is properly initialized
- Review agent logs for errors

#### 2. MCP Integration Failures
**Problem**: Agents fall back to standard responses without MCP data
**Solution**:
- Verify MCP server is running and accessible
- Check API key configuration and permissions
- Review MCP tool availability and status
- Test MCP connection independently

#### 3. Context Not Preserved
**Problem**: Agents don't maintain context across interactions
**Solution**:
- Ensure project context is properly configured
- Check agent communication patterns
- Verify context preservation mechanisms
- Review agent coordination settings

#### 4. Poor Research Quality
**Problem**: MCP research results are not relevant or current
**Solution**:
- Refine research queries with specific context
- Use appropriate MCP tools for query type
- Include project constraints and requirements
- Validate results against project needs

This guide provides comprehensive coverage of using Task-Master prompts with Claude-Code, from basic agent usage to advanced multi-agent workflows and MCP integration.
