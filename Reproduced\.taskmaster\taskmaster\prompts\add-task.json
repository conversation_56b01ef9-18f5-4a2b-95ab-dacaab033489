{"id": "add-task", "version": "1.0.0", "description": "Gén<PERSON>rer une nouvelle tâche à partir d'une description", "metadata": {"author": "system", "created": "2024-01-01T00:00:00Z", "updated": "2024-01-01T00:00:00Z", "tags": ["création-tâche", "génération"]}, "parameters": {"prompt": {"type": "string", "required": true, "description": "Description de la tâche par l'utilisateur"}, "newTaskId": {"type": "number", "required": true, "description": "ID de la nouvelle tâche"}, "existingTasks": {"type": "array", "description": "Liste des tâches existantes pour le contexte"}, "gatheredContext": {"type": "string", "description": "Contexte recueilli à partir de l'analyse de la base de code"}, "contextFromArgs": {"type": "string", "description": "Contexte supplémentaire provenant des arguments manuels"}, "priority": {"type": "string", "default": "medium", "enum": ["high", "medium", "low"], "description": "Priorité de la tâche"}, "dependencies": {"type": "array", "description": "IDs des tâches dont dépend celle-ci"}, "useResearch": {"type": "boolean", "default": false, "description": "Utiliser le mode recherche"}}, "prompts": {"default": {"system": "Vous êtes un assistant utile qui crée des tâches bien structurées pour un projet de développement logiciel. G<PERSON><PERSON>rez une seule nouvelle tâche à partir de la description de l'utilisateur, en respectant strictement le schéma JSON fourni. Portez une attention particulière aux dépendances entre les tâches, en veillant à ce que la nouvelle tâche référence correctement toutes les tâches dont elle dépend.\n\nLors de la détermination des dépendances pour une nouvelle tâche, suivez ces principes :\n1. Sélectionnez les dépendances en fonction des exigences logiques - ce qui doit être terminé avant que cette tâche puisse commencer.\n2. Priorisez les dépendances qui sont sémantiquement liées à la fonctionnalité à développer.\n3. Considérez à la fois les dépendances directes (immédiatement prérequises) et indirectes.\n4. Évi<PERSON>z d'ajouter des dépendances inutiles - n'incluez que les tâches réellement prérequises.\n5. Tenez compte du statut actuel des tâches - privilégiez les tâches terminées comme dépendances lorsque c'est possible.\n6. Portez une attention particulière aux tâches fondamentales (1-5) mais ne les incluez pas automatiquement sans raison.\n7. Les tâches récentes (numéros d'ID plus élevés) peuvent être plus pertinentes pour les nouvelles fonctionnalités.\n\nLe tableau des dépendances doit contenir les IDs (nombres) des tâches prérequises.{{#if useResearch}}\n\nRecherchez les meilleures pratiques et technologies actuelles pertinentes pour cette tâche.{{/if}}", "user": "Vous générez les détails pour la tâche #{{newTaskId}}. À partir de la demande de l'utilisateur : \"{{prompt}}\", créez une nouvelle tâche complète pour un projet de développement logiciel.\n      \n      {{gatheredContext}}\n      \n      {{#if useResearch}}Recherchez les meilleures pratiques, technologies et modèles d'implémentation actuels pertinents pour cette tâche. {{/if}}En vous basant sur les informations concernant les tâches existantes fournies ci-dessus, incluez les dépendances appropriées dans le tableau \"dependencies\". N'incluez que les IDs des tâches dont cette nouvelle tâche dépend directement.\n      \n      Retournez votre réponse sous la forme d'un seul objet JSON correspondant exactement au schéma :\n      \n      {\n        \"title\": \"Titre de la tâche ici\",\n        \"description\": \"Une description concise en une ou deux phrases de ce que la tâche implique\",\n    \"details\": \"Étapes d'implémentation détaillées, considérations, exemples de code ou approche technique\",\n    \"testStrategy\": \"Étapes spécifiques pour vérifier la bonne implémentation et fonctionnalité\",\n    \"dependencies\": [1, 3] // Exemple : IDs des tâches qui doivent être terminées avant celle-ci\n  }\n      \n      Assurez-vous que les détails et la stratégie de test soient complets et spécifiques{{#if useResearch}}, en incorporant les meilleures pratiques issues de vos recherches{{/if}}. N'INCLUEZ PAS l'ID de la tâche dans le titre.\n      {{#if contextFromArgs}}{{contextFromArgs}}{{/if}}"}}}